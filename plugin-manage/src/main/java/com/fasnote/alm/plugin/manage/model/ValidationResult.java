package com.fasnote.alm.plugin.manage.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 验证结果模型 用于表示各种验证操作的结果
 */
public class ValidationResult {

	/**
	 * 创建失败的验证结果
	 *
	 * @param message 失败消息
	 * @return 验证结果
	 */
	public static ValidationResult failure(String message) {
		return new ValidationResult(false, message, null);
	}
	/**
	 * 创建带错误码的失败验证结果
	 *
	 * @param message   失败消息
	 * @param errorCode 错误码
	 * @return 验证结果
	 */
	public static ValidationResult failure(String message, String errorCode) {
		return new ValidationResult(false, message, errorCode);
	}
	/**
	 * 合并多个验证结果 如果所有结果都成功，返回成功结果 如果有任何失败，返回失败结果并合并所有错误信息
	 *
	 * @param results 验证结果列表
	 * @return 合并后的验证结果
	 */
	public static ValidationResult merge(List<ValidationResult> results) {
		if (results == null || results.isEmpty()) {
			return ValidationResult.success("无验证结果");
		}

		List<String> errorMessages = new ArrayList<>();
		List<String> allDetails = new ArrayList<>();
		String firstErrorCode = null;

		for (ValidationResult result : results) {
			if (result.isFailure()) {
				errorMessages.add(result.getMessage());
				allDetails.addAll(result.getDetails());

				if (firstErrorCode == null && result.getErrorCode() != null) {
					firstErrorCode = result.getErrorCode();
				}
			}
		}

		if (errorMessages.isEmpty()) {
			return ValidationResult.success("所有验证通过").addDetails(allDetails);
		} else {
			String combinedMessage = String.join("; ", errorMessages);
			return ValidationResult.failure(combinedMessage, firstErrorCode).addDetails(allDetails);
		}
	}
	/**
	 * 创建成功的验证结果
	 *
	 * @param message 成功消息
	 * @return 验证结果
	 */
	public static ValidationResult success(String message) {
		return new ValidationResult(true, message, null);
	}
	private final boolean valid;

	private final String message;

	private final String errorCode;

	private final LocalDateTime timestamp;

	private final List<String> details;

	/**
	 * 私有构造函数
	 */
	private ValidationResult(boolean valid, String message, String errorCode) {
		this.valid = valid;
		this.message = message;
		this.errorCode = errorCode;
		this.timestamp = LocalDateTime.now();
		this.details = new ArrayList<>();
	}

	/**
	 * 添加详细信息
	 *
	 * @param detail 详细信息
	 * @return 当前对象（支持链式调用）
	 */
	public ValidationResult addDetail(String detail) {
		if (detail != null && !detail.trim().isEmpty()) {
			this.details.add(detail);
		}
		return this;
	}

	/**
	 * 添加多个详细信息
	 *
	 * @param details 详细信息列表
	 * @return 当前对象（支持链式调用）
	 */
	public ValidationResult addDetails(List<String> details) {
		if (details != null) {
			for (String detail : details) {
				addDetail(detail);
			}
		}
		return this;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
		if (obj == null || getClass() != obj.getClass()) {
			return false;
		}

		ValidationResult that = (ValidationResult) obj;

		if ((valid != that.valid) || (message != null ? !message.equals(that.message) : that.message != null) || (errorCode != null ? !errorCode.equals(that.errorCode) : that.errorCode != null)) {
			return false;
		}

		return true;
	}

	/**
	 * 获取详细信息列表
	 *
	 * @return 详细信息列表
	 */
	public List<String> getDetails() {
		return new ArrayList<>(details);
	}

	/**
	 * 获取错误码
	 *
	 * @return 错误码
	 */
	public String getErrorCode() {
		return errorCode;
	}

	/**
	 * 获取格式化的详细信息
	 *
	 * @return 格式化的详细信息字符串
	 */
	public String getFormattedDetails() {
		if (details.isEmpty()) {
			return "";
		}

		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < details.size(); i++) {
			if (i > 0) {
				sb.append("; ");
			}
			sb.append(details.get(i));
		}
		return sb.toString();
	}

	/**
	 * 获取完整的错误信息（包含详细信息）
	 *
	 * @return 完整的错误信息
	 */
	public String getFullMessage() {
		StringBuilder sb = new StringBuilder();
		sb.append(message);

		if (hasDetails()) {
			sb.append(" [详细信息: ").append(getFormattedDetails()).append("]");
		}

		if (errorCode != null) {
			sb.append(" [错误码: ").append(errorCode).append("]");
		}

		return sb.toString();
	}

	/**
	 * 获取验证消息
	 *
	 * @return 验证消息
	 */
	public String getMessage() {
		return message;
	}

	/**
	 * 获取验证时间戳
	 *
	 * @return 时间戳
	 */
	public LocalDateTime getTimestamp() {
		return timestamp;
	}

	/**
	 * 检查是否有详细信息
	 *
	 * @return 是否有详细信息
	 */
	public boolean hasDetails() {
		return !details.isEmpty();
	}

	@Override
	public int hashCode() {
		int result = (valid ? 1 : 0);
		result = 31 * result + (message != null ? message.hashCode() : 0);
		result = 31 * result + (errorCode != null ? errorCode.hashCode() : 0);
		return result;
	}

	/**
	 * 检查验证是否失败
	 *
	 * @return 是否失败
	 */
	public boolean isFailure() {
		return !valid;
	}

	/**
	 * 检查验证是否成功（别名方法）
	 *
	 * @return 是否成功
	 */
	public boolean isSuccess() {
		return valid;
	}

	/**
	 * 检查验证是否成功
	 *
	 * @return 是否成功
	 */
	public boolean isValid() {
		return valid;
	}

	/**
	 * 合并两个验证结果
	 *
	 * @param other 另一个验证结果
	 * @return 合并后的验证结果
	 */
	public ValidationResult merge(ValidationResult other) {
		if (other == null) {
			return this;
		}

		List<ValidationResult> results = new ArrayList<>();
		results.add(this);
		results.add(other);

		return merge(results);
	}

	@Override
	public String toString() {
		return String.format("ValidationResult{valid=%s, message='%s', errorCode='%s', timestamp=%s, details=%d}",
				valid, message, errorCode, timestamp, details.size());
	}
}
