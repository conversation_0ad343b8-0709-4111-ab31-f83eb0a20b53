package com.fasnote.alm.plugin.manage.osgi;

import org.osgi.framework.Bundle;
import org.osgi.framework.BundleContext;
import org.osgi.framework.BundleEvent;
import org.osgi.framework.BundleListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IDependencyInjector;
import com.fasnote.alm.injection.facade.DI;
import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.injection.module.LicenseManagerModule;
import com.fasnote.alm.plugin.manage.injection.module.LicenseModule;

/**
 * OSGi环境下的许可证框架初始化器 解决跨Bundle的类加载和依赖注入问题
 */
public class OSGiLicenseFrameworkInitializer implements BundleListener {

	private static final Logger logger = LoggerFactory.getLogger(OSGiLicenseFrameworkInitializer.class);

	private final BundleContext bundleContext;
	private IDependencyInjector dependencyInjector;
	private LicenseModule licenseModule;

	public OSGiLicenseFrameworkInitializer(BundleContext bundleContext) {
		this.bundleContext = bundleContext;
	}

	/**
	 * Bundle事件监听
	 */
	@Override
	public void bundleChanged(BundleEvent event) {
		Bundle bundle = event.getBundle();
		String symbolicName = bundle.getSymbolicName();

		switch (event.getType()) {
		case BundleEvent.STARTED:
		case BundleEvent.RESOLVED:
			logger.debug("Bundle启动/解析: {}", symbolicName);

			// 自动注册所有启动的Bundle到DI框架
			dependencyInjector.registerBundle(bundle);
			logger.debug("动态注册Bundle到DI框架: {}", symbolicName);
			break;

		case BundleEvent.STOPPED:
		case BundleEvent.UNRESOLVED:
			logger.debug("Bundle停止/未解析: {}", symbolicName);

			// 自动注销停止的Bundle
			dependencyInjector.unregisterBundle(bundle);
			logger.debug("动态注销Bundle: {}", symbolicName);
			break;

		default:
			// 其他事件暂不处理
			break;
		}
	}

	/**
	 * 清理资源
	 */
	public void destroy() {
		if (bundleContext != null) {
			bundleContext.removeBundleListener(this);
		}
		logger.info("OSGi许可证框架初始化器已清理");
	}

	/**
	 * 获取活跃Bundle数量
	 */
	private int getActiveBundleCount() {
		int count = 0;
		Bundle[] bundles = bundleContext.getBundles();
		for (Bundle bundle : bundles) {
			if (bundle.getState() == Bundle.ACTIVE) {
				count++;
			}
		}
		return count;
	}

	/**
	 * 获取依赖注入器
	 */
	public IDependencyInjector getDependencyInjector() {
		return dependencyInjector;
	}

	/**
	 * 获取框架统计信息
	 */
	public java.util.Map<String, Object> getOSGiStatistics() {
		java.util.Map<String, Object> stats = new java.util.HashMap<>();

		stats.put("totalBundles", bundleContext.getBundles().length);
		stats.put("activeBundles", getActiveBundleCount());
		stats.put("crossBundleSupport", supportsCrossBundleInjection());
		stats.put("osgiAwareDI", dependencyInjector != null);

		if (dependencyInjector != null) {
			stats.putAll(dependencyInjector.getStatistics());
		}

		return stats;
	}

	/**
	 * 初始化OSGi感知的许可证框架
	 */
	public void initialize() {
		logger.info("初始化OSGi感知的许可证框架");

		try {
			// 1. 获取DI框架实例
			dependencyInjector = DI.getInjector();
			if (dependencyInjector == null) {
				throw new RuntimeException("依赖注入框架未初始化");
			}

			// 2. 注册相关Bundle到DI框架
			registerRelevantBundles();

			// 3. 创建并安装LicenseModule
			installLicenseModule();

			// 4. 监听Bundle生命周期事件
			bundleContext.addBundleListener(this);

			logger.info("OSGi感知的许可证框架初始化完成");

		} catch (Exception e) {
			logger.error("初始化失败", e);
			throw new RuntimeException("OSGi许可证框架初始化失败", e);
		}
	}

	/**
	 * 安装license业务模块 通过反射尝试加载LicenseServiceModule，如果存在则安装
	 * 该模块会自动扫描其组件包，发现LicenseSwitchModule等子模块
	 */
	private void installLicenseBusinessModules() {
		try {
			Class<?> serviceModuleClass = Class.forName("com.fasnote.alm.license.module.LicenseServiceModule");
			Object serviceModule = serviceModuleClass.getDeclaredConstructor().newInstance();
			if (serviceModule instanceof com.fasnote.alm.injection.api.IModule) {
				dependencyInjector.installModule((com.fasnote.alm.injection.api.IModule) serviceModule);
				logger.info("已安装license业务模块，将自动扫描子模块");
			}
		} catch (ClassNotFoundException e) {
			logger.debug("license业务模块不可用，跳过安装: {}", e.getMessage());
		} catch (Exception e) {
			logger.warn("安装license业务模块失败: {}", e.getMessage());
		}
	}

	/**
	 * 安装许可证模块
	 */
	private void installLicenseModule() {
		try {
			logger.info("开始安装许可证模块...");

			// 1. 首先安装 LicenseManagerModule，配置 LicenseManager 的依赖注入
			LicenseManagerModule licenseManagerModule = new LicenseManagerModule(bundleContext);
			dependencyInjector.installModule(licenseManagerModule);
			logger.info("已安装LicenseManagerModule到DI框架");

			// 2. 获取DI管理的 LicenseManager 实例
			LicenseManager licenseManager = dependencyInjector.getService(LicenseManager.class);
			logger.debug("从DI框架获取LicenseManager实例");

			// 3. 创建并安装 LicenseModule（使用DI管理的LicenseManager）
			licenseModule = new LicenseModule(licenseManager);
			dependencyInjector.installModule(licenseModule);
			logger.info("已安装LicenseModule到DI框架");

			// 4. 尝试安装license业务模块（如果可用）
			installLicenseBusinessModules();

		} catch (Exception e) {
			logger.error("安装许可证模块失败", e);
			throw e;
		}
	}

	/**
	 * 注册所有Bundle到DI框架 让DI框架能够扫描到所有Bundle中的类，通过包名来控制扫描范围
	 */
	private void registerRelevantBundles() {
		try {
			Bundle[] bundles = bundleContext.getBundles();

			for (Bundle bundle : bundles) {
				if (bundle.getState() == Bundle.ACTIVE || bundle.getState() == Bundle.RESOLVED) {
					dependencyInjector.registerBundle(bundle);
					logger.debug("注册Bundle到DI框架: {}", bundle.getSymbolicName());
				}
			}

			logger.info("已注册所有活跃Bundle到DI框架，总数: {}", bundles.length);

		} catch (Exception e) {
			logger.warn("注册Bundle失败", e);
		}
	}

	/**
	 * 检查是否支持跨Bundle注入
	 */
	public boolean supportsCrossBundleInjection() {
		return dependencyInjector != null && bundleContext != null;
	}
}