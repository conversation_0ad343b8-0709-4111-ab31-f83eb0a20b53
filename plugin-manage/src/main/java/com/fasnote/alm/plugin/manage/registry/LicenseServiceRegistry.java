package com.fasnote.alm.plugin.manage.registry;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 许可证服务注册中心 职责：服务注册和发现
 *
 * 功能： - 服务注册（按类型和名称） - 服务获取和解析 - 简化的服务管理
 */
public class LicenseServiceRegistry {

	private static final Logger logger = LoggerFactory.getLogger(LicenseServiceRegistry.class);

	private final Map<String, Object> servicesByName = new ConcurrentHashMap<>();
	private final Map<Class<?>, Object> servicesByType = new ConcurrentHashMap<>();

	public LicenseServiceRegistry() {
		// 默认构造函数
	}

	/**
	 * 检查模块安装状态
	 */
	public void checkModuleInstallationStatus() {
		logger.info("=== 模块安装状态检查 ===");
		logger.info("已注册服务数量: {}", servicesByType.size());
	}

	/**
	 * 创建并注入依赖的组件实例
	 */
	public <T> T createComponent(Class<T> componentClass) {
		try {
			T instance = componentClass.getDeclaredConstructor().newInstance();
			logger.info("创建组件成功: {}", componentClass.getName());
			return instance;
		} catch (Exception e) {
			logger.error("创建组件失败: {}", componentClass.getName(), e);
			return null;
		}
	}

	/**
	 * 获取依赖注入器（简化实现）
	 */
	public Object getDependencyInjector() {
		return this; // 返回自身作为简化的依赖注入器
	}

	/**
	 * 获取服务（按类型）
	 */
	@SuppressWarnings("unchecked")
	public <T> T getService(Class<T> serviceClass) {
		T service = (T) servicesByType.get(serviceClass);
		if (service != null) {
			logger.debug("获取服务成功: {}", serviceClass.getName());
		} else {
			logger.warn("无法获取服务: {}", serviceClass.getName());
		}
		return service;
	}

	/**
	 * 获取命名服务
	 */
	public <T> T getService(Class<T> serviceClass, String name) {
		// 首先尝试按名称获取
		T service = getService(name);
		if (service != null && serviceClass.isInstance(service)) {
			logger.debug("获取命名服务成功: {} (name={})", serviceClass.getName(), name);
			return service;
		}

		// 如果没有找到命名服务，尝试获取默认服务
		service = getService(serviceClass);
		if (service != null) {
			logger.debug("获取默认服务成功: {}", serviceClass.getName());
		} else {
			logger.warn("无法获取服务: {}{}", serviceClass.getName(),
					(name != null && !name.isEmpty() ? " (name=" + name + ")" : ""));
		}
		return service;
	}

	/**
	 * 获取服务（按名称）
	 */
	@SuppressWarnings("unchecked")
	public <T> T getService(String serviceName) {
		Object service = servicesByName.get(serviceName);
		if (service == null) {
			logger.warn("未找到服务: {}", serviceName);
		} else {
			logger.debug("获取服务成功: {} -> {}", serviceName, service.getClass().getName());
		}
		return (T) service;
	}

	/**
	 * 注册服务（按类型）
	 */
	public <T> void registerService(Class<T> serviceClass, T serviceInstance) {
		logger.info("注册服务: {}", serviceClass.getName());
		servicesByType.put(serviceClass, serviceInstance);
	}

	/**
	 * 注册服务（按名称）
	 */
	public <T> void registerService(String serviceName, T serviceInstance) {
		logger.info("注册服务: {}", serviceName);
		servicesByName.put(serviceName, serviceInstance);

		@SuppressWarnings("unchecked")
		Class<T> serviceClass = (Class<T>) serviceInstance.getClass();
		servicesByType.put(serviceClass, serviceInstance);
	}
}
