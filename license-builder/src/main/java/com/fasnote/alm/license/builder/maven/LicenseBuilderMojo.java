package com.fasnote.alm.license.builder.maven;

import com.fasnote.alm.license.builder.core.BuildConfiguration;
import com.fasnote.alm.license.builder.exception.BuildException;
import org.apache.maven.plugin.AbstractMojo;
import org.apache.maven.plugin.MojoExecutionException;
import org.apache.maven.plugin.MojoFailureException;
import org.apache.maven.plugins.annotations.*;
import org.apache.maven.project.MavenProject;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;
import java.util.jar.Attributes;
import java.util.jar.JarEntry;
import java.util.jar.JarOutputStream;
import java.util.jar.Manifest;

/**
 * Maven插件：许可证构建器集成
 * 
 * 将许可证构建器集成到Maven生命周期中，专注于三个核心功能：
 * 1. 类分离：将许可证注解类从JAR中分离
 * 2. 许可证加密：对许可证文件进行加密处理
 * 3. 结果校验：验证生成文件的完整性
 */
@Mojo(
    name = "license-build",
    defaultPhase = LifecyclePhase.PACKAGE,
    requiresDependencyResolution = ResolutionScope.COMPILE
)
public class LicenseBuilderMojo extends AbstractMojo {

    /**
     * Maven项目对象
     */
    @Parameter(defaultValue = "${project}", readonly = true, required = true)
    private MavenProject project;

    /**
     * 输入JAR文件路径
     * 默认使用项目构建的主JAR文件
     */
    @Parameter(property = "license.input.jar", defaultValue = "${project.build.directory}/${project.build.finalName}.jar")
    private File inputJar;

    /**
     * 输出目录
     */
    @Parameter(property = "license.output.dir", defaultValue = "${project.build.directory}/license")
    private File outputDir;

    /**
     * 构建模式
     * - license: 执行许可证生成（默认）
     * - post-build: 处理混淆后的JAR
     * 注意：tycho-integration模式请使用tycho-config goal
     */
    @Parameter(property = "license.build.mode", defaultValue = "license")
    private String buildMode;

    /**
     * 是否启用类分离
     */
    @Parameter(property = "license.separation.enabled", defaultValue = "true")
    private boolean separationEnabled;

    /**
     * 类分离策略
     */
    @Parameter(property = "license.separation.strategy", defaultValue = "AUTO")
    private String separationStrategy;

    /**
     * 是否启用加密
     */
    @Parameter(property = "license.encryption.enabled", defaultValue = "true")
    private boolean encryptionEnabled;

    /**
     * 加密算法
     */
    @Parameter(property = "license.encryption.algorithm", defaultValue = "HYBRID")
    private String encryptionAlgorithm;

    /**
     * RSA密钥路径
     */
    @Parameter(property = "license.encryption.keyPath")
    private String keyPath;

    /**
     * 是否启用验证
     */
    @Parameter(property = "license.validation.enabled", defaultValue = "true")
    private boolean validationEnabled;

    /**
     * 跳过许可证构建
     */
    @Parameter(property = "license.skip", defaultValue = "false")
    private boolean skip;

    @Override
    public void execute() throws MojoExecutionException, MojoFailureException {
        if (skip) {
            getLog().info("许可证构建已跳过");
            return;
        }

        try {
            getLog().info("开始执行许可证构建，模式: " + buildMode);

            // 验证构建模式
            if ("tycho-integration".equals(buildMode)) {
                throw new MojoExecutionException("tycho-integration模式已移除，请使用 license-builder:tycho-config goal");
            }

            // 设置构建模式系统属性
            System.setProperty("build.mode", buildMode);

            // 创建构建配置
            BuildConfiguration config = createBuildConfiguration();

            // 执行许可证构建（直接执行核心功能）
            executeLicenseProcessing(config);

            getLog().info("许可证构建完成");

        } catch (BuildException e) {
            throw new MojoExecutionException("许可证构建失败: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new MojoExecutionException("许可证构建过程中发生未知错误", e);
        }
    }

    /**
     * 创建构建配置
     */
    private BuildConfiguration createBuildConfiguration() {
        BuildConfiguration config = new BuildConfiguration();
        
        // 基本配置
        config.getInput().setSourceJar(inputJar.getAbsolutePath());
        config.getBuild().setOutputDir(outputDir.getAbsolutePath());
        config.getBuild().setWorkDir(new File(project.getBuild().getDirectory(), "license-work").getAbsolutePath());
        
        // 类分离配置
        try {
            config.getSeparation().setStrategy(separationStrategy.toUpperCase());
        } catch (IllegalArgumentException e) {
            getLog().warn("无效的分离策略: " + separationStrategy + "，使用默认策略 AUTO");
            config.getSeparation().setStrategy("AUTO");
        }
        
        // 加密配置（仅支持RSA）
        config.getEncryption().setEnabled(encryptionEnabled);
        try {
            // 映射算法名称到RSA
            String algorithmName = encryptionAlgorithm.toUpperCase();
            if ("HYBRID".equals(algorithmName) || "AES_256_GCM".equals(algorithmName) || "AES".equals(algorithmName)) {
                algorithmName = "RSA_2048";
            }
            config.getEncryption().setAlgorithm(
                BuildConfiguration.EncryptionConfig.Algorithm.valueOf(algorithmName)
            );
        } catch (IllegalArgumentException e) {
            getLog().warn("无效的加密算法: " + encryptionAlgorithm + "，使用默认算法 RSA_2048");
            config.getEncryption().setAlgorithm(BuildConfiguration.EncryptionConfig.Algorithm.RSA_2048);
        }
        
        // RSA密钥配置
        if (keyPath != null && !keyPath.trim().isEmpty()) {
            config.getEncryption().setKeyPath(keyPath.trim());
            config.getPackaging().getLicense().getMetadata().put("keyPath", keyPath.trim());
        } else {
            // 使用默认密钥路径
            String defaultKeyPath = System.getProperty("user.home") + "/opt/license";
            config.getEncryption().setKeyPath(defaultKeyPath);
            config.getPackaging().getLicense().getMetadata().put("keyPath", defaultKeyPath);
        }
        
        // 验证配置
        config.getValidation().setEnabled(validationEnabled);
        
        // 许可证打包配置
        config.getPackaging().getLicense().setFormat("JSON");
        config.getPackaging().getLicense().setOutputName("${bundle.symbolicName}-${bundle.version}.lic");
        
        return config;
    }

    /**
     * 执行许可证处理
     * 统一的JAR打包和许可证生成流程
     */
    private void executeLicenseProcessing(BuildConfiguration config) throws BuildException {
        getLog().info("开始执行许可证处理...");

        // 验证输入文件
        if (!inputJar.exists()) {
            throw new BuildException("输入JAR文件不存在: " + inputJar.getAbsolutePath());
        }

        // 创建输出目录
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }

        // 获取Bundle信息
        com.fasnote.alm.license.builder.model.BundleInfo bundleInfo = getBundleInfo();

        // 统一的JAR处理流程
        com.fasnote.alm.license.builder.model.SeparationResult separationResult = null;
        if (separationEnabled) {
            // 执行类分离
            separationResult = executeSeparation(config);

            // 生成 interface JAR（统一的JAR打包方式）
            if (separationResult != null) {
                generateInterfaceJar(bundleInfo, separationResult, config);
            }
        }

        // 生成许可证文件
        if (encryptionEnabled && separationResult != null) {
            generateLicenseFile(config, bundleInfo, separationResult);
        }

        getLog().info("许可证处理完成");
    }

    /**
     * 获取Bundle信息
     */
    private com.fasnote.alm.license.builder.model.BundleInfo getBundleInfo() throws BuildException {
        try {
            return com.fasnote.alm.license.builder.util.BundleInfoResolver.getBundleInfo();
        } catch (Exception e) {
            throw new BuildException("获取Bundle信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行类分离
     */
    private com.fasnote.alm.license.builder.model.SeparationResult executeSeparation(BuildConfiguration config) throws BuildException {
        getLog().info("执行类分离...");

        try {
            // 使用现有的类分离器
            com.fasnote.alm.license.builder.separator.ClassSeparator separator =
                new com.fasnote.alm.license.builder.separator.ClassSeparator(config);

            // 执行分离
            java.nio.file.Path sourceJarPath = inputJar.toPath();
            com.fasnote.alm.license.builder.model.SeparationResult result = separator.separate(sourceJarPath);

            getLog().info("类分离完成，分离了 " + result.getLicensedClasses().size() + " 个许可证类");
            return result;
        } catch (Exception e) {
            throw new BuildException("类分离失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成许可证文件
     * 重命名并简化职责，专注于许可证文件生成
     */
    private void generateLicenseFile(BuildConfiguration config,
                                   com.fasnote.alm.license.builder.model.BundleInfo bundleInfo,
                                   com.fasnote.alm.license.builder.model.SeparationResult separationResult) throws BuildException {
        getLog().info("生成许可证文件...");

        try {
            // 使用许可证打包器生成许可证文件
            com.fasnote.alm.license.builder.packager.LicensePackager packager =
                new com.fasnote.alm.license.builder.packager.LicensePackager(config);
            packager.packageLicense(bundleInfo, separationResult);

            getLog().info("许可证文件生成完成");
        } catch (Exception e) {
            throw new BuildException("许可证文件生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成 interface JAR 文件
     * 统一的JAR打包方法，负责生成不包含许可证类的interface JAR
     */
    private void generateInterfaceJar(com.fasnote.alm.license.builder.model.BundleInfo bundleInfo,
                                    com.fasnote.alm.license.builder.model.SeparationResult separationResult,
                                    BuildConfiguration config) throws BuildException {
        try {
            getLog().info("生成 interface JAR...");

            // 获取处理后的版本号（替换 qualifier）
            String actualVersion = com.fasnote.alm.license.builder.util.VersionUtils.getActualVersion(bundleInfo.getVersion());

            // 生成 interface JAR 文件名
            String interfaceJarName = bundleInfo.getSymbolicName() + "_" + actualVersion + "-interface.jar";
            Path interfaceJarPath = Paths.get(outputDir.getAbsolutePath()).resolve(interfaceJarName);

            // 生成 interface JAR（统一的JAR打包方式）
            createInterfaceJar(separationResult.getInterfaceClasses(), interfaceJarPath, config);

            getLog().info("interface JAR 生成完成: " + interfaceJarPath.getFileName());

        } catch (Exception e) {
            throw new BuildException("生成 interface JAR 失败: " + e.getMessage(), e);
        }
    }


    /**
     * 创建 interface JAR 文件
     * 统一的JAR创建方法，包含许可证头部信息处理
     */
    private void createInterfaceJar(Map<String, byte[]> classes, Path jarFile, BuildConfiguration config) throws IOException {
        // 确保输出目录存在
        Files.createDirectories(jarFile.getParent());

        try (JarOutputStream jos = new JarOutputStream(Files.newOutputStream(jarFile))) {
            // 首先处理 MANIFEST.MF
            boolean manifestProcessed = false;

            // 保存类文件和资源
            for (Map.Entry<String, byte[]> entry : classes.entrySet()) {
                String entryName = entry.getKey();
                byte[] entryBytes = entry.getValue();

                // 特殊处理 MANIFEST.MF
                if ("META-INF/MANIFEST.MF".equals(entryName)) {
                    entryBytes = updateManifestWithLicenseHeaders(entryBytes);
                    manifestProcessed = true;
                }

                // 创建 JAR 条目
                JarEntry jarEntry = new JarEntry(entryName);
                jos.putNextEntry(jarEntry);

                // 写入字节码
                jos.write(entryBytes);
                jos.closeEntry();
            }

            // 如果没有找到 MANIFEST.MF，创建一个新的
            if (!manifestProcessed) {
                createDefaultManifestWithLicenseHeaders(jos);
            }
        }

        getLog().debug("已保存 JAR 文件: " + jarFile);
    }

    /**
     * 更新 MANIFEST.MF 文件，添加许可证相关的头部信息
     * 用于 interface JAR 的 MANIFEST.MF 处理
     */
    private byte[] updateManifestWithLicenseHeaders(byte[] originalManifestBytes) throws IOException {
        // 解析原始 MANIFEST.MF
        Manifest originalManifest = new Manifest(new ByteArrayInputStream(originalManifestBytes));
        Attributes mainAttributes = originalManifest.getMainAttributes();

        // 从配置中获取许可证相关的头部信息
        BuildConfiguration config = new BuildConfiguration();
        BuildConfiguration.PackagingConfig.PluginConfig.ManifestConfig manifestConfig =
            config.getPackaging().getPlugin().getManifest();
        for (Map.Entry<String, String> header : manifestConfig.getCustomHeaders().entrySet()) {
            mainAttributes.putValue(header.getKey(), header.getValue());
            getLog().debug("添加许可证头部到 interface JAR: " + header.getKey() + " = " + header.getValue());
        }

        getLog().info("已添加许可证头部信息到 interface JAR 的 MANIFEST.MF");

        // 将修改后的 Manifest 转换为字节数组
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        originalManifest.write(baos);
        return baos.toByteArray();
    }

    /**
     * 创建默认的 MANIFEST.MF 文件，包含许可证头部信息
     * 用于 interface JAR 的默认 MANIFEST.MF 创建
     */
    private void createDefaultManifestWithLicenseHeaders(JarOutputStream jos) throws IOException {
        getLog().info("创建默认的 MANIFEST.MF 文件，包含许可证头部信息");

        Manifest manifest = new Manifest();
        Attributes mainAttributes = manifest.getMainAttributes();

        // 基本的 MANIFEST.MF 属性
        mainAttributes.put(Attributes.Name.MANIFEST_VERSION, "1.0");

        // 从配置中获取许可证相关的头部信息
        BuildConfiguration config = new BuildConfiguration();
        BuildConfiguration.PackagingConfig.PluginConfig.ManifestConfig manifestConfig =
            config.getPackaging().getPlugin().getManifest();
        for (Map.Entry<String, String> header : manifestConfig.getCustomHeaders().entrySet()) {
            mainAttributes.putValue(header.getKey(), header.getValue());
            getLog().debug("添加许可证头部到默认 MANIFEST.MF: " + header.getKey() + " = " + header.getValue());
        }

        // 写入 MANIFEST.MF
        JarEntry manifestEntry = new JarEntry("META-INF/MANIFEST.MF");
        jos.putNextEntry(manifestEntry);

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        manifest.write(baos);
        jos.write(baos.toByteArray());
        jos.closeEntry();
    }
}
