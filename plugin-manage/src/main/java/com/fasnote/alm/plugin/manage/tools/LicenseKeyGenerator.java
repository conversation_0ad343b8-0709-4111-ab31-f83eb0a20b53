package com.fasnote.alm.plugin.manage.tools;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.util.Base64;

public class LicenseKeyGenerator {
	private static final String ALGORITHM = "RSA";
	private static final int KEY_SIZE = 2048;

	public static String encodePrivateKey(PrivateKey privateKey) {
		return Base64.getEncoder().encodeToString(privateKey.getEncoded());
	}

	public static String encodePublicKey(PublicKey publicKey) {
		return Base64.getEncoder().encodeToString(publicKey.getEncoded());
	}

	private static String formatAsPemPrivateKey(String base64Key) {
		StringBuilder pem = new StringBuilder();
		pem.append("-----BEGIN PRIVATE KEY-----\n");
		for (int i = 0; i < base64Key.length(); i += 64) {
			int endIndex = Math.min(i + 64, base64Key.length());
			pem.append(base64Key.substring(i, endIndex)).append("\n");
		}
		pem.append("-----END PRIVATE KEY-----\n");
		return pem.toString();
	}

	private static String formatAsPemPublicKey(String base64Key) {
		StringBuilder pem = new StringBuilder();
		pem.append("-----BEGIN PUBLIC KEY-----\n");
		for (int i = 0; i < base64Key.length(); i += 64) {
			int endIndex = Math.min(i + 64, base64Key.length());
			pem.append(base64Key.substring(i, endIndex)).append("\n");
		}
		pem.append("-----END PUBLIC KEY-----\n");
		return pem.toString();
	}

	public static KeyPair generateKeyPair() throws NoSuchAlgorithmException {
		KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(ALGORITHM);
		keyPairGenerator.initialize(KEY_SIZE, new SecureRandom());
		return keyPairGenerator.generateKeyPair();
	}

	public static void main(String[] args) {
		try {
			String outputDir = args.length > 0 ? args[0] : System.getProperty("user.dir");
			File dir = new File(outputDir);
			if (!dir.exists()) {
				dir.mkdirs();
			}

			System.out.println("正在生成RSA密钥对...");

			KeyPair keyPair = generateKeyPair();
			PublicKey publicKey = keyPair.getPublic();
			PrivateKey privateKey = keyPair.getPrivate();

			String publicKeyPath = outputDir + File.separator + "license-public.key";
			savePublicKeyToPemFile(publicKey, publicKeyPath);
			System.out.println("公钥已保存到: " + publicKeyPath);

			String privateKeyPath = outputDir + File.separator + "license-private.key";
			savePrivateKeyToPemFile(privateKey, privateKeyPath);
			System.out.println("私钥已保存到: " + privateKeyPath);

			System.out.println("\n=== 配置信息 ===");
			System.out.println("公钥 (Base64格式，用于SecurityValidator):");
			System.out.println(encodePublicKey(publicKey));

			System.out.println("\n私钥 (Base64格式，用于许可证签名):");
			System.out.println(encodePrivateKey(privateKey));

		} catch (Exception e) {
			System.err.println("生成密钥对失败: " + e.getMessage());
			e.printStackTrace();
		}
	}

	public static void savePrivateKeyToPemFile(PrivateKey privateKey, String filePath) throws IOException {
		String encodedKey = encodePrivateKey(privateKey);
		String pemContent = formatAsPemPrivateKey(encodedKey);
		try (FileWriter writer = new FileWriter(filePath)) {
			writer.write(pemContent);
		}
	}

	public static void savePublicKeyToPemFile(PublicKey publicKey, String filePath) throws IOException {
		String encodedKey = encodePublicKey(publicKey);
		String pemContent = formatAsPemPublicKey(encodedKey);
		try (FileWriter writer = new FileWriter(filePath)) {
			writer.write(pemContent);
		}
	}
}
