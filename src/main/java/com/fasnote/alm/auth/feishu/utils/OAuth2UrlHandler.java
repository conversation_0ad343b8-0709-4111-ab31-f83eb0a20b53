package com.fasnote.alm.auth.feishu.utils;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;

import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.polarion.core.util.StringUtils;
import com.polarion.core.util.logging.Logger;

/**
 * 统一的 OAuth2 URL 参数处理工具类
 *
 * 整合了所有 fromUrl 参数处理逻辑，提供统一的接口用于： 1. fromUrl 参数的提取和解码 2. URL 验证和安全检查 3. SPA
 * 路由处理 4. SSO 登录重定向处理 5. 解决单点登录重复进入时的 404 问题
 */
public class OAuth2UrlHandler {

	@NotNull
	private static final Logger log = Logger.getLogger(OAuth2UrlHandler.class);

	// URL模式匹配
	private static final Pattern SSO_LOGIN_PATTERN = Pattern.compile(".*/ssoLogin/([^/?]+).*");
	private static final Pattern REFRESH_TOKEN_PATTERN = Pattern.compile(".*/refreshToken/reauth.*");
	private static final Pattern REAUTH_PATTERN = Pattern.compile(".*/reauth.*");
	private static final Pattern POLARION_URL_PATTERN = Pattern.compile("^/polarion/.*");
	private static final Pattern SPA_HASH_PATTERN = Pattern.compile("^/polarion/#/.*");

	// 路径常量
	public static final String REFRESH_TOKEN_PATH = "/polarion/refreshToken/reauth";
	public static final String REAUTH_PATH = "/polarion/reauth";
	public static final String SSO_LOGIN_PATH = "/polarion/ssoLogin";
	public static final String FROM_URL_PARAM = "fromUrl";

	/**
	 * 统一处理已认证用户的 fromUrl 重定向 解决单点登录重复进入时的 404 问题
	 *
	 * @param request HTTP 请求
	 * @return 处理后的重定向 URL，如果无需重定向则返回 null
	 */
	@Nullable
	public static String handleAuthenticatedUserRedirect(@NotNull HttpServletRequest request) {
		if (!isSsoLoginRequest(request)) {
			return null;
		}

		String targetUrl = extractTargetUrlFromSsoRequest(request);
		if (StringUtils.isEmpty(targetUrl)) {
			return null;
		}

		try {
			// 解码目标URL
			String decodedTargetUrl = URLDecoder.decode(targetUrl, "UTF-8");

			// 验证URL安全性
			if (isValidPolarionUrl(decodedTargetUrl)) {
				return processSsoRequestUri(request);
			} else {
				log.warn("Invalid target URL detected: " + decodedTargetUrl);
				return null;
			}
		} catch (UnsupportedEncodingException e) {
			log.warn("Failed to decode target URL: " + targetUrl, e);
			return null;
		}
	}

	/**
	 * 从 SSO 请求中提取目标 URL 支持两种格式： 1. 标准格式：/polarion/ssoLogin/feishu?fromUrl=xxx 2.
	 * 直接格式：/polarion/ssoLogin/feishu?http://localhost/polarion/#/project/xxx
	 */
	@Nullable
	public static String extractTargetUrlFromSsoRequest(@NotNull HttpServletRequest request) {
		// 首先尝试从 fromUrl 参数获取
		String fromUrl = request.getParameter(FROM_URL_PARAM);
		if (!StringUtils.isEmpty(fromUrl)) {
			return fromUrl;
		}

		// 然后尝试从查询字符串中解析直接URL格式
		String queryString = request.getQueryString();
		if (!StringUtils.isEmpty(queryString)) {
			// 检查是否包含完整URL（以http开头）
			if (queryString.startsWith("http")) {
				return queryString;
			}

			// 检查是否包含相对路径（以/polarion开头）
			if (queryString.startsWith("/polarion")) {
				return queryString;
			}
		}

		return null;
	}

	/**
	 * 处理 SSO 请求 URI，生成正确的重定向 URL
	 */
	@NotNull
	public static String processSsoRequestUri(@NotNull HttpServletRequest request) {
		String fromUrl = request.getParameter(FROM_URL_PARAM);

		if (!StringUtils.isEmpty(fromUrl)) {
			try {
				// 解码fromUrl参数
				String decodedFromUrl = URLDecoder.decode(fromUrl, "UTF-8");

				// 验证URL安全性
				if (isValidPolarionUrl(decodedFromUrl)) {
					String normalizedUrl = processSpaRoute(decodedFromUrl);
					log.debug("Processed fromUrl parameter: " + fromUrl + " -> " + normalizedUrl);
					return normalizedUrl;
				} else {
					log.warn("Invalid fromUrl parameter: " + decodedFromUrl + ", using default");
					return "/polarion/";
				}
			} catch (UnsupportedEncodingException e) {
				log.warn("Failed to decode fromUrl parameter: " + fromUrl, e);
				return "/polarion/";
			}
		} else {
			// 没有fromUrl参数，返回默认路径
			return "/polarion/";
		}
	}

	/**
	 * 检查是否为 SSO 登录请求
	 */
	public static boolean isSsoLoginRequest(@NotNull HttpServletRequest request) {
		return SSO_LOGIN_PATTERN.matcher(request.getRequestURI()).matches();
	}

	/**
	 * 处理SPA路由，确保正确的URL格式
	 *
	 * @param url 原始URL
	 * @return 处理后的URL
	 */
	@NotNull
	public static String processSpaRoute(@Nullable String url) {
		if (StringUtils.isEmpty(url)) {
			return "/polarion/";
		}

		// 如果是SPA路由（包含#），直接返回
		if (SPA_HASH_PATTERN.matcher(url).matches()) {
			return url;
		}

		// 确保以/polarion/开头
		if (!url.startsWith("/polarion/")) {
			if (url.startsWith("/")) {
				return "/polarion" + url;
			} else {
				return "/polarion/" + url;
			}
		}

		return url;
	}

	/**
	 * 检查是否为SSO登录URL
	 */
	public static boolean isSsoLoginUrl(@Nullable String url) {
		if (url == null)
			return false;
		return SSO_LOGIN_PATTERN.matcher(url).matches();
	}

	/**
	 * 检查是否为刷新令牌请求
	 */
	public static boolean isRefreshTokenRequest(@Nullable String url) {
		if (url == null)
			return false;
		return REFRESH_TOKEN_PATTERN.matcher(url).matches();
	}

	/**
	 * 检查是否为重新认证请求
	 */
	public static boolean isReauthRequest(@Nullable String url) {
		if (url == null)
			return false;
		return REAUTH_PATTERN.matcher(url).matches();
	}

	/**
	 * 从URL中提取fromUrl参数值
	 * 
	 * @param originalUrl 原始URL
	 * @return fromUrl参数值，如果没有则返回原始URL
	 */
	@NotNull
	public static String extractFromUrlParameter(@Nullable String originalUrl) {
		if (StringUtils.isEmpty(originalUrl)) {
			return "";
		}

		try {
			// 解析URL参数
			List<NameValuePair> params = URLEncodedUtils.parse(new URI(originalUrl), StandardCharsets.UTF_8);

			// 查找fromUrl参数
			for (NameValuePair param : params) {
				if (FROM_URL_PARAM.equals(param.getName())) {
					String fromUrl = param.getValue();
					log.debug("Extracted fromUrl parameter: " + fromUrl + " from URL: " + originalUrl);
					return StringUtils.getEmptyIfNull(fromUrl);
				}
			}
		} catch (URISyntaxException e) {
			log.debug("Failed to parse URL for fromUrl parameter: " + originalUrl, e);
		}

		// 没有找到fromUrl参数，返回空字符串
		log.debug("No fromUrl parameter found in URL: " + originalUrl);
		return "";
	}

	/**
	 * 处理请求URL，提取实际的目标URL
	 *
	 * 这个方法实现了增强的URL处理逻辑： 1. 如果是重新认证请求，返回reauth路径 2. 如果是刷新令牌请求，提取fromUrl参数 3.
	 * 如果是SSO登录请求，提取fromUrl参数 4. 否则返回原始URL
	 */
	@NotNull
	public static String processRequestUrl(@Nullable String originalUrl) {
		String url = StringUtils.getEmptyIfNull(originalUrl);

		if (isReauthRequest(url) && !isRefreshTokenRequest(url)) {
			// 普通的重新认证请求
			log.debug("Processing reauth request: " + url);
			return REAUTH_PATH;
		} else if (isRefreshTokenRequest(url)) {
			// 刷新令牌请求，提取fromUrl参数
			log.debug("Processing refresh token request: " + url);
			String fromUrl = extractFromUrlParameter(url);
			return StringUtils.isEmpty(fromUrl) ? url : fromUrl;
		} else if (isSsoLoginUrl(url)) {
			// SSO登录请求，提取fromUrl参数
			log.debug("Processing SSO login request: " + url);
			String fromUrl = extractFromUrlParameter(url);
			return StringUtils.isEmpty(fromUrl) ? "/polarion/" : fromUrl;
		} else {
			// 其他情况，返回原始URL
			return url;
		}
	}

	/**
	 * 从SSO登录URL中提取认证器ID
	 */
	@Nullable
	public static String extractAuthenticatorId(@Nullable String ssoUrl) {
		if (ssoUrl == null)
			return null;

		Matcher matcher = SSO_LOGIN_PATTERN.matcher(ssoUrl);
		if (matcher.matches()) {
			return matcher.group(1);
		}
		return null;
	}

	/**
	 * 构建带有fromUrl参数的刷新令牌URL
	 */
	@NotNull
	public static String buildRefreshTokenUrl(@NotNull String targetUrl) {
		if (StringUtils.isEmpty(targetUrl)) {
			return REFRESH_TOKEN_PATH;
		}

		try {
			// URL编码目标URL
			String encodedTarget = java.net.URLEncoder.encode(targetUrl, StandardCharsets.UTF_8.name());
			return REFRESH_TOKEN_PATH + "?" + FROM_URL_PARAM + "=" + encodedTarget;
		} catch (Exception e) {
			log.warn("Failed to encode target URL: " + targetUrl, e);
			return REFRESH_TOKEN_PATH;
		}
	}

	/**
	 * 验证URL是否为有效的Polarion内部URL
	 */
	public static boolean isValidPolarionUrl(@Nullable String url) {
		if (StringUtils.isEmpty(url)) {
			return false;
		}

		// 检查是否为Polarion路径
		return POLARION_URL_PATTERN.matcher(url).matches() || url.startsWith("polarion/") || url.equals("/")
				|| url.startsWith("#/") || // SPA路由
				url.startsWith("/redirect/"); // Polarion redirect servlet路径
	}
}
