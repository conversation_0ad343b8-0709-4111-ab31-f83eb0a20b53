<?xml version="1.0" encoding="UTF-8"?>
<plugin>
    
    <extension point="org.eclipse.ui.views">
        <view id="com.fasnote.alm.auth.feishu.view.FeishuAuthView"
              name="Feishu Authentication"
              class="com.fasnote.alm.auth.feishu.view.FeishuAuthView"/>
    </extension>
    
    <extension point="org.eclipse.ui.commands">
        <command id="com.fasnote.alm.auth.feishu.command.login"
                 name="Feishu Login"
                 defaultHandler="com.fasnote.alm.auth.feishu.handler.FeishuLoginHandler"/>
    </extension>
    
    <extension point="com.polarion.platform.security.authenticationProvider">
        <authenticationProvider class="com.fasnote.alm.auth.feishu.provider.FeishuAuthenticationProvider"/>
    </extension>
    
    <extension point="com.polarion.platform.core.service">
        <service interface="com.fasnote.alm.auth.feishu.api.IFeishuAuthService"
                 class="com.fasnote.alm.auth.feishu.service.FeishuAuthServiceImpl"/>
    </extension>

</plugin>