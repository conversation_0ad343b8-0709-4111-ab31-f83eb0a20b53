package com.fasnote.alm.license.crypto;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.license.crypto.model.LicenseMetadata;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.crypto.SecretKey;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.nio.file.Files;
import java.nio.file.Paths;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * RSA许可证加解密器 - 独立的加解密模块
 * 负责许可证的RSA加密、解密和数字签名验证
 * 集成AES加密功能用于JAR数据保护
 */
public class RSALicenseEncryption {

    private static final Logger logger = LoggerFactory.getLogger(RSALicenseEncryption.class);
    private static final String LOG_PREFIX = "[RSALicenseEncryption] ";

    private final RSAKeyManager keyManager;
    private final AESJarEncryption aesEncryption;
    
    /**
     * 构造函数 - 使用指定的密钥管理器
     */
    public RSALicenseEncryption(RSAKeyManager keyManager) {
        this.keyManager = keyManager;
        this.aesEncryption = new AESJarEncryption();
        logger.info(LOG_PREFIX + "RSA+AES混合加密系统初始化完成，密钥版本: {}", keyManager.getKeyVersion());
    }
    
    /**
     * 加密并签名许可证数据包
     */
    public EncryptedLicensePackage encryptAndSignLicensePackage(Map<String, byte[]> licensedClasses, 
                                                               Map<String, Object> metadata) throws Exception {
        
        if (!keyManager.hasPrivateKey()) {
            throw new IllegalStateException("私钥未加载，无法进行加密和签名");
        }
        
        logger.info(LOG_PREFIX + "开始加密并签名许可证数据包");
        
        // 1. 序列化和压缩数据
        byte[] serializedData = serializeClassData(licensedClasses);
        byte[] compressedData = compressData(serializedData);
        logger.debug(LOG_PREFIX + "数据压缩完成，原始大小: {}, 压缩后大小: {}",
                    serializedData.length, compressedData.length);

        // 2. AES加密
        SecretKey aesKey = aesEncryption.deriveAESKey(keyManager.getKeyVersion(), null);
        byte[] encryptedData = aesEncryption.encryptJarData(compressedData, aesKey);
        String encryptedDataBase64 = Base64.getEncoder().encodeToString(encryptedData);
        logger.debug(LOG_PREFIX + "AES加密完成，加密数据长度: {}", encryptedDataBase64.length());

        // 3. 创建加密包
        EncryptedLicensePackage encryptedPackage = new EncryptedLicensePackage();
        encryptedPackage.setEncryptedClassData(encryptedDataBase64);
        encryptedPackage.setEncryptionVersion("1.0");
        encryptedPackage.setKeyVersion(keyManager.getKeyVersion());
        encryptedPackage.setSignatureAlgorithm("SHA256withRSA");
        encryptedPackage.setLicenseMetadata(metadata);
        
        // 4. 生成contentHash和数字签名
        // 先设置加密相关字段到metadata中
        @SuppressWarnings("unchecked")
        Map<String, Object> customProperties = (Map<String, Object>) metadata.get("customProperties");
        if (customProperties == null) {
            customProperties = new HashMap<>();
            metadata.put("customProperties", customProperties);
        }
        customProperties.put("encryptionVersion", "1.0");
        customProperties.put("keyVersion", keyManager.getKeyVersion());

        // 生成contentHash（基于包含加密信息的完整metadata）
        String contentHash = generateContentHash(metadata);
        metadata.put("contentHash", contentHash);

        // 创建LicenseMetadata对象用于签名
        LicenseMetadata licenseMetadata = LicenseMetadata.fromMap(metadata);
        licenseMetadata.setEncryptionVersion("1.0");
        licenseMetadata.setKeyVersion(keyManager.getKeyVersion());

        // 调试：输出LicenseMetadata中的关键字段
        logger.info(LOG_PREFIX + "=== LicenseMetadata字段调试 ===");
        logger.info(LOG_PREFIX + "issuer: [{}]", licenseMetadata.getIssuer());
        logger.info(LOG_PREFIX + "licenseType: [{}]", licenseMetadata.getLicenseType());
        logger.info(LOG_PREFIX + "pluginId: [{}]", licenseMetadata.getPluginId());
        logger.info(LOG_PREFIX + "features: [{}]", licenseMetadata.getFeatures());
        logger.info(LOG_PREFIX + "limitations: [{}]", licenseMetadata.getLimitations());
        logger.info(LOG_PREFIX + "=== LicenseMetadata字段调试结束 ===");

        String signatureContent = licenseMetadata.createSignatureContent() + ";encryptedClassData=" + encryptedDataBase64;
        logger.info(LOG_PREFIX + "签名内容长度: {}", signatureContent.length());
        logger.info(LOG_PREFIX + "=== BUILDER端签名内容 ===");
        logger.info(LOG_PREFIX + "签名内容: {}", signatureContent);
        logger.info(LOG_PREFIX + "=== BUILDER端签名内容结束 ===");

        String digitalSignature = keyManager.generateSignature(signatureContent);
        encryptedPackage.setDigitalSignature(digitalSignature);
        
        logger.info(LOG_PREFIX + "许可证数据包加密和签名完成");
        return encryptedPackage;
    }

    /**
     * 生成内容哈希
     */
    private String generateContentHash(Map<String, Object> metadata) {
        try {
            StringBuilder content = new StringBuilder();

            // 按键排序确保一致性，排除签名和哈希字段本身
            metadata.entrySet().stream()
                    .filter(entry -> !"signature".equals(entry.getKey()) &&
                                   !"contentHash".equals(entry.getKey()) &&
                                   !"digitalSignature".equals(entry.getKey()))
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> {
                        content.append(entry.getKey()).append("=").append(entry.getValue()).append(";");
                    });

            java.security.MessageDigest md = java.security.MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(content.toString().getBytes("UTF-8"));
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "生成内容哈希失败", e);
            throw new RuntimeException("生成内容哈希失败: " + e.getMessage(), e);
        }
    }

    /**
     * 直接从许可证文件路径解密并验证（最简化接口）
     */
    public Map<String, byte[]> decryptAndVerifyLicenseFile(String licenseFilePath) throws Exception {
        logger.info(LOG_PREFIX + "开始从文件路径验证并解密许可证: {}", licenseFilePath);

        try {
            // 读取许可证文件
            String content = new String(Files.readAllBytes(Paths.get(licenseFilePath)), "UTF-8");
            logger.debug(LOG_PREFIX + "许可证文件内容长度: {}", content.length());

            // 使用Jackson解析JSON
            ObjectMapper objectMapper = new ObjectMapper();
            @SuppressWarnings("unchecked")
            Map<String, Object> licenseData = objectMapper.readValue(content, Map.class);

            return decryptAndVerifyLicenseFile(licenseData);

        } catch (IOException e) {
            logger.error(LOG_PREFIX + "读取或解析许可证文件失败: {}", licenseFilePath, e);
            throw new Exception("读取或解析许可证文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 直接从许可证文件内容解密并验证（简化接口）
     */
    public Map<String, byte[]> decryptAndVerifyLicenseFile(Map<String, Object> licenseData) throws Exception {
        logger.info(LOG_PREFIX + "开始验证并解密许可证文件");

        // 构建EncryptedLicensePackage
        EncryptedLicensePackage encryptedPackage = new EncryptedLicensePackage();

        @SuppressWarnings("unchecked")
        Map<String, Object> customProperties = (Map<String, Object>) licenseData.get("customProperties");

        encryptedPackage.setEncryptionVersion((String) customProperties.get("encryptionVersion"));
        encryptedPackage.setKeyVersion((String) customProperties.get("keyVersion"));
        encryptedPackage.setEncryptedClassData((String) licenseData.get("encryptedClassData"));
        encryptedPackage.setDigitalSignature((String) customProperties.get("digitalSignature"));
        encryptedPackage.setSignatureAlgorithm((String) customProperties.get("signatureAlgorithm"));

        // 设置用于签名验证的完整字段，排除签名相关字段
        Map<String, Object> metadataForSignature = new HashMap<>();

        // 复制所有字段，排除签名相关字段
        for (Map.Entry<String, Object> entry : licenseData.entrySet()) {
            String key = entry.getKey();
            if (!"signature".equals(key) && !"digitalSignature".equals(key)) {
                metadataForSignature.put(key, entry.getValue());
            }
        }

        // 创建不包含签名字段的customProperties
        Map<String, Object> cleanCustomProperties = new HashMap<>();
        cleanCustomProperties.put("encryptionVersion", customProperties.get("encryptionVersion"));
        cleanCustomProperties.put("keyVersion", customProperties.get("keyVersion"));
        // 不包含digitalSignature和signatureAlgorithm

        metadataForSignature.put("customProperties", cleanCustomProperties);
        encryptedPackage.setLicenseMetadata(metadataForSignature);

        return decryptAndVerifyLicensePackage(encryptedPackage);
    }

    /**
     * 解密并验证许可证数据包
     */
    public Map<String, byte[]> decryptAndVerifyLicensePackage(EncryptedLicensePackage encryptedPackage) throws Exception {
        logger.info(LOG_PREFIX + "开始验证并解密许可证数据包");
        
        // 1. 验证数字签名
        // 使用LicenseMetadata重建签名内容
        LicenseMetadata metadata = LicenseMetadata.fromMap(encryptedPackage.getLicenseMetadata());
        metadata.setEncryptionVersion(encryptedPackage.getEncryptionVersion());
        metadata.setKeyVersion(encryptedPackage.getKeyVersion());

        String signatureContent = metadata.createSignatureContent() + ";encryptedClassData=" + encryptedPackage.getEncryptedClassData();
        logger.info(LOG_PREFIX + "重建的签名内容长度: {}", signatureContent.length());
        logger.info(LOG_PREFIX + "=== CRYPTO端验证签名内容 ===");
        logger.info(LOG_PREFIX + "重建的签名内容: {}", signatureContent);
        logger.info(LOG_PREFIX + "=== CRYPTO端验证签名内容结束 ===");
        logger.info(LOG_PREFIX + "开始验证数字签名");
        
        String digitalSignature = encryptedPackage.getDigitalSignature();
        logger.info(LOG_PREFIX + "数字签名字符串长度: {}", digitalSignature.length());
        logger.info(LOG_PREFIX + "数字签名前50字符: {}", 
                   digitalSignature.substring(0, Math.min(50, digitalSignature.length())));
        
        byte[] signatureBytes = Base64.getDecoder().decode(digitalSignature);
        logger.info(LOG_PREFIX + "数字签名字节数组长度: {}", signatureBytes.length);
        logger.info(LOG_PREFIX + "使用密钥版本进行验证: {}", encryptedPackage.getKeyVersion());
        
        boolean signatureValid = keyManager.verifySignature(signatureContent, digitalSignature, 
                                                          encryptedPackage.getKeyVersion());
        logger.info(LOG_PREFIX + "数字签名验证结果: {}", signatureValid);
        
        if (!signatureValid) {
            logger.warn(LOG_PREFIX + "数字签名验证失败，许可证可能被篡改");
            logger.info(LOG_PREFIX + "验证失败的签名内容: {}", signatureContent);
            throw new SecurityException("数字签名验证失败，许可证可能被篡改");
        }
        
        logger.debug(LOG_PREFIX + "数字签名验证通过");
        
        // 2. AES解密
        String encryptedDataBase64 = encryptedPackage.getEncryptedClassData();
        byte[] encryptedData = Base64.getDecoder().decode(encryptedDataBase64);
        SecretKey aesKey = aesEncryption.deriveAESKey(encryptedPackage.getKeyVersion(), null);
        byte[] decryptedData = aesEncryption.decryptJarData(encryptedData, aesKey);
        logger.debug(LOG_PREFIX + "AES解密完成");
        
        // 3. 解压缩
        byte[] decompressedData = decompressData(decryptedData);
        logger.debug(LOG_PREFIX + "数据解压缩完成");
        
        // 4. 反序列化
        @SuppressWarnings("unchecked")
        Map<String, byte[]> licensedClasses = (Map<String, byte[]>) deserializeClassData(decompressedData);
        
        logger.info(LOG_PREFIX + "许可证数据包验证和解密完成");
        return licensedClasses;
    }
    


    
    /**
     * 序列化类数据
     */
    private byte[] serializeClassData(Map<String, byte[]> classData) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        java.io.ObjectOutputStream oos = new java.io.ObjectOutputStream(baos);
        oos.writeObject(classData);
        oos.close();
        return baos.toByteArray();
    }
    
    /**
     * 反序列化类数据
     */
    private Object deserializeClassData(byte[] data) throws Exception {
        ByteArrayInputStream bais = new ByteArrayInputStream(data);
        java.io.ObjectInputStream ois = new java.io.ObjectInputStream(bais);
        Object result = ois.readObject();
        ois.close();
        return result;
    }
    
    /**
     * 压缩数据
     */
    private byte[] compressData(byte[] data) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        GZIPOutputStream gzipOut = new GZIPOutputStream(baos);
        gzipOut.write(data);
        gzipOut.close();
        return baos.toByteArray();
    }
    
    /**
     * 解压缩数据
     */
    private byte[] decompressData(byte[] compressedData) throws Exception {
        ByteArrayInputStream bais = new ByteArrayInputStream(compressedData);
        GZIPInputStream gzipIn = new GZIPInputStream(bais);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        
        byte[] buffer = new byte[1024];
        int len;
        while ((len = gzipIn.read(buffer)) != -1) {
            baos.write(buffer, 0, len);
        }
        
        gzipIn.close();
        return baos.toByteArray();
    }
    
    /**
     * 加密许可证包数据结构
     */
    public static class EncryptedLicensePackage implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private String encryptedClassData;
        private String encryptionVersion;
        private String keyVersion;
        private String digitalSignature;
        private String signatureAlgorithm;
        private Map<String, Object> licenseMetadata;
        
        // Getters and Setters
        public String getEncryptedClassData() { return encryptedClassData; }
        public void setEncryptedClassData(String encryptedClassData) { this.encryptedClassData = encryptedClassData; }
        
        public String getEncryptionVersion() { return encryptionVersion; }
        public void setEncryptionVersion(String encryptionVersion) { this.encryptionVersion = encryptionVersion; }
        
        public String getKeyVersion() { return keyVersion; }
        public void setKeyVersion(String keyVersion) { this.keyVersion = keyVersion; }
        
        public String getDigitalSignature() { return digitalSignature; }
        public void setDigitalSignature(String digitalSignature) { this.digitalSignature = digitalSignature; }
        
        public String getSignatureAlgorithm() { return signatureAlgorithm; }
        public void setSignatureAlgorithm(String signatureAlgorithm) { this.signatureAlgorithm = signatureAlgorithm; }
        
        public Map<String, Object> getLicenseMetadata() { return licenseMetadata; }
        public void setLicenseMetadata(Map<String, Object> licenseMetadata) { this.licenseMetadata = licenseMetadata; }
    }
}
