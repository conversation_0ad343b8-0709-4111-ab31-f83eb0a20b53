package com.fasnote.alm.license.builder.maven;

import com.fasnote.alm.license.builder.core.BuildConfiguration;
import com.fasnote.alm.license.builder.exception.BuildException;
import com.fasnote.alm.license.builder.model.BundleInfo;
import com.fasnote.alm.license.builder.util.BundleInfoResolver;
import com.fasnote.alm.license.builder.validation.*;
import com.fasnote.alm.license.builder.validation.validators.ClassIntegrityValidator;
import com.fasnote.alm.license.builder.validation.validators.DependencyResolutionValidator;
import com.fasnote.alm.license.builder.validation.validators.LicenseIntegrityValidator;
import org.apache.maven.plugin.AbstractMojo;
import org.apache.maven.plugin.MojoExecutionException;
import org.apache.maven.plugin.MojoFailureException;
import org.apache.maven.plugins.annotations.*;
import org.apache.maven.project.MavenProject;

import java.io.File;
import java.util.Arrays;
import java.util.List;

/**
 * Maven插件：许可证验证器
 * 
 * 专门用于验证许可证构建结果的完整性和正确性，包括：
 * - 插件完整性验证
 * - 许可证文件完整性验证
 * - 依赖解析验证
 * - Manifest 文件验证
 * - 类加载验证
 */
@Mojo(
    name = "validate",
    defaultPhase = LifecyclePhase.VERIFY,
    requiresDependencyResolution = ResolutionScope.COMPILE
)
public class LicenseValidationMojo extends AbstractMojo {

    /**
     * Maven项目对象
     */
    @Parameter(defaultValue = "${project}", readonly = true, required = true)
    private MavenProject project;

    /**
     * 输入JAR文件路径
     */
    @Parameter(property = "validation.inputJar", defaultValue = "${project.build.directory}/${project.build.finalName}.jar")
    private File inputJar;

    /**
     * 许可证文件路径（可选，用于许可证完整性验证）
     */
    @Parameter(property = "validation.licenseFile")
    private File licenseFile;

    /**
     * 输出目录
     */
    @Parameter(property = "validation.outputDir", defaultValue = "${project.build.directory}")
    private File outputDir;

    /**
     * 插件目录
     */
    @Parameter(property = "validation.pluginDir", defaultValue = "${basedir}")
    private File pluginDir;

    /**
     * 验证检查类型列表
     */
    @Parameter(property = "validation.checks")
    private List<String> validationChecks;

    /**
     * 构建模式
     */
    @Parameter(property = "validation.buildMode", defaultValue = "all")
    private String buildMode;

    /**
     * 是否启用验证
     */
    @Parameter(property = "validation.enabled", defaultValue = "true")
    private boolean enabled;

    /**
     * 跳过验证
     */
    @Parameter(property = "validation.skip", defaultValue = "false")
    private boolean skip;

    /**
     * 详细输出
     */
    @Parameter(property = "validation.verbose", defaultValue = "false")
    private boolean verbose;

    @Override
    public void execute() throws MojoExecutionException, MojoFailureException {
        if (skip) {
            getLog().info("许可证验证已跳过");
            return;
        }

        if (!enabled) {
            getLog().info("许可证验证已禁用");
            return;
        }

        try {
            getLog().info("开始执行许可证验证...");

            // 创建构建配置
            BuildConfiguration config = createBuildConfiguration();

            // 获取Bundle信息
            BundleInfo bundleInfo = getBundleInfo();

            // 执行验证
            executeValidation(config, bundleInfo);

            getLog().info("许可证验证完成");

        } catch (BuildException e) {
            throw new MojoExecutionException("许可证验证失败: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new MojoExecutionException("许可证验证过程中发生未知错误", e);
        }
    }

    /**
     * 执行验证
     */
    private void executeValidation(BuildConfiguration config, BundleInfo bundleInfo) throws BuildException {
        getLog().info("开始验证构建结果...");

        // 创建验证器选择器并注册验证器
        ValidatorSelector selector = createValidatorSelector();

        // 创建验证上下文
        ValidationContext context = createValidationContext(config, bundleInfo);

        // 选择并执行验证器
        executeValidators(selector, bundleInfo, context);

        getLog().info("构建结果验证完成");
    }

    /**
     * 创建验证器选择器并注册验证器
     */
    private ValidatorSelector createValidatorSelector() {
        ValidatorSelector selector = new ValidatorSelector();

        // 注册所有验证器
        selector.registerValidator(new ClassIntegrityValidator());
        selector.registerValidator(new DependencyResolutionValidator());
        selector.registerValidator(new LicenseIntegrityValidator());

        if (verbose) {
            getLog().info("注册了 " + selector.getAllValidators().size() + " 个验证器");
        }

        return selector;
    }

    /**
     * 创建验证上下文
     */
    private ValidationContext createValidationContext(BuildConfiguration config, BundleInfo bundleInfo) {
        // 根据构建模式确定阶段
        BuildPhase phase = determineBuildPhase();

        // 检测插件类型
        PluginType pluginType = PluginType.detectFromBundleInfo(bundleInfo);

        return ValidationContext.builder()
                .currentPhase(phase)
                .pluginType(pluginType)
                .buildMode(buildMode)
                .configuration(config)
                .property("inputJar", inputJar != null ? inputJar.getAbsolutePath() : null)
                .property("outputDir", outputDir != null ? outputDir.getAbsolutePath() : null)
                .property("pluginDir", pluginDir != null ? pluginDir.getAbsolutePath() : null)
                .property("verbose", verbose)
                .build();
    }

    /**
     * 确定构建阶段
     */
    private BuildPhase determineBuildPhase() {
        // 根据构建模式和文件存在性判断阶段
        if (outputDir != null && outputDir.exists()) {
            return BuildPhase.POST_LICENSE_BUILD;
        } else {
            return BuildPhase.POST_OBFUSCATION;
        }
    }

    /**
     * 执行验证器
     */
    private void executeValidators(ValidatorSelector selector, BundleInfo bundleInfo,
                                 ValidationContext context) throws BuildException {

        List<PhaseAwareValidator> validators = selector.selectValidators(context);

        if (validators.isEmpty()) {
            getLog().warn("没有找到适用的验证器");
            return;
        }

        getLog().info("执行 " + validators.size() + " 个验证器...");

        boolean hasFailure = false;
        for (PhaseAwareValidator validator : validators) {
            try {
                if (verbose) {
                    getLog().info("执行验证器: " + validator.getName());
                }

                ValidationResult result = validator.validate(bundleInfo, context);

                // 输出验证结果
                logValidationResult(result);

                if (!result.isSuccess() && validator.isCritical()) {
                    hasFailure = true;
                }

            } catch (Exception e) {
                getLog().error("验证器 " + validator.getName() + " 执行失败", e);
                if (validator.isCritical()) {
                    hasFailure = true;
                }
            }
        }

        if (hasFailure) {
            throw new BuildException("验证失败，存在关键错误");
        }
    }

    /**
     * 输出验证结果
     */
    private void logValidationResult(ValidationResult result) {
        if (result.isSuccess()) {
            if (verbose || result.hasMessages()) {
                getLog().info("[" + result.getValidatorName() + "] 验证通过");
                for (String message : result.getMessages()) {
                    getLog().info("  " + message);
                }
            }
        } else {
            getLog().error("[" + result.getValidatorName() + "] 验证失败");
            for (String message : result.getMessages()) {
                getLog().error("  " + message);
            }
        }

        // 输出警告信息
        for (String warning : result.getWarnings()) {
            getLog().warn("  " + warning);
        }
    }

    /**
     * 获取Bundle信息
     */
    private BundleInfo getBundleInfo() throws BuildException {
        try {
            // 设置插件目录系统属性，供BundleInfoResolver使用
            System.setProperty("plugin.dir", pluginDir.getAbsolutePath());
            
            return BundleInfoResolver.getBundleInfo();
        } catch (Exception e) {
            throw new BuildException("获取Bundle信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建构建配置
     */
    private BuildConfiguration createBuildConfiguration() {
        BuildConfiguration config = new BuildConfiguration();

        // 构建配置
        BuildConfiguration.BuildConfig buildConfig = config.getBuild();
        buildConfig.setOutputDir(outputDir.getAbsolutePath());
        buildConfig.setWorkDir(new File(outputDir, "work").getAbsolutePath());

        // 输入配置
        BuildConfiguration.InputConfig inputConfig = config.getInput();
        inputConfig.setSourceJar(inputJar.getAbsolutePath());

        // 验证配置
        BuildConfiguration.ValidationConfig validationConfig = config.getValidation();
        validationConfig.setEnabled(enabled);
        
        // 设置验证检查类型
        if (validationChecks != null && !validationChecks.isEmpty()) {
            validationConfig.setChecks(validationChecks);
        } else {
            // 使用默认检查类型
            validationConfig.setChecks(Arrays.asList(
                "PLUGIN_COMPLETENESS",
                "LICENSE_INTEGRITY", 
                "DEPENDENCY_RESOLUTION",
                "MANIFEST_VALIDATION",
                "CLASS_LOADING"
            ));
        }

        // 日志配置
        BuildConfiguration.LoggingConfig loggingConfig = config.getLogging();
        loggingConfig.setLevel(verbose ? "DEBUG" : "INFO");

        if (verbose) {
            getLog().info("验证配置:");
            getLog().info("  输入JAR: " + inputJar.getAbsolutePath());
            getLog().info("  输出目录: " + outputDir.getAbsolutePath());
            getLog().info("  插件目录: " + pluginDir.getAbsolutePath());
            getLog().info("  验证检查: " + validationConfig.getChecks());
            getLog().info("  构建模式: " + buildMode);
        }

        return config;
    }
}
