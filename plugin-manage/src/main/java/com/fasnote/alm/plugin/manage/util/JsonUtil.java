package com.fasnote.alm.plugin.manage.util;

import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

/**
 * JSON工具类 基于Jackson库提供高性能的JSON解析和生成功能 保持原有API接口不变，内部使用Jackson实现
 */
public class JsonUtil {

	/**
	 * Jackson ObjectMapper实例，配置为容错模式
	 */
	private static final ObjectMapper OBJECT_MAPPER = createObjectMapper();

	/**
	 * 创建并配置ObjectMapper
	 */
	private static ObjectMapper createObjectMapper() {
		ObjectMapper mapper = new ObjectMapper();

		// 配置反序列化特性 - 容错处理
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		mapper.configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false);
		mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);

		// 配置序列化特性
		mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

		return mapper;
	}

	/**
	 * 从JSON字符串解析对象 支持基本类型、Map、List和复杂POJO对象反序列化
	 *
	 * @param json  JSON字符串
	 * @param clazz 目标类
	 * @return 解析后的对象
	 * @throws RuntimeException 当解析失败时
	 */
	public static <T> T fromJson(String json, Class<T> clazz) {
		if (json == null || json.trim().isEmpty()) {
			return null;
		}

		try {
			return OBJECT_MAPPER.readValue(json.trim(), clazz);
		} catch (JsonProcessingException e) {
			// 为了保持向后兼容，解析失败时返回null而不是抛出异常
			// 这与原始实现的行为一致
			return null;
		}
	}

	/**
	 * 解析JSON字符串为Map对象
	 *
	 * @param json JSON字符串
	 * @return Map对象
	 * @throws IllegalArgumentException 当JSON格式无效时
	 */
	public static Map<String, Object> parseJson(String json) {
		if (json == null || json.trim().isEmpty()) {
			return new HashMap<>();
		}

		try {
			json = json.trim();
			if (!json.startsWith("{") || !json.endsWith("}")) {
				throw new IllegalArgumentException("Invalid JSON format: must be an object");
			}

			return OBJECT_MAPPER.readValue(json, new TypeReference<Map<String, Object>>() {
			});
		} catch (JsonProcessingException e) {
			throw new IllegalArgumentException("Invalid JSON format: " + e.getMessage(), e);
		}
	}

	/**
	 * 将对象转换为JSON字符串
	 *
	 * @param obj 对象
	 * @return JSON字符串
	 * @throws RuntimeException 当序列化失败时
	 */
	public static String toJson(Object obj) {
		try {
			return OBJECT_MAPPER.writeValueAsString(obj);
		} catch (JsonProcessingException e) {
			throw new RuntimeException("Failed to serialize object to JSON: " + e.getMessage(), e);
		}
	}
}
