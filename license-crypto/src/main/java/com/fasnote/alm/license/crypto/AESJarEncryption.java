package com.fasnote.alm.license.crypto;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Arrays;

/**
 * AES JAR包加密处理器
 * 负责JAR包数据的AES加密和解密操作
 */
public class AESJarEncryption {
    
    private static final Logger logger = LoggerFactory.getLogger(AESJarEncryption.class);
    private static final String LOG_PREFIX = "[AESJarEncryption] ";
    
    private static final String AES_ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final String KEY_ALGORITHM = "AES";
    private static final int AES_KEY_LENGTH = 256;
    private static final int IV_LENGTH = 16;
    
    // 固定盐值，用于密钥派生
    private static final String SALT = "FasNote-ALM-License-Salt-2024";
    
    /**
     * 基于许可证信息派生AES密钥
     * 
     * @param keyVersion RSA密钥版本
     * @param licenseId 许可证ID（可选，可以是任何唯一标识）
     * @return AES密钥
     */
    public SecretKey deriveAESKey(String keyVersion, String licenseId) {
        try {
            logger.debug(LOG_PREFIX + "开始派生AES密钥，keyVersion: {}, licenseId: {}", keyVersion, licenseId);
            
            // 构建种子字符串
            String seed = keyVersion + (licenseId != null ? licenseId : "") + SALT;
            
            // 使用SHA-256生成密钥材料
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] keyBytes = digest.digest(seed.getBytes(StandardCharsets.UTF_8));
            
            // 创建AES密钥（使用前32字节作为256位密钥）
            SecretKey aesKey = new SecretKeySpec(keyBytes, KEY_ALGORITHM);
            
            logger.debug(LOG_PREFIX + "AES密钥派生完成");
            return aesKey;
            
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "AES密钥派生失败", e);
            throw new RuntimeException("AES密钥派生失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 使用AES加密JAR数据
     * 
     * @param jarData 原始JAR数据
     * @param secretKey AES密钥
     * @return 加密后的数据（包含IV）
     */
    public byte[] encryptJarData(byte[] jarData, SecretKey secretKey) {
        try {
            logger.debug(LOG_PREFIX + "开始AES加密JAR数据，数据大小: {} 字节", jarData.length);
            
            // 生成随机IV
            byte[] iv = generateRandomIV();
            
            // 初始化加密器
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec);
            
            // 执行加密
            byte[] encryptedData = cipher.doFinal(jarData);
            
            // 将IV和加密数据合并（IV在前16字节）
            byte[] result = new byte[IV_LENGTH + encryptedData.length];
            System.arraycopy(iv, 0, result, 0, IV_LENGTH);
            System.arraycopy(encryptedData, 0, result, IV_LENGTH, encryptedData.length);
            
            logger.debug(LOG_PREFIX + "AES加密完成，加密后大小: {} 字节", result.length);
            return result;
            
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "AES加密失败", e);
            throw new RuntimeException("AES加密失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 使用AES解密JAR数据
     * 
     * @param encryptedData 加密的数据（包含IV）
     * @param secretKey AES密钥
     * @return 解密后的JAR数据
     */
    public byte[] decryptJarData(byte[] encryptedData, SecretKey secretKey) {
        try {
            logger.debug(LOG_PREFIX + "开始AES解密JAR数据，数据大小: {} 字节", encryptedData.length);
            
            if (encryptedData.length < IV_LENGTH) {
                throw new IllegalArgumentException("加密数据长度不足，无法提取IV");
            }
            
            // 提取IV（前16字节）
            byte[] iv = new byte[IV_LENGTH];
            System.arraycopy(encryptedData, 0, iv, 0, IV_LENGTH);
            
            // 提取实际的加密数据
            byte[] actualEncryptedData = new byte[encryptedData.length - IV_LENGTH];
            System.arraycopy(encryptedData, IV_LENGTH, actualEncryptedData, 0, actualEncryptedData.length);
            
            // 初始化解密器
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec);
            
            // 执行解密
            byte[] decryptedData = cipher.doFinal(actualEncryptedData);
            
            logger.debug(LOG_PREFIX + "AES解密完成，解密后大小: {} 字节", decryptedData.length);
            return decryptedData;
            
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "AES解密失败", e);
            throw new RuntimeException("AES解密失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 生成随机IV
     */
    private byte[] generateRandomIV() {
        byte[] iv = new byte[IV_LENGTH];
        new SecureRandom().nextBytes(iv);
        return iv;
    }
    
    /**
     * 验证AES密钥的有效性
     */
    public boolean validateKey(SecretKey key) {
        if (key == null) {
            return false;
        }
        
        if (!KEY_ALGORITHM.equals(key.getAlgorithm())) {
            logger.warn(LOG_PREFIX + "密钥算法不匹配，期望: {}, 实际: {}", KEY_ALGORITHM, key.getAlgorithm());
            return false;
        }
        
        if (key.getEncoded().length * 8 != AES_KEY_LENGTH) {
            logger.warn(LOG_PREFIX + "密钥长度不匹配，期望: {} 位, 实际: {} 位", 
                       AES_KEY_LENGTH, key.getEncoded().length * 8);
            return false;
        }
        
        return true;
    }
    
    /**
     * 生成随机AES密钥（用于测试或特殊场景）
     */
    public SecretKey generateRandomKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(KEY_ALGORITHM);
            keyGenerator.init(AES_KEY_LENGTH);
            return keyGenerator.generateKey();
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "生成随机AES密钥失败", e);
            throw new RuntimeException("生成随机AES密钥失败: " + e.getMessage(), e);
        }
    }
}
