package com.fasnote.alm.license.builder.model;

import java.util.Map;

/**
 * 分离结果
 */
public class SeparationResult {
    
    private final Map<String, byte[]> interfaceClasses;    // 接口插件中的类
    private final Map<String, byte[]> licensedClasses;     // 许可证中的实现类
    private final Map<String, byte[]> resources;           // 其他资源文件
    
    public SeparationResult(Map<String, byte[]> interfaceClasses, 
                           Map<String, byte[]> licensedClasses, 
                           Map<String, byte[]> resources) {
        this.interfaceClasses = interfaceClasses;
        this.licensedClasses = licensedClasses;
        this.resources = resources;
    }
    
    public Map<String, byte[]> getInterfaceClasses() {
        return interfaceClasses;
    }
    
    public Map<String, byte[]> getLicensedClasses() {
        return licensedClasses;
    }
    
    public Map<String, byte[]> getResources() {
        return resources;
    }
    
    @Override
    public String toString() {
        return "SeparationResult{" +
                "interfaceClasses=" + interfaceClasses.size() +
                ", licensedClasses=" + licensedClasses.size() +
                ", resources=" + resources.size() +
                '}';
    }
}