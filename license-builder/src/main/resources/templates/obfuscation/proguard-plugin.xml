            <!-- ProGuard 混淆插件 -->
            <plugin>
                <groupId>com.github.wvengen</groupId>
                <artifactId>proguard-maven-plugin</artifactId>
                <version>2.6.0</version>
                <executions>
                    <execution>
                        <id>obfuscate</id>
                        <phase>package</phase>
                        <goals>
                            <goal>proguard</goal>
                        </goals>
                        <configuration>
                            <injar>${project.build.finalName}.jar</injar>
                            <outjar>${project.build.finalName}-obfuscated.jar</outjar>
                            <!-- Java 11+ 不需要显式指定 rt.jar，使用自动检测 -->
                            <options>
                                {{>obfuscation/proguard-rules.xml}}
                            </options>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 混淆后处理：清理重复的library.jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>cleanup-library-jar</id>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <!-- 创建临时目录 -->
                                <mkdir dir="${project.build.directory}/temp-cleanup"/>

                                <!-- 备份原始MANIFEST.MF -->
                                <unzip src="${project.build.directory}/${project.build.finalName}-obfuscated.jar"
                                       dest="${project.build.directory}/temp-cleanup">
                                    <patternset>
                                        <include name="META-INF/MANIFEST.MF"/>
                                    </patternset>
                                </unzip>
                                <copy file="${project.build.directory}/temp-cleanup/META-INF/MANIFEST.MF"
                                      tofile="${project.build.directory}/original-manifest.mf"/>

                                <!-- 解压混淆后的JAR -->
                                <unzip src="${project.build.directory}/${project.build.finalName}-obfuscated.jar"
                                       dest="${project.build.directory}/temp-cleanup"/>

                                <!-- 删除重复的library.jar文件 -->
                                <delete file="${project.build.directory}/temp-cleanup/library.jar"
                                        failonerror="false"/>

                                <!-- 恢复原始MANIFEST.MF -->
                                <copy file="${project.build.directory}/original-manifest.mf"
                                      tofile="${project.build.directory}/temp-cleanup/META-INF/MANIFEST.MF"
                                      overwrite="true"/>

                                <!-- 重新打包JAR，保持原始MANIFEST.MF -->
                                <jar destfile="${project.build.directory}/${project.build.finalName}-obfuscated.jar"
                                     basedir="${project.build.directory}/temp-cleanup"
                                     manifest="${project.build.directory}/original-manifest.mf"/>

                                <!-- 清理临时文件 -->
                                <delete dir="${project.build.directory}/temp-cleanup"/>
                                <delete file="${project.build.directory}/original-manifest.mf"/>

                                <echo message="已清理混淆后JAR中的重复library.jar文件，保留原始MANIFEST.MF"/>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
