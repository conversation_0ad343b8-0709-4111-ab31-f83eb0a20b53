package com.fasnote.alm.auth.feishu.utils;

import java.util.HashMap;
import java.util.Map;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.polarion.core.util.logging.Logger;

/**
 * 飞书用户信息映射器
 *
 * 将飞书API返回的用户信息映射为Polarion OAuth2标准格式 使用Gson库进行JSON解析，支持动态ID字段选择
 */
public class FeishuUserInfoMapper {

	/**
	 * 飞书API响应数据结构
	 */
	public static class FeishuApiResponse {
		public int code;
		public String msg;
		public FeishuUserData data;
	}

	/**
	 * 飞书用户数据结构
	 */
	public static class FeishuUserData {
		public String open_id;
		public String union_id;
		public String user_id;
		public String name;
		public String en_name;
		public String email;
		public String enterprise_email;
		public String avatar_url;
		public String avatar_big;
		public String avatar_middle;
		public String avatar_thumb;
		public String mobile;
		public String tenant_key;
	}

	@NotNull
	private static final Logger log = Logger.getLogger(FeishuUserInfoMapper.class);

	@NotNull
	private static final Gson gson = new Gson();

	// 系统属性键，用于配置使用哪个ID字段作为主要用户ID
	private static final String ID_FIELD_PROPERTY = "feishu.user.id.field";

	// 默认使用的ID字段
	private static final String DEFAULT_ID_FIELD = "open_id";

	// 支持的ID字段类型
	private static final String[] SUPPORTED_ID_FIELDS = { "open_id", "union_id", "user_id" };

	/**
	 * 将飞书用户信息映射为标准格式
	 *
	 * @param feishuUserInfo 飞书API返回的原始JSON字符串
	 * @return 映射后的标准格式JSON字符串
	 */
	@NotNull
	public String mapFeishuUserInfo(@NotNull String feishuUserInfo) {
		try {
			log.debug("Mapping Feishu user info: " + feishuUserInfo);

			// 使用Gson解析飞书API响应
			FeishuApiResponse response = gson.fromJson(feishuUserInfo, FeishuApiResponse.class);

			if (response == null || response.data == null) {
				log.warn("Invalid Feishu API response: missing data");
				return createErrorResponse("Invalid response format", feishuUserInfo);
			}

			if (response.code != 0) {
				log.warn("Feishu API returned error code: " + response.code + ", message: " + response.msg);
				return createErrorResponse("API error: " + response.msg, feishuUserInfo);
			}

			// 映射用户数据
			Map<String, Object> mappedData = mapUserData(response.data);

			// 转换为JSON字符串
			String result = gson.toJson(mappedData);

			log.debug("Mapped user info result: " + result);
			return result;

		} catch (JsonSyntaxException e) {
			log.error("Failed to parse Feishu JSON response", e);
			return createErrorResponse("JSON parse error", feishuUserInfo);
		} catch (Exception e) {
			log.error("Failed to map Feishu user info", e);
			return createErrorResponse("Mapping error: " + e.getMessage(), feishuUserInfo);
		}
	}

	/**
	 * 映射飞书用户数据为标准格式
	 *
	 * @param userData 飞书用户数据
	 * @return 映射后的数据
	 */
	@NotNull
	private Map<String, Object> mapUserData(@NotNull FeishuUserData userData) {
		Map<String, Object> result = new HashMap<>();

		// 获取配置的ID字段
		String idField = getConfiguredIdField();
		String userId = getUserIdByField(userData, idField);

		log.debug("Using ID field: " + idField + ", value: " + userId);

		// 映射基本字段
		result.put("id", userId);
		result.put("name", getFirstNonEmpty(userData.name, userData.en_name));
		result.put("email", getFirstNonEmpty(userData.email, userData.enterprise_email));

		// 保留所有ID字段用于调试和备用
		result.put("open_id", userData.open_id);
		result.put("union_id", userData.union_id);
		result.put("user_id", userData.user_id);

		// 其他有用字段
		result.put("avatar_url", userData.avatar_url);
		result.put("tenant_key", userData.tenant_key);

		return result;
	}

	/**
	 * 获取配置的ID字段名称
	 */
	@NotNull
	private String getConfiguredIdField() {
		String configuredField = System.getProperty(ID_FIELD_PROPERTY, DEFAULT_ID_FIELD);

		// 验证配置的字段是否支持
		for (String supportedField : SUPPORTED_ID_FIELDS) {
			if (supportedField.equals(configuredField)) {
				return configuredField;
			}
		}

		log.warn("Unsupported ID field configured: " + configuredField + ", using default: " + DEFAULT_ID_FIELD);
		return DEFAULT_ID_FIELD;
	}

	/**
	 * 根据字段名获取用户ID
	 */
	@Nullable
	private String getUserIdByField(@NotNull FeishuUserData userData, @NotNull String fieldName) {
		switch (fieldName) {
		case "open_id":
			return userData.open_id;
		case "union_id":
			return userData.union_id;
		case "user_id":
			return userData.user_id;
		default:
			log.warn("Unknown ID field: " + fieldName);
			return userData.open_id; // 回退到默认值
		}
	}

	/**
	 * 获取第一个非空值
	 */
	@Nullable
	private String getFirstNonEmpty(String... values) {
		for (String value : values) {
			if (value != null && !value.trim().isEmpty()) {
				return value;
			}
		}
		return null;
	}

	/**
	 * 创建错误响应
	 */
	@NotNull
	private String createErrorResponse(@NotNull String errorMessage, @NotNull String originalJson) {
		Map<String, Object> errorResponse = new HashMap<>();
		errorResponse.put("error", errorMessage);
		errorResponse.put("original", originalJson);
		return gson.toJson(errorResponse);
	}
}
