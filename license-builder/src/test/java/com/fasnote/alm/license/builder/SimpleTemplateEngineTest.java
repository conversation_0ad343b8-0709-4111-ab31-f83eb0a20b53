package com.fasnote.alm.license.builder;

import com.fasnote.alm.license.builder.template.TemplateEngine;
import com.fasnote.alm.license.builder.template.TemplateContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简化的模板引擎测试 - 避免使用 text blocks
 */
class SimpleTemplateEngineTest {

    private TemplateEngine templateEngine;
    private TemplateContext context;

    @BeforeEach
    void setUp() {
        templateEngine = new TemplateEngine();
        context = new TemplateContext();
    }

    @Test
    void testSimpleVariableReplacement() {
        String template = "Hello ${name}!";
        context.put("name", "World");
        
        String result = templateEngine.process(template, context);
        assertEquals("Hello World!", result);
    }

    @Test
    void testMultipleVariables() {
        String template = "${greeting} ${name}, version ${version}";
        context.put("greeting", "Hello");
        context.put("name", "User");
        context.put("version", "1.0.0");
        
        String result = templateEngine.process(template, context);
        assertEquals("Hello User, version 1.0.0", result);
    }

    @Test
    void testConditionalRendering() {
        String template = "{{#if hasFeature}}Feature is enabled{{/if}}";
        
        // 测试条件为真
        context.put("hasFeature", true);
        String result = templateEngine.process(template, context);
        assertTrue(result.contains("Feature is enabled"));
        
        // 测试条件为假
        context.put("hasFeature", false);
        result = templateEngine.process(template, context);
        assertFalse(result.contains("Feature is enabled"));
    }

    @Test
    void testConditionalWithElse() {
        String template = "{{#if isProduction}}Production mode{{else}}Development mode{{/if}}";
        
        // 测试生产模式
        context.put("isProduction", true);
        String result = templateEngine.process(template, context);
        assertTrue(result.contains("Production mode"));
        assertFalse(result.contains("Development mode"));
        
        // 测试开发模式
        context.put("isProduction", false);
        result = templateEngine.process(template, context);
        assertFalse(result.contains("Production mode"));
        assertTrue(result.contains("Development mode"));
    }

    @Test
    void testLoopRendering() {
        String template = "{{#each items}}- ${item}\n{{/each}}";
        
        context.put("items", Arrays.asList("apple", "banana", "cherry"));
        String result = templateEngine.process(template, context);
        
        assertTrue(result.contains("- apple"));
        assertTrue(result.contains("- banana"));
        assertTrue(result.contains("- cherry"));
    }

    @Test
    void testIfEqualsRendering() {
        String template = "{{#if_equals tool \"PROGUARD\"}}Using ProGuard{{/if_equals}}";
        
        context.put("tool", "PROGUARD");
        String result = templateEngine.process(template, context);
        assertTrue(result.contains("Using ProGuard"));
        
        context.put("tool", "YGUARD");
        result = templateEngine.process(template, context);
        assertFalse(result.contains("Using ProGuard"));
    }

    @Test
    void testMissingVariable() {
        String template = "Hello ${missingVar}!";
        
        String result = templateEngine.process(template, context);
        assertEquals("Hello ${missingVar}!", result); // 未定义变量保持原样
    }

    @Test
    void testEmptyTemplate() {
        String result = templateEngine.process("", context);
        assertEquals("", result);
    }

    @Test
    void testNullTemplate() {
        assertThrows(IllegalArgumentException.class, () -> {
            templateEngine.process(null, context);
        });
    }

    @Test
    void testNullContext() {
        assertThrows(IllegalArgumentException.class, () -> {
            templateEngine.process("Hello ${name}", null);
        });
    }
}
