package com.fasnote.alm.plugin.manage;

import org.osgi.framework.BundleActivator;
import org.osgi.framework.BundleContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.config.LicenseProperties;
import com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer;
import com.fasnote.alm.plugin.manage.repository.LicenseRepository;
import com.fasnote.alm.plugin.manage.repository.impl.InMemoryLicenseRepository;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;
import com.fasnote.alm.plugin.manage.service.LicenseService;
import com.fasnote.alm.plugin.manage.service.impl.LicenseServiceImpl;

/**
 * 许可证管理插件激活器 负责插件的启动和停止，初始化核心服务
 */
public class Activator implements BundleActivator {

	private static final Logger logger = LoggerFactory.getLogger(Activator.class);

	private static BundleContext context;
	private static LicenseService licenseService;
	private static LicenseRepository licenseRepository;
	private static SecurityValidator securityValidator;
	private static LicenseProperties licenseProperties;
	private static OSGiLicenseFrameworkInitializer osgiInitializer;

	/**
	 * 获取Bundle上下文
	 */
	static BundleContext getContext() {
		return context;
	}

	/**
	 * 获取许可证配置
	 */
	public static LicenseProperties getLicenseProperties() {
		return licenseProperties;
	}

	/**
	 * 获取许可证存储库
	 */
	public static LicenseRepository getLicenseRepository() {
		return licenseRepository;
	}

	/**
	 * 获取许可证服务
	 */
	public static LicenseService getLicenseService() {
		return licenseService;
	}

	/**
	 * 获取安全验证器
	 */
	public static SecurityValidator getSecurityValidator() {
		return securityValidator;
	}

	/**
	 * 清理资源
	 */
	private void cleanup() {
		logger.debug("清理资源...");

		// 清理存储库
		if (licenseRepository instanceof InMemoryLicenseRepository) {
			((InMemoryLicenseRepository) licenseRepository).clear();
		}

		logger.debug("资源清理完成");
	}

	/**
	 * 初始化核心组件
	 */
	private void initializeComponents() {
		logger.debug("初始化核心组件...");

		// 初始化安全验证器
		securityValidator = new SecurityValidator();
		try {
			securityValidator.initialize();
		} catch (Exception e) {
			logger.error("SecurityValidator 初始化失败", e);
		}

		// 初始化许可证存储库
		licenseRepository = new InMemoryLicenseRepository();

		logger.debug("核心组件初始化完成");
	}

	/**
	 * 初始化配置
	 */
	private void initializeConfiguration() {
		logger.debug("初始化许可证配置...");

		licenseProperties = new LicenseProperties();

		// 从系统属性或环境变量加载配置
		loadConfigurationFromEnvironment();

		logger.debug("许可证配置初始化完成");
	}

	/**
	 * 初始化OSGi许可证框架 这将安装许可证拦截器到DI框架中
	 */
	private void initializeOSGiLicenseFramework() {
		logger.info("开始初始化OSGi许可证框架...");

		try {
			// 创建OSGi许可证框架初始化器
			osgiInitializer = new OSGiLicenseFrameworkInitializer(context);
			logger.debug("OSGi许可证框架初始化器创建完成");

			// 初始化框架（这将安装LicenseModule和许可证拦截器）
			osgiInitializer.initialize();

			logger.info("OSGi许可证框架初始化完成 - 许可证拦截器已安装");

		} catch (Exception e) {
			logger.error("OSGi许可证框架初始化失败", e);
			// 不抛出异常，允许插件继续启动，但许可证功能可能不可用
		}
	}

	/**
	 * 初始化服务
	 */
	private void initializeServices() {
		logger.debug("初始化服务...");

		// 初始化许可证服务
		licenseService = new LicenseServiceImpl(licenseRepository, securityValidator);

		logger.debug("服务初始化完成");
	}

	/**
	 * 从环境变量加载配置
	 */
	private void loadConfigurationFromEnvironment() {
		// 存储目录配置
		String storageDir = System.getProperty("license.storage.directory");
		if (storageDir != null) {
			licenseProperties.setStorageDirectory(storageDir);
		}

		// 缓存配置
		String cacheSize = System.getProperty("license.cache.size");
		if (cacheSize != null) {
			try {
				licenseProperties.setCacheSize(Integer.parseInt(cacheSize));
			} catch (NumberFormatException e) {
				logger.warn("无效的缓存大小配置: " + cacheSize);
			}
		}

		// 安全配置
		String validationEnabled = System.getProperty("security.validation.enabled");
		if (validationEnabled != null) {
			licenseProperties.setValidationEnabled(Boolean.parseBoolean(validationEnabled));
		}

		logger.debug("从环境变量加载配置完成");
	}

	@Override
	public void start(BundleContext bundleContext) throws Exception {
		logger.info("启动许可证管理插件...");

		try {
			Activator.context = bundleContext;

			// 初始化配置
			initializeConfiguration();

			// 初始化核心组件
			initializeComponents();

			// 初始化服务
			initializeServices();

			// 初始化OSGi许可证框架（包含许可证拦截器）
			initializeOSGiLicenseFramework();

			logger.info("许可证管理插件启动成功");

		} catch (Exception e) {
			logger.error("许可证管理插件启动失败", e);
			throw e;
		}
	}

	@Override
	public void stop(BundleContext bundleContext) throws Exception {
		logger.info("停止许可证管理插件...");

		try {
			// 清理资源
			cleanup();

			// 清理OSGi许可证框架
			if (osgiInitializer != null) {
				osgiInitializer.destroy();
				osgiInitializer = null;
			}

			// 清空静态引用
			Activator.context = null;
			licenseService = null;
			licenseRepository = null;
			securityValidator = null;
			licenseProperties = null;

			logger.info("许可证管理插件停止成功");

		} catch (Exception e) {
			logger.error("许可证管理插件停止失败", e);
			throw e;
		}
	}
}
