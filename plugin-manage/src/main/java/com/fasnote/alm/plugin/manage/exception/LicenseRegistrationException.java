package com.fasnote.alm.plugin.manage.exception;

/**
 * 许可证注册异常
 */
public class LicenseRegistrationException extends LicenseException {

	private static final long serialVersionUID = 1L;

	public LicenseRegistrationException(String message, String pluginId) {
		super(message, "LICENSE_REGISTRATION_FAILED", pluginId);
	}

	public LicenseRegistrationException(String message, String pluginId, Throwable cause) {
		super(message, "LICENSE_REGISTRATION_FAILED", cause);
		setParameters(pluginId);
	}
}