package com.fasnote.alm.auth.feishu;

import java.io.IOException;
import java.io.PrintWriter;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jetbrains.annotations.NotNull;

import com.fasnote.alm.auth.feishu.utils.FeishuApiClient;
import com.fasnote.alm.auth.feishu.utils.FeishuUserInfoMapper;
import com.polarion.core.util.logging.Logger;

/**
 * 飞书用户信息获取Servlet
 * 
 * 处理路径：/polarion/oauth-feishu/userinfo
 * 
 * 功能： 1. 接收Bearer Token认证 2. 调用飞书用户信息API 3. 返回标准化的用户信息JSON 4. 支持配置化的字段映射
 */
public class FeishuUserInfoServlet extends HttpServlet {

	private static final long serialVersionUID = 1L;

	@NotNull
	public static final Logger log = Logger.getLogger(FeishuUserInfoServlet.class);

	// 飞书用户信息API端点
	@NotNull
	private static final String FEISHU_USER_INFO_API = "https://open.feishu.cn/open-apis/authen/v1/user_info";

	private final FeishuApiClient apiClient;
	private final FeishuUserInfoMapper userInfoMapper;

	public FeishuUserInfoServlet() {
		this.apiClient = new FeishuApiClient();
		this.userInfoMapper = new FeishuUserInfoMapper();
	}

	@Override
	protected void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {

		log.debug("Feishu user info request received from: " + request.getRemoteAddr());

		try {
			// 验证Authorization头 - 符合Polarion OAuth2UserProfileParser的标准实现
			String authHeader = request.getHeader("Authorization");
			if (authHeader == null || !authHeader.startsWith("Bearer ")) {
				log.warn("Missing or invalid Authorization header in Feishu user info request");
				sendErrorResponse(response, HttpServletResponse.SC_UNAUTHORIZED,
						"Missing or invalid Authorization header");
				return;
			}

			// 提取访问令牌
			String accessToken = authHeader.substring(7); // 移除"Bearer "前缀
			if (accessToken.trim().isEmpty()) {
				log.warn("Empty access token in Feishu user info request");
				sendErrorResponse(response, HttpServletResponse.SC_UNAUTHORIZED, "Empty access token");
				return;
			}

			log.debug("Processing Feishu user info request with access token: "
					+ accessToken.substring(0, Math.min(10, accessToken.length())) + "...");

			// 调用飞书API获取用户信息
			String feishuUserInfo = apiClient.getUserInfo(FEISHU_USER_INFO_API, accessToken);

			// 映射用户信息到标准格式
			String mappedUserInfo = userInfoMapper.mapFeishuUserInfo(feishuUserInfo);

			// 返回用户信息
			sendSuccessResponse(response, mappedUserInfo);

			log.debug("Feishu user info successfully returned");

		} catch (IOException e) {
			log.error("Failed to fetch user info from Feishu API", e);
			sendErrorResponse(response, HttpServletResponse.SC_BAD_GATEWAY,
					"Failed to fetch user info from Feishu: " + e.getMessage());
		} catch (Exception e) {
			log.error("Unexpected error in Feishu user info servlet", e);
			sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
					"Internal server error: " + e.getMessage());
		}
	}

	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		// 飞书用户信息API通常使用GET请求，但为了兼容性也支持POST
		doGet(request, response);
	}

	/**
	 * 发送成功响应 - 符合Polarion OAuth2UserProfileParser期望的JSON格式
	 */
	private void sendSuccessResponse(@NotNull HttpServletResponse response, @NotNull String userInfo)
			throws IOException {

		response.setStatus(HttpServletResponse.SC_OK);
		response.setContentType("application/json; charset=UTF-8");
		response.setCharacterEncoding("UTF-8");

		// 设置缓存控制头，避免敏感信息被缓存
		response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
		response.setHeader("Pragma", "no-cache");
		response.setHeader("Expires", "0");

		// 添加CORS头（如果需要）
		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
		response.setHeader("Access-Control-Allow-Headers", "Authorization, Content-Type");

		try (PrintWriter writer = response.getWriter()) {
			writer.write(userInfo);
			writer.flush();
		}

		log.debug("Sent successful response with user info");
	}

	/**
	 * 发送错误响应
	 */
	private void sendErrorResponse(@NotNull HttpServletResponse response, int statusCode, @NotNull String errorMessage)
			throws IOException {

		response.setStatus(statusCode);
		response.setContentType("application/json; charset=UTF-8");
		response.setCharacterEncoding("UTF-8");

		String errorJson = String.format("{\"error\": \"%s\", \"error_description\": \"%s\", \"status\": %d}",
				getErrorType(statusCode), errorMessage.replace("\"", "\\\""), statusCode);

		try (PrintWriter writer = response.getWriter()) {
			writer.write(errorJson);
			writer.flush();
		}

		log.warn("Feishu user info error response sent: " + statusCode + " - " + errorMessage);
	}

	/**
	 * 根据状态码获取错误类型
	 */
	@NotNull
	private String getErrorType(int statusCode) {
		switch (statusCode) {
		case HttpServletResponse.SC_UNAUTHORIZED:
			return "unauthorized";
		case HttpServletResponse.SC_BAD_REQUEST:
			return "bad_request";
		case HttpServletResponse.SC_BAD_GATEWAY:
			return "api_error";
		case HttpServletResponse.SC_INTERNAL_SERVER_ERROR:
			return "internal_error";
		default:
			return "unknown_error";
		}
	}

	@Override
	protected void doOptions(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {

		// 处理CORS预检请求
		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
		response.setHeader("Access-Control-Allow-Headers", "Authorization, Content-Type");
		response.setHeader("Access-Control-Max-Age", "3600");
		response.setStatus(HttpServletResponse.SC_OK);
	}

	@Override
	public String getServletInfo() {
		return "Feishu OAuth2 User Info Servlet - Handles user information retrieval from Feishu API";
	}
}
