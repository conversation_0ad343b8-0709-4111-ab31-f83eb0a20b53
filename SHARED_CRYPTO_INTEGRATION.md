# 共享加密模块集成指南

## 概述

为了解决Builder和Manage模块之间签名内容不一致的问题，我们创建了一个独立的`license-crypto`模块，包含共享的RSA密钥管理和许可证加解密逻辑。

## 问题解决

### 原始问题
- Builder模块生成许可证时使用的签名内容与Manage模块验证时使用的签名内容不一致
- 两个模块各自维护RSA加解密代码，容易出现不同步的问题
- 密钥管理分散，难以保证一致性

### 解决方案
1. **创建独立的license-crypto模块**
2. **统一签名内容构建逻辑**
3. **共享密钥管理**
4. **保持向后兼容性**

## 模块结构

### license-crypto模块
```
license-crypto/
├── pom.xml
└── src/main/java/com/fasnote/alm/license/crypto/
    ├── RSAKeyManager.java          # RSA密钥管理器
    └── RSALicenseEncryption.java   # RSA许可证加解密器
```

### 核心类说明

#### RSAKeyManager
- 负责RSA密钥的加载、管理和签名验证
- 支持从文件路径加载密钥对（Builder模块使用）
- 支持使用硬编码公钥（Manage模块使用）
- 提供统一的签名生成和验证接口

#### RSALicenseEncryption
- 负责许可证的RSA加密、解密和数字签名验证
- 包含统一的签名内容构建逻辑
- 与LicensePackager生成的JSON结构保持一致

## 集成方式

### license-builder模块集成
1. **添加Maven依赖**：
   ```xml
   <dependency>
       <groupId>com.fasnote.alm</groupId>
       <artifactId>license-crypto</artifactId>
       <version>1.0.0</version>
   </dependency>
   ```

2. **更新现有代码**：
   - 导入`com.fasnote.alm.license.crypto.RSAKeyManager`
   - 导入`com.fasnote.alm.license.crypto.RSALicenseEncryption`
   - 使用共享的加解密逻辑

### plugin-manage模块集成
1. **复制JAR文件**：
   ```bash
   cp license-crypto/target/license-crypto-1.0.0.jar plugin-manage/lib/
   ```

2. **创建适配器类**：
   - `SharedRSALicenseEncryption`：桥接crypto模块和现有的plugin-manage接口
   - 保持现有API不变，内部使用共享的crypto模块

3. **更新SecurityValidator**：
   - 添加`SharedRSALicenseEncryption`字段
   - 创建`buildSharedRSAEncryptedPackageFromLicense`方法
   - 在RSA许可证验证中使用新的共享逻辑

## 签名内容一致性

### 统一的签名内容构建
两个模块现在使用相同的签名内容构建逻辑：

```java
private String createLicenseContentForSignature(EncryptedLicensePackage encryptedPackage) {
    // 创建完整的字段映射，包含所有字段
    Map<String, Object> allFields = new TreeMap<>();
    
    // 添加固定字段（按字母顺序）
    allFields.put("contentHash", "");
    allFields.put("customProperties", "{}");
    allFields.put("features", "{}");
    allFields.put("limitations", "{}");
    allFields.put("machineCode", "");
    allFields.put("signature", "");
    
    // 添加元数据
    // 按字母顺序添加所有字段
    // 添加加密数据
    // ...
}
```

### 字段包含
签名内容现在包含所有LicensePackager生成的字段：
- `contentHash`
- `customProperties`
- `features`
- `limitations`
- `machineCode`
- `signature`
- 所有许可证元数据字段
- `encryptedClassData`
- `encryptionVersion`
- `keyVersion`

## 密钥管理

### 统一的密钥配置
- Builder模块：从`~/opt/license/`路径加载密钥对
- Manage模块：使用硬编码的`PUBLIC_KEY_V1`常量
- 两个模块使用相同的公钥进行验证

### 密钥同步
当需要更新密钥时：
1. 生成新的密钥对：
   ```bash
   java -cp target/classes com.fasnote.alm.license.builder.tools.RSAKeyGeneratorTool ~/opt/license
   ```

2. 更新Manage模块的公钥常量：
   - 更新`RSAKeyManager.PUBLIC_KEY_V1`
   - 重新编译license-crypto模块
   - 更新plugin-manage/lib/中的jar文件

## 向后兼容性

### plugin-manage模块
- 保持现有的`RSALicenseEncryption`类不变
- 新增`SharedRSALicenseEncryption`作为适配器
- `SecurityValidator`同时支持两种验证方式
- 现有的API和接口保持不变

### license-builder模块
- 现有的工具类继续工作
- 签名内容构建逻辑已更新为与crypto模块一致
- 所有现有功能保持兼容

## 测试验证

### 验证步骤
1. **编译所有模块**：
   ```bash
   cd license-crypto && mvn clean install
   cd ../license-builder && mvn compile
   cd ../plugin-manage && mvn compile
   ```

2. **生成新的许可证**：
   ```bash
   cd license-builder
   ./scripts/build-plugin.sh ../../com.fasnote.alm.auth.feishu/ -m full -v --rsa-key-path ~/opt/license
   ```

3. **验证Builder模块自验证**：
   ```bash
   mvn exec:java -Dexec.mainClass="com.fasnote.alm.license.builder.tools.SelfVerificationTool" \
     -Dexec.args="path/to/generated/license.lic"
   ```

4. **验证Manage模块验证**：
   - 将生成的许可证文件部署到plugin-manage环境
   - 检查验证日志确认使用SharedRSALicenseEncryption成功验证

## 维护建议

### 代码维护
1. **统一修改**：所有签名相关的逻辑修改都应该在license-crypto模块中进行
2. **版本同步**：确保两个模块使用相同版本的license-crypto
3. **测试覆盖**：在crypto模块中添加完整的单元测试

### 部署流程
1. 修改crypto模块后，重新编译并安装到本地Maven仓库
2. 更新plugin-manage/lib/中的jar文件
3. 重新编译和测试两个模块
4. 确保密钥配置同步

## 故障排除

### 常见问题
1. **签名验证失败**：检查两个模块是否使用相同版本的crypto模块
2. **密钥不匹配**：使用KeyConsistencyChecker工具验证密钥一致性
3. **Base64解码错误**：检查许可证文件格式是否正确

### 调试工具
- `KeyConsistencyChecker`：验证密钥一致性
- `SignatureContentComparator`：比较签名内容差异
- `SelfVerificationTool`：Builder模块自验证工具
