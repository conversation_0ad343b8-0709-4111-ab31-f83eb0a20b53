package com.fasnote.alm.auth.feishu.utils;

import java.net.URLDecoder;
import java.net.URLEncoder;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 飞书State参数工具类
 * 
 * 用于在state参数中编码和解码目标URL，避免redirect_uri包含查询参数
 */
public class FeishuStateUtils {

	private static final String TARGET_MARKER = "|target:";

	/**
	 * 生成包含目标URL的state参数
	 * 
	 * @param baseState 基础state值
	 * @param targetUrl 目标URL
	 * @return 编码后的state参数
	 */
	@NotNull
	public static String encodeTargetUrlInState(@NotNull String baseState, @Nullable String targetUrl) {
		if (targetUrl != null && !targetUrl.isEmpty()) {
			try {
				// URL编码目标URL
				String encodedTargetUrl = URLEncoder.encode(targetUrl, "UTF-8");
				// 将目标URL附加到state参数中，使用特殊分隔符
				return baseState + TARGET_MARKER + encodedTargetUrl;
			} catch (Exception e) {
				// 编码失败，返回原始state
				return baseState;
			}
		}

		return baseState;
	}

	/**
	 * 从state参数中提取目标URL
	 *
	 * @param state state参数
	 * @return 解码后的目标URL，如果没有则返回null
	 */
	@Nullable
	public static String extractTargetUrlFromState(@Nullable String state) {
		if (state == null || state.isEmpty()) {
			return null;
		}

		// 查找目标URL标记
		int targetIndex = state.indexOf(TARGET_MARKER);
		if (targetIndex >= 0) {
			try {
				String encodedTargetUrl = state.substring(targetIndex + TARGET_MARKER.length());
				return URLDecoder.decode(encodedTargetUrl, "UTF-8");
			} catch (Exception e) {
				// 忽略解码错误，返回null
				return null;
			}
		}

		return null;
	}

	/**
	 * 从state参数中提取基础state值（移除目标URL部分）
	 *
	 * @param state 完整的state参数
	 * @return 基础state值
	 */
	@NotNull
	public static String extractBaseState(@Nullable String state) {
		if (state == null || state.isEmpty()) {
			return "";
		}

		// 查找目标URL标记
		int targetIndex = state.indexOf(TARGET_MARKER);
		if (targetIndex >= 0) {
			return state.substring(0, targetIndex);
		}

		return state;
	}

	/**
	 * 检查state参数是否包含目标URL
	 * 
	 * @param state state参数
	 * @return 是否包含目标URL
	 */
	public static boolean hasTargetUrl(@Nullable String state) {
		return state != null && state.contains(TARGET_MARKER);
	}


}
