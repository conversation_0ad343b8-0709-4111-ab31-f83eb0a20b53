package com.fasnote.alm.plugin.manage.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 许可证信息模型 用于对外提供许可证的基本信息，不包含敏感数据
 */
public class LicenseInfo {

	private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

	private String pluginId;
	private String productName;
	private String version;
	private String licenseType;
	private String licenseData; // 许可证数据
	private LocalDateTime expiryDate;
	private LocalDateTime loadTime; // 加载时间
	private boolean valid;
	private String status;
	private Map<String, Object> publicFeatures;

	/**
	 * 无参构造函数
	 */
	public LicenseInfo() {
		this.publicFeatures = new HashMap<>();
	}

	/**
	 * 构造函数
	 */
	public LicenseInfo(String pluginId, String productName, String version, LocalDateTime expiryDate, boolean valid) {
		this.pluginId = pluginId;
		this.productName = productName;
		this.version = version;
		this.expiryDate = expiryDate;
		this.valid = valid;
		this.publicFeatures = new HashMap<>();
		this.status = determineStatus();
	}

	/**
	 * 完整构造函数
	 */
	public LicenseInfo(String pluginId, String productName, String version, String licenseType,
			LocalDateTime expiryDate, boolean valid) {
		this(pluginId, productName, version, expiryDate, valid);
		this.licenseType = licenseType;
	}

	/**
	 * 添加公开功能信息
	 *
	 * @param featureName  功能名称
	 * @param featureValue 功能值
	 */
	public void addPublicFeature(String featureName, Object featureValue) {
		publicFeatures.put(featureName, featureValue);
	}

	/**
	 * 确定许可证状态
	 *
	 * @return 状态描述
	 */
	private String determineStatus() {
		if (!valid) {
			if (expiryDate != null && LocalDateTime.now().isAfter(expiryDate)) {
				return "已过期";
			} else {
				return "无效";
			}
		} else {
			if (expiryDate == null) {
				return "永久有效";
			} else {
				long daysUntilExpiry = java.time.temporal.ChronoUnit.DAYS.between(LocalDateTime.now(), expiryDate);

				if (daysUntilExpiry <= 0) {
					return "已过期";
				} else if (daysUntilExpiry <= 30) {
					return "即将过期 (" + daysUntilExpiry + "天)";
				} else {
					return "有效";
				}
			}
		}
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
		if (obj == null || getClass() != obj.getClass()) {
			return false;
		}

		LicenseInfo that = (LicenseInfo) obj;

		if ((valid != that.valid) || (pluginId != null ? !pluginId.equals(that.pluginId) : that.pluginId != null) || (productName != null ? !productName.equals(that.productName) : that.productName != null)
				|| (version != null ? !version.equals(that.version) : that.version != null)) {
			return false;
		}
		if (licenseType != null ? !licenseType.equals(that.licenseType) : that.licenseType != null) {
			return false;
		}
		if (expiryDate != null ? !expiryDate.equals(that.expiryDate) : that.expiryDate != null) {
			return false;
		}

		return true;
	}

	/**
	 * 获取剩余天数
	 *
	 * @return 剩余天数，如果是永久许可证返回-1
	 */
	public long getDaysRemaining() {
		if (expiryDate == null) {
			return -1; // 永久许可证
		}

		return java.time.temporal.ChronoUnit.DAYS.between(LocalDateTime.now(), expiryDate);
	}

	public LocalDateTime getExpiryDate() {
		return expiryDate;
	}

	/**
	 * 获取格式化的过期时间
	 *
	 * @return 格式化的过期时间字符串
	 */
	public String getFormattedExpiryDate() {
		if (expiryDate == null) {
			return "永不过期";
		}
		return expiryDate.format(DATE_FORMATTER);
	}

	public String getLicenseData() {
		return licenseData;
	}

	public String getLicenseType() {
		return licenseType;
	}

	// Getter 和 Setter 方法

	public LocalDateTime getLoadTime() {
		return loadTime;
	}

	public String getPluginId() {
		return pluginId;
	}

	public String getProductName() {
		return productName;
	}

	/**
	 * 获取公开功能信息
	 *
	 * @param featureName 功能名称
	 * @return 功能值
	 */
	public Object getPublicFeature(String featureName) {
		return publicFeatures.get(featureName);
	}

	public Map<String, Object> getPublicFeatures() {
		return new HashMap<>(publicFeatures);
	}

	public String getStatus() {
		return status;
	}

	public String getVersion() {
		return version;
	}

	@Override
	public int hashCode() {
		int result = pluginId != null ? pluginId.hashCode() : 0;
		result = 31 * result + (productName != null ? productName.hashCode() : 0);
		result = 31 * result + (version != null ? version.hashCode() : 0);
		result = 31 * result + (licenseType != null ? licenseType.hashCode() : 0);
		result = 31 * result + (expiryDate != null ? expiryDate.hashCode() : 0);
		result = 31 * result + (valid ? 1 : 0);
		return result;
	}

	/**
	 * 检查是否支持指定功能
	 *
	 * @param featureName 功能名称
	 * @return 是否支持
	 */
	public boolean hasPublicFeature(String featureName) {
		Object feature = publicFeatures.get(featureName);
		if (feature instanceof Boolean) {
			return (Boolean) feature;
		}
		return feature != null;
	}

	/**
	 * 检查许可证是否即将过期
	 *
	 * @param warningDays 警告天数
	 * @return 是否即将过期
	 */
	public boolean isExpiringSoon(int warningDays) {
		if (expiryDate == null) {
			return false; // 永久许可证不会过期
		}

		long daysUntilExpiry = java.time.temporal.ChronoUnit.DAYS.between(LocalDateTime.now(), expiryDate);

		return daysUntilExpiry > 0 && daysUntilExpiry <= warningDays;
	}

	public boolean isValid() {
		return valid;
	}

	public void setExpiryDate(LocalDateTime expiryDate) {
		this.expiryDate = expiryDate;
		this.status = determineStatus(); // 重新计算状态
	}

	public void setLicenseData(String licenseData) {
		this.licenseData = licenseData;
	}

	public void setLicenseType(String licenseType) {
		this.licenseType = licenseType;
	}

	public void setLoadTime(LocalDateTime loadTime) {
		this.loadTime = loadTime;
	}

	public void setPluginId(String pluginId) {
		this.pluginId = pluginId;
		this.status = determineStatus(); // 重新计算状态
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public void setPublicFeatures(Map<String, Object> publicFeatures) {
		this.publicFeatures = new HashMap<>(publicFeatures);
	}

	public void setValid(boolean valid) {
		this.valid = valid;
		this.status = determineStatus(); // 重新计算状态
	}

	public void setVersion(String version) {
		this.version = version;
	}

	/**
	 * 创建简化的许可证信息（用于API返回）
	 *
	 * @return 简化的许可证信息
	 */
	public Map<String, Object> toSimpleMap() {
		Map<String, Object> map = new HashMap<>();
		map.put("pluginId", pluginId);
		map.put("productName", productName);
		map.put("version", version);
		map.put("licenseType", licenseType);
		map.put("expiryDate", getFormattedExpiryDate());
		map.put("valid", valid);
		map.put("status", status);
		map.put("daysRemaining", getDaysRemaining());
		map.put("features", new HashMap<>(publicFeatures));

		return map;
	}

	@Override
	public String toString() {
		return String.format(
				"LicenseInfo{pluginId='%s', productName='%s', version='%s', "
						+ "licenseType='%s', expiryDate=%s, valid=%s, status='%s'}",
				pluginId, productName, version, licenseType, getFormattedExpiryDate(), valid, status);
	}
}
