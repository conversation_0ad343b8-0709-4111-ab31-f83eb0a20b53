package com.fasnote.alm.license.builder.validation;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 验证器选择器
 * 根据构建阶段和插件类型自动选择合适的验证器
 */
public class ValidatorSelector {
    
    private static final Logger logger = LoggerFactory.getLogger(ValidatorSelector.class);
    
    private final Map<String, PhaseAwareValidator> validators = new LinkedHashMap<>();
    
    /**
     * 注册验证器
     */
    public void registerValidator(PhaseAwareValidator validator) {
        if (validator == null) {
            throw new IllegalArgumentException("验证器不能为空");
        }
        
        String name = validator.getName();
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("验证器名称不能为空");
        }
        
        if (validators.containsKey(name)) {
            logger.warn("验证器 {} 已存在，将被覆盖", name);
        }
        
        validators.put(name, validator);
        logger.debug("注册验证器: {} (阶段: {}, 插件类型: {}, 优先级: {})", 
                    name, validator.getSupportedPhases(), 
                    validator.getSupportedPluginTypes(), validator.getPriority());
    }
    
    /**
     * 批量注册验证器
     */
    public void registerValidators(Collection<PhaseAwareValidator> validators) {
        for (PhaseAwareValidator validator : validators) {
            registerValidator(validator);
        }
    }
    
    /**
     * 获取所有注册的验证器
     */
    public Collection<PhaseAwareValidator> getAllValidators() {
        return new ArrayList<>(validators.values());
    }
    
    /**
     * 根据名称获取验证器
     */
    public PhaseAwareValidator getValidator(String name) {
        return validators.get(name);
    }
    
    /**
     * 根据验证上下文选择合适的验证器
     */
    public List<PhaseAwareValidator> selectValidators(ValidationContext context) {
        if (context == null) {
            throw new IllegalArgumentException("验证上下文不能为空");
        }
        
        BuildPhase currentPhase = context.getCurrentPhase();
        PluginType pluginType = context.getPluginType();
        
        logger.debug("选择验证器: 阶段={}, 插件类型={}", currentPhase, pluginType);
        
        List<PhaseAwareValidator> selectedValidators = validators.values().stream()
                .filter(validator -> isPhaseMatched(validator, currentPhase))
                .filter(validator -> isPluginTypeMatched(validator, pluginType))
                .filter(validator -> validator.isApplicable(context))
                .sorted(this::compareValidators)
                .collect(Collectors.toList());
        
        logger.info("为阶段 {} 和插件类型 {} 选择了 {} 个验证器: {}", 
                   currentPhase, pluginType, selectedValidators.size(),
                   selectedValidators.stream().map(PhaseAwareValidator::getName).collect(Collectors.toList()));
        
        return selectedValidators;
    }
    
    /**
     * 检查阶段是否匹配
     */
    private boolean isPhaseMatched(PhaseAwareValidator validator, BuildPhase phase) {
        Set<BuildPhase> supportedPhases = validator.getSupportedPhases();
        return supportedPhases.contains(phase) || supportedPhases.contains(BuildPhase.ALL);
    }
    
    /**
     * 检查插件类型是否匹配
     */
    private boolean isPluginTypeMatched(PhaseAwareValidator validator, PluginType type) {
        Set<PluginType> supportedTypes = validator.getSupportedPluginTypes();
        return supportedTypes.contains(type) || supportedTypes.contains(PluginType.ALL);
    }
    
    /**
     * 验证器比较器：按优先级排序，优先级相同时按名称排序
     */
    private int compareValidators(PhaseAwareValidator v1, PhaseAwareValidator v2) {
        int priorityCompare = Integer.compare(v1.getPriority(), v2.getPriority());
        if (priorityCompare != 0) {
            return priorityCompare;
        }
        return v1.getName().compareTo(v2.getName());
    }
    
    /**
     * 获取指定阶段的验证器数量
     */
    public int getValidatorCount(BuildPhase phase) {
        return (int) validators.values().stream()
                .filter(validator -> isPhaseMatched(validator, phase))
                .count();
    }
    
    /**
     * 获取指定插件类型的验证器数量
     */
    public int getValidatorCount(PluginType pluginType) {
        return (int) validators.values().stream()
                .filter(validator -> isPluginTypeMatched(validator, pluginType))
                .count();
    }
    
    /**
     * 清空所有验证器
     */
    public void clear() {
        validators.clear();
        logger.debug("清空所有验证器");
    }
    
    /**
     * 移除指定验证器
     */
    public boolean removeValidator(String name) {
        PhaseAwareValidator removed = validators.remove(name);
        if (removed != null) {
            logger.debug("移除验证器: {}", name);
            return true;
        }
        return false;
    }
    
    /**
     * 获取验证器统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalValidators", validators.size());
        stats.put("postObfuscationValidators", getValidatorCount(BuildPhase.POST_OBFUSCATION));
        stats.put("postLicenseBuildValidators", getValidatorCount(BuildPhase.POST_LICENSE_BUILD));
        stats.put("businessPluginValidators", getValidatorCount(PluginType.BUSINESS));
        stats.put("managePluginValidators", getValidatorCount(PluginType.MANAGE));
        return stats;
    }
}
