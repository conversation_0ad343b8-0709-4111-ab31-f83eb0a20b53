package com.fasnote.alm.plugin.manage;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import com.fasnote.alm.plugin.manage.bootstrap.LicenseFrameworkBootstrapTest;
import com.fasnote.alm.plugin.manage.monitor.FrameworkMonitorTest;
import com.fasnote.alm.plugin.manage.registry.LicenseServiceRegistryTest;

/**
 * 简单的测试运行器 用于在没有JUnit环境的情况下运行基本测试
 */
public class TestRunner {

	public static void main(String[] args) {
		System.out.println("=== 重构架构单元测试运行器 ===\n");

		TestRunner runner = new TestRunner();
		int totalTests = 0;
		int passedTests = 0;
		int failedTests = 0;

		// 运行各个测试类
		System.out.println("1. 运行 LicenseServiceRegistry 测试...");
		int[] registryResults = runner.runBasicTests(LicenseServiceRegistryTest.class);
		totalTests += registryResults[0];
		passedTests += registryResults[1];
		failedTests += registryResults[2];

		System.out.println("\n2. 运行 FrameworkMonitor 测试...");
		int[] monitorResults = runner.runBasicTests(FrameworkMonitorTest.class);
		totalTests += monitorResults[0];
		passedTests += monitorResults[1];
		failedTests += monitorResults[2];

		System.out.println("\n3. 运行 LicenseFrameworkBootstrap 测试...");
		int[] bootstrapResults = runner.runBasicTests(LicenseFrameworkBootstrapTest.class);
		totalTests += bootstrapResults[0];
		passedTests += bootstrapResults[1];
		failedTests += bootstrapResults[2];

		// Facade 测试已删除，因为 facade 包已被移除

		// 输出总结
		System.out.println("\n" + "=".repeat(50));
		System.out.println("测试总结:");
		System.out.println("总测试数: " + totalTests);
		System.out.println("通过: " + passedTests);
		System.out.println("失败: " + failedTests);
		System.out.println("成功率: " + String.format("%.1f%%", (double) passedTests / totalTests * 100));

		if (failedTests == 0) {
			System.out.println("\n🎉 所有测试通过！重构架构测试成功！");
		} else {
			System.out.println("\n⚠️  有 " + failedTests + " 个测试失败，请检查实现。");
		}
	}

	/**
	 * 运行架构验证测试
	 */
	public static void runArchitectureValidation() {
		System.out.println("\n=== 架构验证测试 ===");

		try {
			// 验证所有新类都存在
			Class.forName("com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade");
			Class.forName("com.fasnote.alm.plugin.manage.facade.LicenseManagementFacade");
			Class.forName("com.fasnote.alm.plugin.manage.facade.ConfigurationFacade");
			Class.forName("com.fasnote.alm.plugin.manage.facade.SecurityFacade");
			Class.forName("com.fasnote.alm.plugin.manage.bootstrap.LicenseFrameworkBootstrap");
			Class.forName("com.fasnote.alm.plugin.manage.registry.LicenseServiceRegistry");
			Class.forName("com.fasnote.alm.plugin.manage.monitor.FrameworkMonitor");

			System.out.println("✓ 所有新架构类都存在");

			// 验证旧类已被移除
			try {
				Class.forName("com.fasnote.alm.plugin.manage.LicenseManagementFramework");
				System.out.println("⚠️  旧的LicenseManagementFramework类仍然存在");
			} catch (ClassNotFoundException e) {
				System.out.println("✓ 旧的LicenseManagementFramework类已被移除");
			}

			System.out.println("✓ 架构验证通过");

		} catch (ClassNotFoundException e) {
			System.out.println("✗ 架构验证失败: " + e.getMessage());
		}
	}

	/**
	 * 运行基本测试（不依赖JUnit注解）
	 *
	 * @param testClass 测试类
	 * @return [总数, 通过数, 失败数]
	 */
	private int[] runBasicTests(Class<?> testClass) {
		int total = 0;
		int passed = 0;
		int failed = 0;

		try {
			Object testInstance = testClass.getDeclaredConstructor().newInstance();

			// 查找setUp方法并执行
			try {
				Method setUp = testClass.getDeclaredMethod("setUp");
				setUp.invoke(testInstance);
			} catch (NoSuchMethodException e) {
				// setUp方法不存在，忽略
			}

			// 查找所有test开头的方法
			Method[] methods = testClass.getDeclaredMethods();
			List<Method> testMethods = new ArrayList<>();

			for (Method method : methods) {
				if (method.getName().startsWith("test") && method.getParameterCount() == 0) {
					testMethods.add(method);
				}
			}

			System.out.println("  找到 " + testMethods.size() + " 个测试方法");

			for (Method testMethod : testMethods) {
				total++;
				try {
					testMethod.invoke(testInstance);
					passed++;
					System.out.println("    ✓ " + testMethod.getName());
				} catch (Exception e) {
					failed++;
					System.out
							.println("    ✗ " + testMethod.getName() + " - " + e.getCause().getClass().getSimpleName());
				}
			}

		} catch (Exception e) {
			System.out.println("  ✗ 无法创建测试实例: " + e.getMessage());
			failed++;
			total++;
		}

		System.out.println("  结果: " + passed + "/" + total + " 通过");
		return new int[] { total, passed, failed };
	}
}
