package com.fasnote.alm.plugin.manage.exception;

/**
 * 插件异常类 用于表示插件相关的异常
 */
public class PluginException extends LicenseException {

	private static final long serialVersionUID = 1L;

	/**
	 * 构造函数
	 *
	 * @param message 异常消息
	 */
	public PluginException(String message) {
		super(message);
	}

	/**
	 * 构造函数
	 *
	 * @param message   异常消息
	 * @param errorCode 错误码
	 */
	public PluginException(String message, String errorCode) {
		super(message, errorCode);
	}

	/**
	 * 构造函数
	 *
	 * @param message   异常消息
	 * @param errorCode 错误码
	 * @param cause     原因异常
	 */
	public PluginException(String message, String errorCode, Throwable cause) {
		super(message, errorCode, cause);
	}

	/**
	 * 构造函数
	 *
	 * @param message 异常消息
	 * @param cause   原因异常
	 */
	public PluginException(String message, Throwable cause) {
		super(message, cause);
	}
}
