package com.fasnote.alm.license.builder.validation;

import java.util.ArrayList;
import java.util.List;

/**
 * 验证结果
 * 包含验证的成功/失败状态和详细信息
 */
public class ValidationResult {
    
    private final boolean success;
    private final String validatorName;
    private final List<String> messages;
    private final List<String> warnings;
    private final Exception exception;
    
    private ValidationResult(Builder builder) {
        this.success = builder.success;
        this.validatorName = builder.validatorName;
        this.messages = new ArrayList<>(builder.messages);
        this.warnings = new ArrayList<>(builder.warnings);
        this.exception = builder.exception;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public String getValidatorName() {
        return validatorName;
    }
    
    public List<String> getMessages() {
        return new ArrayList<>(messages);
    }
    
    public List<String> getWarnings() {
        return new ArrayList<>(warnings);
    }
    
    public Exception getException() {
        return exception;
    }
    
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
    
    public boolean hasMessages() {
        return !messages.isEmpty();
    }
    
    /**
     * 创建成功结果
     */
    public static ValidationResult success(String validatorName) {
        return builder()
                .validatorName(validatorName)
                .success(true)
                .build();
    }
    
    /**
     * 创建成功结果（带消息）
     */
    public static ValidationResult success(String validatorName, String message) {
        return builder()
                .validatorName(validatorName)
                .success(true)
                .message(message)
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static ValidationResult failure(String validatorName, String message) {
        return builder()
                .validatorName(validatorName)
                .success(false)
                .message(message)
                .build();
    }
    
    /**
     * 创建失败结果（带异常）
     */
    public static ValidationResult failure(String validatorName, String message, Exception exception) {
        return builder()
                .validatorName(validatorName)
                .success(false)
                .message(message)
                .exception(exception)
                .build();
    }
    
    /**
     * 创建构建器
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * 构建器类
     */
    public static class Builder {
        private boolean success = true;
        private String validatorName;
        private List<String> messages = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();
        private Exception exception;
        
        public Builder success(boolean success) {
            this.success = success;
            return this;
        }
        
        public Builder validatorName(String validatorName) {
            this.validatorName = validatorName;
            return this;
        }
        
        public Builder message(String message) {
            this.messages.add(message);
            return this;
        }
        
        public Builder messages(List<String> messages) {
            this.messages.addAll(messages);
            return this;
        }
        
        public Builder warning(String warning) {
            this.warnings.add(warning);
            return this;
        }
        
        public Builder warnings(List<String> warnings) {
            this.warnings.addAll(warnings);
            return this;
        }
        
        public Builder exception(Exception exception) {
            this.exception = exception;
            return this;
        }
        
        public ValidationResult build() {
            return new ValidationResult(this);
        }
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("ValidationResult{")
          .append("validator='").append(validatorName).append('\'')
          .append(", success=").append(success);
        
        if (!messages.isEmpty()) {
            sb.append(", messages=").append(messages);
        }
        if (!warnings.isEmpty()) {
            sb.append(", warnings=").append(warnings);
        }
        if (exception != null) {
            sb.append(", exception=").append(exception.getMessage());
        }
        
        sb.append('}');
        return sb.toString();
    }
}
