bundle:
  symbolicName: ${bundle_symbolic_name}
  version: ${bundle_version}
  name: ${bundle_name}
  vendor: ${bundle_vendor}
  activator: ${bundle_activator}
  exportedPackages: ${bundle_exported_packages}

build:
  finalName: ${final_name}
  javaVersion: ${java_version}
  tychoVersion: ${tycho_version}

license:
  validity: ${license_validity}
  machineBinding: ${machine_binding_enabled}

obfuscation:
  enabled: ${enable_obfuscation}
  tool: ${obfuscation_tool}
  level: ${obfuscation_level}
