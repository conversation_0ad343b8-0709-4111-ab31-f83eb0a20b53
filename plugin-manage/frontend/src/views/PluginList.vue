<template>
  <div class="plugin-list">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>插件列表</span>
          <div class="header-actions">
            <el-button type="primary" @click="loadPlugins" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table 
        :data="plugins" 
        v-loading="loading"
        stripe
        style="width: 100%"
        @row-click="handleRowClick"
      >
        <el-table-column prop="pluginId" label="插件ID" width="200" />
        <el-table-column prop="pluginName" label="插件名称" width="180" />
        <el-table-column prop="version" label="版本" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag 
              :type="getStatusType(scope.row.status)"
              size="small"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="licenseValid" label="许可证状态" width="120">
          <template #default="scope">
            <el-tag 
              :type="scope.row.licenseValid ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.licenseValid ? '有效' : '无效' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="licenseStatus" label="许可证详情" min-width="200" />
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              size="small" 
              @click.stop="viewDetail(scope.row)"
            >
              查看详情
            </el-button>
            <el-button 
              type="success" 
              size="small" 
              @click.stop="validateLicense(scope.row)"
              :loading="validatingPlugins.has(scope.row.pluginId)"
            >
              验证
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div v-if="!loading && plugins.length === 0" class="empty-state">
        <el-empty description="暂无插件数据">
          <el-button type="primary" @click="loadPlugins">重新加载</el-button>
        </el-empty>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { pluginApi } from '../api'
import { Refresh } from '@element-plus/icons-vue'

export default {
  name: 'PluginList',
  components: {
    Refresh
  },
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const plugins = ref([])
    const validatingPlugins = ref(new Set())

    const loadPlugins = async () => {
      loading.value = true
      try {
        const data = await pluginApi.getAllPlugins()
        plugins.value = data || []
        ElMessage.success(`成功加载 ${plugins.value.length} 个插件`)
      } catch (error) {
        console.error('加载插件列表失败:', error)
        ElMessage.error('加载插件列表失败')
        plugins.value = []
      } finally {
        loading.value = false
      }
    }

    const getStatusType = (status) => {
      switch (status) {
        case 'ACTIVE':
          return 'success'
        case 'ERROR':
          return 'danger'
        case 'INACTIVE':
          return 'warning'
        default:
          return 'info'
      }
    }

    const handleRowClick = (row) => {
      viewDetail(row)
    }

    const viewDetail = (plugin) => {
      router.push(`/plugins/${plugin.pluginId}`)
    }

    const validateLicense = async (plugin) => {
      validatingPlugins.value.add(plugin.pluginId)
      try {
        const result = await pluginApi.validatePluginLicense(plugin.pluginId)
        
        if (result.valid) {
          ElMessage.success(`插件 ${plugin.pluginId} 许可证验证通过`)
        } else {
          ElMessage.warning(`插件 ${plugin.pluginId} 许可证验证失败`)
        }
        
        // 重新加载插件列表以更新状态
        await loadPlugins()
      } catch (error) {
        console.error('验证许可证失败:', error)
        ElMessage.error(`验证插件 ${plugin.pluginId} 许可证失败`)
      } finally {
        validatingPlugins.value.delete(plugin.pluginId)
      }
    }

    // 监听全局刷新事件
    const handleGlobalRefresh = () => {
      loadPlugins()
    }

    onMounted(() => {
      loadPlugins()
      window.addEventListener('refresh-data', handleGlobalRefresh)
    })

    return {
      loading,
      plugins,
      validatingPlugins,
      loadPlugins,
      getStatusType,
      handleRowClick,
      viewDetail,
      validateLicense
    }
  }
}
</script>

<style scoped>
.plugin-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.el-table {
  cursor: pointer;
}

.el-table .el-table__row:hover {
  background-color: #f5f7fa;
}
</style>
