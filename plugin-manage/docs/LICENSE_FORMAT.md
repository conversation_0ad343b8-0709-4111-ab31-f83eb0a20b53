# 许可证文件格式说明和示例

## 1. 许可证文件结构

许可证文件(`.lic`)是一个JSON格式的文件，包含插件的许可信息、加密的实现类和服务映射关系。

### 1.1 完整格式定义

```json
{
  // === 基本信息 ===
  "pluginId": "string",           // 插件唯一标识
  "productName": "string",        // 产品名称
  "version": "string",            // 版本号
  "licenseType": "string",        // 许可证类型：Trial|Commercial|Enterprise
  "issuer": "string",             // 发行机构
  
  // === 时间限制 ===
  "issueDate": "yyyy-MM-dd HH:mm:ss",      // 签发时间
  "effectiveDate": "yyyy-MM-dd HH:mm:ss",  // 生效时间
  "expiryDate": "yyyy-MM-dd HH:mm:ss",     // 过期时间（null表示永久）
  
  // === 安全信息 ===
  "machineCode": "string",        // 机器码绑定（可选）
  "signature": "string",          // 数字签名
  "contentHash": "string",        // 内容哈希值
  
  // === 用户信息 ===
  "licensedTo": "string",         // 许可给谁
  "organization": "string",       // 组织名称
  "maxUsers": number,            // 最大用户数
  
  // === 功能控制 ===
  "features": {                   // 功能开关
    "feature_name": boolean,
    "advanced_analytics": true,
    "data_export": true,
    "real_time_processing": false
  },
  
  "limitations": {                // 功能限制
    "max_data_size": number,
    "concurrent_users": number,
    "api_calls_per_day": number
  },
  
  // === 服务映射（核心部分）===
  "serviceMappings": {            // 接口到实现类的映射
    "interface_full_name": "implementation_full_name",
    "com.example.IService": "com.example.impl.PremiumService"
  },
  
  // === 加密数据 ===
  "encryptedClassData": "string", // Base64编码的加密类文件数据
  
  // === 扩展属性 ===
  "customProperties": {           // 自定义属性
    "key": "value"
  }
}
```

## 2. 许可证示例

### 2.1 数据分析插件许可证示例

```json
{
  "pluginId": "com.fasnote.analytics.premium",
  "productName": "FasNote Premium Analytics",
  "version": "2.1.0",
  "licenseType": "Commercial",
  "issuer": "FasNote Technologies Ltd.",
  
  "issueDate": "2024-01-15 09:00:00",
  "effectiveDate": "2024-01-15 09:00:00",
  "expiryDate": "2025-01-15 23:59:59",
  
  "machineCode": "A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6",
  "signature": "MEUCIQDKj9X7...(省略签名内容)...8H9I0J=",
  "contentHash": "SHA256:f2d8e7c6b5a4...(省略哈希)...1a2b3c4d",
  
  "licensedTo": "Example Corporation",
  "organization": "Example Corp - Analytics Division",
  "maxUsers": 50,
  
  "features": {
    "advanced_analytics": true,
    "predictive_modeling": true,
    "real_time_dashboard": true,
    "data_export": true,
    "custom_reports": true,
    "api_access": true,
    "white_labeling": false
  },
  
  "limitations": {
    "max_data_rows": 1000000,
    "concurrent_users": 20,
    "api_calls_per_day": 10000,
    "storage_limit_mb": 5120
  },
  
  "serviceMappings": {
    "com.fasnote.analytics.IDataProcessor": "com.fasnote.analytics.impl.AdvancedDataProcessor",
    "com.fasnote.analytics.IReportGenerator": "com.fasnote.analytics.impl.PremiumReportGenerator",
    "com.fasnote.analytics.IDataExporter": "com.fasnote.analytics.impl.MultiFormatExporter",
    "com.fasnote.analytics.IPredictiveModel": "com.fasnote.analytics.impl.MLPredictiveModel",
    "com.fasnote.analytics.IDashboardService": "com.fasnote.analytics.impl.RealTimeDashboard"
  },
  
  "encryptedClassData": "UEsDBBQACAgIALxK+VQAAAAAAAAAAAAAAAAWA...(Base64编码的加密JAR数据)...AAAEA",
  
  "customProperties": {
    "deployment_environment": "production",
    "support_level": "premium",
    "training_included": "true",
    "custom_integration": "false"
  }
}
```

### 2.2 试用版许可证示例

```json
{
  "pluginId": "com.fasnote.analytics.trial",
  "productName": "FasNote Analytics Trial",
  "version": "2.1.0",
  "licenseType": "Trial",
  "issuer": "FasNote Technologies Ltd.",
  
  "issueDate": "2024-12-01 10:00:00",
  "effectiveDate": "2024-12-01 10:00:00",
  "expiryDate": "2024-12-31 23:59:59",
  
  "signature": "MEQCIFx7Y8...(省略签名)...9Z0A=",
  "contentHash": "SHA256:e3f2d1c0...(省略哈希)...5a6b7c8d",
  
  "licensedTo": "Trial User",
  "organization": "Evaluation",
  "maxUsers": 3,
  
  "features": {
    "basic_analytics": true,
    "advanced_analytics": false,
    "predictive_modeling": false,
    "real_time_dashboard": false,
    "data_export": true,
    "custom_reports": false,
    "api_access": false
  },
  
  "limitations": {
    "max_data_rows": 10000,
    "concurrent_users": 2,
    "storage_limit_mb": 100
  },
  
  "serviceMappings": {
    "com.fasnote.analytics.IDataProcessor": "com.fasnote.analytics.impl.BasicDataProcessor",
    "com.fasnote.analytics.IDataExporter": "com.fasnote.analytics.impl.BasicExporter"
  },
  
  "encryptedClassData": "UEsDBBQACAgIALxK+VQAA...(试用版加密数据)...AA",
  
  "customProperties": {
    "deployment_environment": "trial",
    "support_level": "community",
    "watermark_enabled": "true"
  }
}
```

### 2.3 企业版许可证示例

```json
{
  "pluginId": "com.fasnote.analytics.enterprise",
  "productName": "FasNote Enterprise Analytics Suite",
  "version": "2.1.0",
  "licenseType": "Enterprise",
  "issuer": "FasNote Technologies Ltd.",
  
  "issueDate": "2024-01-01 00:00:00",
  "effectiveDate": "2024-01-01 00:00:00",
  "expiryDate": null,
  
  "machineCode": "ENTERPRISE_CLUSTER_ID_12345",
  "signature": "MEYCIQDXy9W8...(省略企业签名)...7K8L9M=",
  "contentHash": "SHA256:a1b2c3d4...(省略哈希)...9x0y1z2",
  
  "licensedTo": "Fortune 500 Corporation",
  "organization": "Global Analytics Department",
  "maxUsers": -1,
  
  "features": {
    "advanced_analytics": true,
    "predictive_modeling": true,
    "real_time_dashboard": true,
    "data_export": true,
    "custom_reports": true,
    "api_access": true,
    "white_labeling": true,
    "cluster_deployment": true,
    "custom_algorithms": true,
    "enterprise_sso": true,
    "audit_logging": true
  },
  
  "limitations": {
    "max_data_rows": -1,
    "concurrent_users": -1,
    "api_calls_per_day": -1,
    "storage_limit_mb": -1
  },
  
  "serviceMappings": {
    "com.fasnote.analytics.IDataProcessor": "com.fasnote.analytics.impl.EnterpriseDataProcessor",
    "com.fasnote.analytics.IReportGenerator": "com.fasnote.analytics.impl.EnterpriseReportGenerator",
    "com.fasnote.analytics.IDataExporter": "com.fasnote.analytics.impl.EnterpriseExporter",
    "com.fasnote.analytics.IPredictiveModel": "com.fasnote.analytics.impl.EnterpriseMLModel",
    "com.fasnote.analytics.IDashboardService": "com.fasnote.analytics.impl.EnterpriseDashboard",
    "com.fasnote.analytics.IClusterManager": "com.fasnote.analytics.impl.ClusterManager",
    "com.fasnote.analytics.ISSOProvider": "com.fasnote.analytics.impl.EnterpriseSSOProvider",
    "com.fasnote.analytics.IAuditService": "com.fasnote.analytics.impl.EnterpriseAuditService"
  },
  
  "encryptedClassData": "UEsDBBQACAgIALxK+VQAA...(企业版大量加密数据)...ENTERPRISE_END",
  
  "customProperties": {
    "deployment_environment": "enterprise",
    "support_level": "24x7_premium",
    "training_included": "true",
    "custom_integration": "true",
    "dedicated_support": "true",
    "sla_tier": "platinum",
    "cluster_nodes": "5",
    "backup_retention_days": "90"
  }
}
```

## 3. 字段说明

### 3.1 必填字段
- `pluginId`: 插件唯一标识，用于在系统中注册和查找
- `productName`: 产品名称，用于显示
- `version`: 版本号，用于版本控制
- `licenseType`: 许可证类型，影响功能级别
- `issuer`: 签发机构，用于验证合法性
- `signature`: 数字签名，用于防篡改验证

### 3.2 可选字段
- `machineCode`: 机器绑定，为空则不绑定特定机器
- `expiryDate`: 过期时间，为null表示永久许可证
- `maxUsers`: 最大用户数，-1表示无限制
- `serviceMappings`: 为空表示无许可证专有实现
- `encryptedClassData`: 为空表示无加密实现类

### 3.3 特殊值说明
- **-1**: 在数值限制字段中表示"无限制"
- **null**: 在时间字段中表示"无限制"或"永久"
- **空字符串**: 表示该字段不适用
- **空对象{}**: 表示没有相关配置

## 4. 使用场景

### 4.1 开发阶段
```java
// 开发时使用标准DI接口
IDataProcessor processor = DI.get(IDataProcessor.class);

// 运行时，根据许可证自动获取对应实现：
// - Trial: BasicDataProcessor
// - Commercial: AdvancedDataProcessor  
// - Enterprise: EnterpriseDataProcessor
```

### 4.2 部署阶段
1. 将业务插件jar部署到plugins目录
2. 将对应的.lic文件放到licenses目录
3. 启动应用，系统自动加载许可证和服务实现

### 4.3 运行时阶段
- 系统根据许可证中的`serviceMappings`自动注册对应的服务实现
- 业务代码透明地获取许可证授权的功能
- 过期或无效许可证自动降级到基础功能

## 5. 安全特性

### 5.1 加密保护
- 实现类字节码使用AES-256加密
- 只有有效许可证才能解密和加载
- 内存中的类定义受JVM保护

### 5.2 完整性验证
- 数字签名防止许可证被篡改
- 内容哈希验证数据完整性
- 时间戳防止重放攻击

### 5.3 绑定机制
- 可选的机器码绑定
- 用户数量限制
- 功能级别权限控制

这个许可证系统提供了完整的商业级许可证管理功能，支持从试用版到企业版的各种许可证策略，同时保持了代码的透明性和易用性。