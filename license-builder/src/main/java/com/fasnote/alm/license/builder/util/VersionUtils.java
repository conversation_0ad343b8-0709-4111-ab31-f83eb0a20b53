package com.fasnote.alm.license.builder.util;

import com.fasnote.alm.license.builder.model.BundleInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.util.jar.JarFile;
import java.util.jar.Manifest;
import java.util.stream.Stream;

/**
 * 版本号处理工具类
 * 统一处理版本号中的 qualifier 替换逻辑
 */
public class VersionUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(VersionUtils.class);
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
    
    /**
     * 获取实际的版本号（处理 qualifier 时间戳替换）
     * 
     * @param version 原始版本号
     * @return 处理后的版本号
     */
    public static String getActualVersion(String version) {
        if (version == null || !version.endsWith(".qualifier")) {
            return version;
        }
        
        // 优先从系统属性获取 buildQualifier
        String buildQualifier = System.getProperty("buildQualifier");
        if (buildQualifier != null && !buildQualifier.isEmpty()) {
            logger.debug("使用系统属性 buildQualifier: {}", buildQualifier);
            return version.replace(".qualifier", "." + buildQualifier);
        }
        
        // 如果没有找到 buildQualifier，生成当前时间戳
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        logger.debug("生成时间戳 qualifier: {}", timestamp);
        return version.replace(".qualifier", "." + timestamp);
    }
    
    /**
     * 获取实际的版本号（从 BundleInfo）
     * 
     * @param bundleInfo Bundle信息
     * @return 处理后的版本号
     */
    public static String getActualVersion(BundleInfo bundleInfo) {
        return getActualVersion(bundleInfo.getVersion());
    }
    
    /**
     * 获取实际的Bundle版本号
     * 尝试从Tycho构建的JAR文件中获取正确的版本号（包含时间戳）
     * 
     * @param bundleInfo Bundle信息
     * @return 实际的Bundle版本号
     */
    public static String getActualBundleVersion(BundleInfo bundleInfo) {
        try {
            // 尝试从target目录找到Tycho构建的JAR文件
            Path targetDir = Paths.get("target");
            if (Files.exists(targetDir)) {
                String symbolicName = bundleInfo.getSymbolicName();

                // 查找匹配的JAR文件（排除-obfuscated和license-output目录的文件）
                try (Stream<Path> files = Files.list(targetDir)) {
                    Optional<Path> tychoJar = files
                        .filter(path -> path.toString().endsWith(".jar"))
                        .filter(path -> path.getFileName().toString().startsWith(symbolicName + "_"))
                        .filter(path -> !path.getFileName().toString().contains("-obfuscated"))
                        .findFirst();

                    if (tychoJar.isPresent()) {
                        // 从JAR文件名提取版本号
                        String fileName = tychoJar.get().getFileName().toString();
                        String prefix = symbolicName + "_";
                        String suffix = ".jar";
                        if (fileName.startsWith(prefix) && fileName.endsWith(suffix)) {
                            String version = fileName.substring(prefix.length(), fileName.length() - suffix.length());
                            logger.debug("从Tycho JAR文件提取版本号: {}", version);
                            return version;
                        }

                        // 如果文件名解析失败，尝试从JAR的MANIFEST.MF读取
                        try (JarFile jarFile = new JarFile(tychoJar.get().toFile())) {
                            Manifest manifest = jarFile.getManifest();
                            if (manifest != null) {
                                String version = manifest.getMainAttributes().getValue("Bundle-Version");
                                if (version != null && !version.endsWith(".qualifier")) {
                                    logger.debug("从Tycho JAR的MANIFEST.MF读取版本号: {}", version);
                                    return version;
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("从Tycho JAR文件获取版本号失败: {}", e.getMessage());
        }

        // 如果无法从Tycho JAR获取，则使用标准的qualifier处理
        return getActualVersion(bundleInfo);
    }
    
    /**
     * 处理 Bundle 版本号用于 Maven
     * 对于 .qualifier 版本，保持原样让 Tycho 处理时间戳替换
     * 
     * @param bundleVersion Bundle版本号
     * @return Maven版本号
     */
    public static String processBundleVersionForMaven(String bundleVersion) {
        if (bundleVersion.endsWith(".qualifier")) {
            // 保持 .qualifier，让 Tycho 自动替换为时间戳
            // 同时添加 -SNAPSHOT 用于 Maven 版本管理
            String baseVersion = bundleVersion.replace(".qualifier", "");
            return baseVersion + "-SNAPSHOT";
        } else {
            // 对于非 qualifier 版本，直接添加 -SNAPSHOT
            return bundleVersion + "-SNAPSHOT";
        }
    }
    
    /**
     * 生成最终的 JAR 文件名（让 Tycho 处理 qualifier 替换）
     * 
     * @param bundleInfo Bundle信息
     * @param mavenVersion Maven版本号
     * @return JAR文件名
     */
    public static String generateFinalName(BundleInfo bundleInfo, String mavenVersion) {
        if (bundleInfo.getVersion().endsWith(".qualifier")) {
            // 对于 qualifier 版本，使用 ${unqualifiedVersion}.${buildQualifier} 模式
            // Tycho 会自动替换 buildQualifier 为时间戳
            String baseVersion = bundleInfo.getVersion().replace(".qualifier", "");
            return bundleInfo.getSymbolicName() + "_" + baseVersion + ".${buildQualifier}";
        } else {
            // 非 qualifier 版本，使用原始版本号
            return bundleInfo.getSymbolicName() + "_" + bundleInfo.getVersion();
        }
    }
}
