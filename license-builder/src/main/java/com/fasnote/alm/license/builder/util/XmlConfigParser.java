package com.fasnote.alm.license.builder.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Stream;

/**
 * XML配置文件解析器
 * 自动解析web.xml和hivemodule.xml等配置文件，提取需要保护的类名
 * 
 * 支持的配置文件类型：
 * - web.xml: servlet-class, filter-class, listener-class
 * - hivemodule.xml: construct class属性
 * - plugin.xml: class属性
 */
public class XmlConfigParser {
    
    private static final Logger logger = LoggerFactory.getLogger(XmlConfigParser.class);
    
    /**
     * XML配置信息
     */
    public static class XmlConfigInfo {
        private final Set<String> webXmlClasses = new LinkedHashSet<>();
        private final Set<String> hiveModuleClasses = new LinkedHashSet<>();
        private final Set<String> pluginXmlClasses = new LinkedHashSet<>();
        private final Set<String> allClasses = new LinkedHashSet<>();
        
        public Set<String> getWebXmlClasses() { return webXmlClasses; }
        public Set<String> getHiveModuleClasses() { return hiveModuleClasses; }
        public Set<String> getPluginXmlClasses() { return pluginXmlClasses; }
        public Set<String> getAllClasses() { return allClasses; }
        
        void addWebXmlClass(String className) {
            if (isValidClassName(className)) {
                webXmlClasses.add(className);
                allClasses.add(className);
            }
        }
        
        void addHiveModuleClass(String className) {
            if (isValidClassName(className)) {
                hiveModuleClasses.add(className);
                allClasses.add(className);
            }
        }
        
        void addPluginXmlClass(String className) {
            if (isValidClassName(className)) {
                pluginXmlClasses.add(className);
                allClasses.add(className);
            }
        }
        
        private boolean isValidClassName(String className) {
            return className != null && 
                   !className.trim().isEmpty() && 
                   className.contains(".") &&
                   !className.startsWith("java.") &&
                   !className.startsWith("javax.");
        }
    }
    
    /**
     * 扫描指定目录下的XML配置文件
     * 
     * @param baseDir 基础目录
     * @return XML配置信息
     */
    public static XmlConfigInfo scanXmlConfigs(Path baseDir) {
        XmlConfigInfo configInfo = new XmlConfigInfo();
        
        if (!Files.exists(baseDir) || !Files.isDirectory(baseDir)) {
            logger.warn("基础目录不存在或不是目录: {}", baseDir);
            return configInfo;
        }
        
        try (Stream<Path> paths = Files.walk(baseDir)) {
            paths.filter(Files::isRegularFile)
                 .filter(path -> isXmlConfigFile(path))
                 .forEach(path -> parseXmlFile(path, configInfo));
        } catch (IOException e) {
            logger.error("扫描XML配置文件失败: {}", baseDir, e);
        }
        
        logger.info("XML配置扫描完成，发现 {} 个需要保护的类", configInfo.getAllClasses().size());
        return configInfo;
    }
    
    /**
     * 判断是否为XML配置文件
     */
    private static boolean isXmlConfigFile(Path path) {
        String fileName = path.getFileName().toString().toLowerCase();
        return fileName.equals("web.xml") || 
               fileName.equals("hivemodule.xml") ||
               fileName.equals("plugin.xml");
    }
    
    /**
     * 解析XML文件
     */
    private static void parseXmlFile(Path xmlFile, XmlConfigInfo configInfo) {
        try {
            String fileName = xmlFile.getFileName().toString().toLowerCase();
            
            switch (fileName) {
                case "web.xml":
                    parseWebXml(xmlFile, configInfo);
                    break;
                case "hivemodule.xml":
                    parseHiveModuleXml(xmlFile, configInfo);
                    break;
                case "plugin.xml":
                    parsePluginXml(xmlFile, configInfo);
                    break;
                default:
                    logger.debug("跳过未知的XML文件: {}", xmlFile);
            }
            
        } catch (Exception e) {
            logger.warn("解析XML文件失败: {}", xmlFile, e);
        }
    }
    
    /**
     * 解析web.xml文件
     */
    private static void parseWebXml(Path webXmlFile, XmlConfigInfo configInfo) {
        try {
            Document doc = parseXmlDocument(webXmlFile.toFile());
            if (doc == null) return;
            
            // 解析servlet-class
            NodeList servletClasses = doc.getElementsByTagName("servlet-class");
            for (int i = 0; i < servletClasses.getLength(); i++) {
                String className = getTextContent(servletClasses.item(i));
                if (className != null) {
                    configInfo.addWebXmlClass(className);
                    logger.debug("发现servlet类: {}", className);
                }
            }
            
            // 解析filter-class
            NodeList filterClasses = doc.getElementsByTagName("filter-class");
            for (int i = 0; i < filterClasses.getLength(); i++) {
                String className = getTextContent(filterClasses.item(i));
                if (className != null) {
                    configInfo.addWebXmlClass(className);
                    logger.debug("发现filter类: {}", className);
                }
            }
            
            // 解析listener-class
            NodeList listenerClasses = doc.getElementsByTagName("listener-class");
            for (int i = 0; i < listenerClasses.getLength(); i++) {
                String className = getTextContent(listenerClasses.item(i));
                if (className != null) {
                    configInfo.addWebXmlClass(className);
                    logger.debug("发现listener类: {}", className);
                }
            }
            
            // 解析param-value中的配置类（如Spring配置类）
            NodeList paramValues = doc.getElementsByTagName("param-value");
            for (int i = 0; i < paramValues.getLength(); i++) {
                String value = getTextContent(paramValues.item(i));
                if (value != null && isLikelyClassName(value)) {
                    configInfo.addWebXmlClass(value);
                    logger.debug("发现配置类: {}", value);
                }
            }
            
            logger.debug("解析web.xml完成: {}", webXmlFile);
            
        } catch (Exception e) {
            logger.warn("解析web.xml失败: {}", webXmlFile, e);
        }
    }
    
    /**
     * 解析hivemodule.xml文件
     */
    private static void parseHiveModuleXml(Path hiveModuleFile, XmlConfigInfo configInfo) {
        try {
            Document doc = parseXmlDocument(hiveModuleFile.toFile());
            if (doc == null) return;
            
            // 解析construct标签的class属性
            NodeList constructElements = doc.getElementsByTagName("construct");
            for (int i = 0; i < constructElements.getLength(); i++) {
                Element element = (Element) constructElements.item(i);
                String className = element.getAttribute("class");
                if (className != null && !className.trim().isEmpty()) {
                    configInfo.addHiveModuleClass(className.trim());
                    logger.debug("发现HiveMind构造类: {}", className);
                }
            }
            
            logger.debug("解析hivemodule.xml完成: {}", hiveModuleFile);
            
        } catch (Exception e) {
            logger.warn("解析hivemodule.xml失败: {}", hiveModuleFile, e);
        }
    }
    
    /**
     * 解析plugin.xml文件
     */
    private static void parsePluginXml(Path pluginXmlFile, XmlConfigInfo configInfo) {
        try {
            Document doc = parseXmlDocument(pluginXmlFile.toFile());
            if (doc == null) return;
            
            // 解析所有带class属性的元素
            NodeList allElements = doc.getElementsByTagName("*");
            for (int i = 0; i < allElements.getLength(); i++) {
                if (allElements.item(i) instanceof Element) {
                    Element element = (Element) allElements.item(i);
                    String className = element.getAttribute("class");
                    if (className != null && !className.trim().isEmpty()) {
                        configInfo.addPluginXmlClass(className.trim());
                        logger.debug("发现plugin.xml类: {}", className);
                    }
                }
            }
            
            logger.debug("解析plugin.xml完成: {}", pluginXmlFile);
            
        } catch (Exception e) {
            logger.warn("解析plugin.xml失败: {}", pluginXmlFile, e);
        }
    }
    
    /**
     * 解析XML文档
     */
    private static Document parseXmlDocument(File xmlFile) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            factory.setValidating(false);
            factory.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
            
            DocumentBuilder builder = factory.newDocumentBuilder();
            return builder.parse(xmlFile);
            
        } catch (Exception e) {
            logger.warn("解析XML文档失败: {}", xmlFile, e);
            return null;
        }
    }
    
    /**
     * 获取节点文本内容
     */
    private static String getTextContent(Node node) {
        if (node == null) return null;
        String content = node.getTextContent();
        return content != null ? content.trim() : null;
    }
    
    /**
     * 判断字符串是否像类名
     */
    private static boolean isLikelyClassName(String value) {
        return value != null && 
               value.contains(".") && 
               Character.isUpperCase(value.charAt(value.lastIndexOf('.') + 1)) &&
               !value.startsWith("java.") &&
               !value.startsWith("javax.");
    }
}
