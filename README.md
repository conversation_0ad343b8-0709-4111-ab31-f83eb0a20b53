Polarion许可证构建项目
injection是许可证的依赖注入插件，用来管理许可证失效后自动降级等操作
license-crypto 许可证加密解密算法的实现，确保加密解密正常
license-builder用来打包 PDE 插件，打包使用 tycho，脚本为 build-plugin.sh，使用方式是
./scripts/build-plugin.sh ../../com.fasnote.alm.auth.feishu/ -m full -v
生成的许可证文件需要放到/opt/polarion/polarion/license/fasnote/目录下，jar 包放在/opt/polarion/polarion/extensions/exts/eclipse/plugins目录下
plugin-manage 许可证管理的实现，目前给 polarion 进行实现，支持 polarion 的机器码绑定

injection和plugin-manage都是 PDE 项目，需要使用license-builder进行打包，但是不需要使用完整模式，直接使用混淆模式即可
./scripts/build-plugin.sh ../../com.fasnote.alm.plugin.injection/ 