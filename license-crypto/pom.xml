<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.fasnote.alm</groupId>
    <artifactId>license-crypto</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <name>ALM License Crypto</name>
    <description>共享的许可证加解密模块</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- SLF4J API -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.36</version>
        </dependency>
        
        <!-- Logback for testing -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.2.12</version>
            <scope>test</scope>
        </dependency>
        
        <!-- Jackson for JSON parsing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.15.2</version>
        </dependency>

        <!-- JUnit for testing -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>


        </plugins>
    </build>

    <profiles>
        <!-- 自动更新plugin-manage依赖的profile -->
        <profile>
            <id>update-plugin-manage</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <!-- 自动复制JAR到plugin-manage模块并更新PDE配置 -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>3.1.0</version>
                        <executions>
                            <execution>
                                <id>update-plugin-manage-dependencies</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <!-- 定义变量 -->
                                        <property name="plugin.manage.dir" value="${project.basedir}/../plugin-manage"/>
                                        <property name="plugin.manage.lib.dir" value="${plugin.manage.dir}/lib"/>
                                        <property name="current.jar.name" value="${project.artifactId}-${project.version}.jar"/>
                                        <property name="current.jar.path" value="${project.build.directory}/${current.jar.name}"/>

                                        <!-- 检查plugin-manage目录是否存在 -->
                                        <available file="${plugin.manage.dir}" type="dir" property="plugin.manage.exists"/>
                                        <fail unless="plugin.manage.exists" message="plugin-manage目录不存在: ${plugin.manage.dir}"/>

                                        <!-- 检查必要的配置文件是否存在 -->
                                        <available file="${plugin.manage.dir}/META-INF/MANIFEST.MF" property="manifest.exists"/>
                                        <available file="${plugin.manage.dir}/.classpath" property="classpath.exists"/>
                                        <available file="${plugin.manage.dir}/build.properties" property="buildprops.exists"/>
                                        <fail unless="manifest.exists" message="MANIFEST.MF文件不存在: ${plugin.manage.dir}/META-INF/MANIFEST.MF"/>
                                        <fail unless="classpath.exists" message=".classpath文件不存在: ${plugin.manage.dir}/.classpath"/>
                                        <fail unless="buildprops.exists" message="build.properties文件不存在: ${plugin.manage.dir}/build.properties"/>

                                        <!-- 创建lib目录（如果不存在） -->
                                        <mkdir dir="${plugin.manage.lib.dir}"/>

                                        <!-- 检查当前构建的JAR文件是否存在 -->
                                        <available file="${current.jar.path}" property="current.jar.exists"/>
                                        <fail unless="current.jar.exists" message="构建的JAR文件不存在: ${current.jar.path}"/>

                                        <!-- 删除旧的JAR文件 -->
                                        <delete>
                                            <fileset dir="${plugin.manage.lib.dir}">
                                                <include name="${project.artifactId}-*.jar"/>
                                            </fileset>
                                        </delete>

                                        <!-- 复制新的JAR文件 -->
                                        <copy file="${current.jar.path}" todir="${plugin.manage.lib.dir}"/>
                                        <echo message="已复制 ${current.jar.name} 到 ${plugin.manage.lib.dir}"/>

                                        <!-- 创建配置文件备份 -->
                                        <tstamp>
                                            <format property="backup.timestamp" pattern="yyyyMMdd_HHmmss"/>
                                        </tstamp>
                                        <copy file="${plugin.manage.dir}/META-INF/MANIFEST.MF"
                                              tofile="${plugin.manage.dir}/META-INF/MANIFEST.MF.backup.${backup.timestamp}"/>
                                        <copy file="${plugin.manage.dir}/.classpath"
                                              tofile="${plugin.manage.dir}/.classpath.backup.${backup.timestamp}"/>
                                        <copy file="${plugin.manage.dir}/build.properties"
                                              tofile="${plugin.manage.dir}/build.properties.backup.${backup.timestamp}"/>
                                        <echo message="已创建配置文件备份（时间戳: ${backup.timestamp}）"/>

                                        <!-- 更新MANIFEST.MF文件中的Bundle-ClassPath -->
                                        <replaceregexp file="${plugin.manage.dir}/META-INF/MANIFEST.MF"
                                                     match="(Bundle-ClassPath:\s*)lib/${project.artifactId}-[^,\s]*\.jar"
                                                     replace="\1lib/${current.jar.name}"
                                                     flags="g"/>
                                        <echo message="已更新MANIFEST.MF中的Bundle-ClassPath"/>

                                        <!-- 更新.classpath文件中的JAR路径 -->
                                        <replaceregexp file="${plugin.manage.dir}/.classpath"
                                                     match='(path=")lib/${project.artifactId}-[^"]*\.jar(")'
                                                     replace='\1lib/${current.jar.name}\2'
                                                     flags="g"/>
                                        <echo message="已更新.classpath中的JAR路径"/>

                                        <!-- 更新build.properties文件中的JAR引用 -->
                                        <replaceregexp file="${plugin.manage.dir}/build.properties"
                                                     match="lib/${project.artifactId}-[^,\s]*\.jar"
                                                     replace="lib/${current.jar.name}"
                                                     flags="g"/>
                                        <echo message="已更新build.properties中的JAR引用"/>

                                        <!-- 验证更新结果 -->
                                        <available file="${plugin.manage.lib.dir}/${current.jar.name}" property="jar.copied"/>
                                        <fail unless="jar.copied" message="验证失败: JAR文件未成功复制到lib目录"/>

                                        <!-- 验证配置文件中的引用是否正确更新 -->
                                        <loadfile property="manifest.content" srcFile="${plugin.manage.dir}/META-INF/MANIFEST.MF"/>
                                        <loadfile property="classpath.content" srcFile="${plugin.manage.dir}/.classpath"/>
                                        <loadfile property="buildprops.content" srcFile="${plugin.manage.dir}/build.properties"/>

                                        <condition property="manifest.updated">
                                            <contains string="${manifest.content}" substring="lib/${current.jar.name}"/>
                                        </condition>
                                        <condition property="classpath.updated">
                                            <contains string="${classpath.content}" substring="lib/${current.jar.name}"/>
                                        </condition>
                                        <condition property="buildprops.updated">
                                            <contains string="${buildprops.content}" substring="lib/${current.jar.name}"/>
                                        </condition>

                                        <fail unless="manifest.updated" message="验证失败: MANIFEST.MF中未找到正确的JAR引用"/>
                                        <fail unless="classpath.updated" message="验证失败: .classpath中未找到正确的JAR引用"/>
                                        <fail unless="buildprops.updated" message="验证失败: build.properties中未找到正确的JAR引用"/>

                                        <echo message="✅ 验证通过: 所有配置文件已正确更新"/>

                                        <!-- 清理备份文件 -->
                                        <delete file="${plugin.manage.dir}/META-INF/MANIFEST.MF.backup.${backup.timestamp}"/>
                                        <delete file="${plugin.manage.dir}/.classpath.backup.${backup.timestamp}"/>
                                        <delete file="${plugin.manage.dir}/build.properties.backup.${backup.timestamp}"/>
                                        <echo message="🧹 已清理临时备份文件"/>

                                        <echo message="🎉 plugin-manage模块依赖更新完成！"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- 跳过自动更新的profile -->
        <profile>
            <id>skip-plugin-manage-update</id>
            <activation>
                <property>
                    <name>skipPluginManageUpdate</name>
                    <value>true</value>
                </property>
            </activation>
            <!-- 此profile为空，用于跳过自动更新 -->
        </profile>
    </profiles>
</project>
