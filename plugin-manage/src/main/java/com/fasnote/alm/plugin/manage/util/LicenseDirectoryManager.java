package com.fasnote.alm.plugin.manage.util;

import java.io.File;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import com.polarion.core.boot.PolarionProperties;
import com.polarion.core.config.Configuration;
import com.polarion.core.config.IConfiguration;
import com.polarion.core.util.logging.Logger;

/**
 * 许可证目录管理器
 *
 * 功能特性： 1. 自动检测集群环境 2. 优先使用共享许可证目录 3. 智能回退机制 4. 线程安全的目录缓存 5. 详细的日志记录
 *
 * 使用方式：
 *
 * <pre>
 * // 获取许可证目录
 * File licenseDir = LicenseDirectoryManager.getInstance().getLicenseDir();
 *
 * // 获取许可证文件路径
 * File licenseFile = LicenseDirectoryManager.getInstance().getLicenseFile("plugin-id.lic");
 *
 * // 检查是否为集群环境
 * boolean isCluster = LicenseDirectoryManager.getInstance().isClusterEnvironment();
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class LicenseDirectoryManager {

	private static final Logger log = Logger.getLogger(LicenseDirectoryManager.class);

	// 单例实例
	private static volatile LicenseDirectoryManager instance;

	// 系统属性键
	private static final String POLARION_LICENSE_DIR = "com.polarion.licenseDir";
	private static final String POLARION_SHARED = "com.polarion.shared";
	private static final String POLARION_HOME = "com.polarion.home";
	private static final String POLARION_NODE_ID = "com.polarion.nodeId";

	// 默认目录名
	private static final String DEFAULT_LICENSE_DIR_NAME = "license";
	private static final String FASNOTE_SUBDIR_NAME = "fasnote";

	/**
	 * 获取单例实例
	 *
	 * @return LicenseDirectoryManager实例
	 */
	public static LicenseDirectoryManager getInstance() {
		if (instance == null) {
			synchronized (LicenseDirectoryManager.class) {
				if (instance == null) {
					instance = new LicenseDirectoryManager();
				}
			}
		}
		return instance;
	}
	// 缓存和锁
	private volatile File cachedLicenseDir;
	private volatile Boolean cachedIsCluster;

	private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

	/**
	 * 私有构造函数，实现单例模式
	 */
	private LicenseDirectoryManager() {
		// 私有构造函数
	}

	/**
	 * 清除缓存，强制重新检测目录 在配置变更后可以调用此方法
	 */
	public void clearCache() {
		lock.writeLock().lock();
		try {
			cachedLicenseDir = null;
			cachedIsCluster = null;
			log.info("许可证目录缓存已清除");
		} finally {
			lock.writeLock().unlock();
		}
	}

	/**
	 * 检测是否为集群环境
	 */
	private boolean detectClusterEnvironment() {
		// 方法1：检查Polarion内置的集群检测
		try {
			if (PolarionProperties.isClusterNode() || PolarionProperties.isCoordinator()) {
				log.info("通过PolarionProperties检测到集群环境");
				return true;
			}
		} catch (Exception e) {
			log.debug("PolarionProperties集群检测失败: " + e.getMessage());
		}

		// 方法2：检查集群相关的系统属性
		String sharedPath = System.getProperty(POLARION_SHARED);
		String nodeId = System.getProperty(POLARION_NODE_ID);

		boolean hasSharedPath = sharedPath != null && !sharedPath.trim().isEmpty();
		boolean hasNodeId = nodeId != null && !nodeId.trim().isEmpty();

		if (hasSharedPath || hasNodeId) {
			log.info("通过系统属性检测到集群环境 (shared: " + hasSharedPath + ", nodeId: " + hasNodeId + ")");
			return true;
		}

		log.info("未检测到集群环境，使用单机模式");
		return false;
	}

	/**
	 * 确定许可证目录的具体实现
	 */
	private File determineLicenseDir() {
		log.info("开始确定许可证目录...");

		if (isClusterEnvironment()) {
			log.info("检测到集群环境，尝试使用集群许可证目录策略");
			File clusterDir = getClusterLicenseDir();
			if (clusterDir != null) {
				log.info("成功确定集群许可证目录: " + clusterDir.getAbsolutePath());
				return clusterDir;
			}
			log.warn("集群许可证目录不可用，回退到标准目录");
		}

		// 使用标准许可证目录
		File standardDir = getStandardLicenseDir();
		log.info("使用标准许可证目录: " + standardDir.getAbsolutePath());
		return standardDir;
	}

	/**
	 * 获取集群环境下的许可证目录
	 */
	private File getClusterLicenseDir() {
		// 尝试使用共享目录
		String sharedPath = System.getProperty(POLARION_SHARED);
		if (sharedPath != null && !sharedPath.trim().isEmpty()) {
			// 基础路径应该是: {shared}/polarion/license
			File sharedPolarionDir = new File(sharedPath.trim(), "polarion");
			File baseLicenseDir = new File(sharedPolarionDir, "license");

			// 添加fasnote子目录
			File sharedLicenseDir = new File(baseLicenseDir, "fasnote");

			if (isDirectoryUsable(sharedLicenseDir)) {
				log.info("使用共享许可证目录: " + sharedLicenseDir.getAbsolutePath());
				return sharedLicenseDir;
			} else {
				log.warn("共享许可证目录不可用: " + sharedLicenseDir.getAbsolutePath());

				// 尝试创建目录结构
				if (!baseLicenseDir.exists() && !baseLicenseDir.mkdirs()) {
					log.warn("无法创建基础许可证目录: " + baseLicenseDir.getAbsolutePath());
					return null;
				}

				if (!sharedLicenseDir.exists() && !sharedLicenseDir.mkdir()) {
					log.warn("无法创建fasnote子目录，将使用基础目录: " + baseLicenseDir.getAbsolutePath());
					return baseLicenseDir; // 如果无法创建子目录，则回退到基础目录
				}

				log.info("成功创建共享许可证目录: " + sharedLicenseDir.getAbsolutePath());
				return sharedLicenseDir;
			}
		}

		return null; // 返回null表示集群目录不可用
	}

	/**
	 * 获取许可证目录 集群环境下优先使用共享目录，单机环境使用标准目录
	 *
	 * @return 许可证目录
	 */
	public File getLicenseDir() {
		if (cachedLicenseDir == null) {
			lock.writeLock().lock();
			try {
				if (cachedLicenseDir == null) {
					cachedLicenseDir = determineLicenseDir();
				}
			} finally {
				lock.writeLock().unlock();
			}
		}

		lock.readLock().lock();
		try {
			return cachedLicenseDir;
		} finally {
			lock.readLock().unlock();
		}
	}

	/**
	 * 获取许可证目录信息的详细描述 用于调试和日志记录
	 *
	 * @return 目录信息描述
	 */
	public String getLicenseDirectoryInfo() {
		File licenseDir = getLicenseDir();
		boolean isCluster = isClusterEnvironment();

		StringBuilder info = new StringBuilder();
		info.append("许可证目录信息:\n");
		info.append("  - 目录路径: ").append(licenseDir.getAbsolutePath()).append("\n");
		info.append("  - 环境类型: ").append(isCluster ? "集群环境" : "单机环境").append("\n");
		info.append("  - 目录存在: ").append(licenseDir.exists()).append("\n");
		info.append("  - 目录可读: ").append(licenseDir.canRead()).append("\n");
		info.append("  - 目录可写: ").append(licenseDir.canWrite()).append("\n");

		// 添加系统属性信息
		info.append("  - 系统属性:\n");
		info.append("    * com.polarion.licenseDir: ").append(System.getProperty(POLARION_LICENSE_DIR)).append("\n");
		info.append("    * com.polarion.shared: ").append(System.getProperty(POLARION_SHARED)).append("\n");
		info.append("    * com.polarion.home: ").append(System.getProperty(POLARION_HOME)).append("\n");
		info.append("    * com.polarion.nodeId: ").append(System.getProperty(POLARION_NODE_ID)).append("\n");

		// 添加集群环境下的路径计算信息
		if (isCluster) {
			String sharedPath = System.getProperty(POLARION_SHARED);
			if (sharedPath != null && !sharedPath.trim().isEmpty()) {
				info.append("  - 集群路径计算:\n");
				info.append("    * 共享根目录: ").append(sharedPath).append("\n");
				info.append("    * 共享polarion目录: ").append(new File(sharedPath, "polarion").getAbsolutePath())
						.append("\n");
				File baseLicenseDir = new File(new File(sharedPath, "polarion"), DEFAULT_LICENSE_DIR_NAME);
				info.append("    * 共享基础许可证目录: ").append(baseLicenseDir.getAbsolutePath()).append("\n");
				info.append("    * 共享fasnote许可证目录: ")
						.append(new File(baseLicenseDir, FASNOTE_SUBDIR_NAME).getAbsolutePath()).append("\n");
			}
		}

		return info.toString();
	}

	/**
	 * 获取指定许可证文件的完整路径
	 *
	 * @param fileName 许可证文件名
	 * @return 许可证文件的完整路径
	 */
	public File getLicenseFile(String fileName) {
		if (fileName == null || fileName.trim().isEmpty()) {
			throw new IllegalArgumentException("许可证文件名不能为空");
		}
		return new File(getLicenseDir(), fileName.trim());
	}

	/**
	 * 获取标准许可证目录（兼容Polarion原生方式）
	 */
	private File getStandardLicenseDir() {
		File baseDir = null;

		// 方法1：使用Polarion Configuration
		try {
			IConfiguration config = Configuration.getInstance();
			File configLicenseDir = config.getLicenseDir();
			if (configLicenseDir != null) {
				log.info("通过Configuration获取基础许可证目录: " + configLicenseDir.getAbsolutePath());
				baseDir = configLicenseDir;
			}
		} catch (Exception e) {
			log.debug("通过Configuration获取许可证目录失败: " + e.getMessage());
		}

		// 方法2：直接读取系统属性
		if (baseDir == null) {
			String licenseDirPath = System.getProperty(POLARION_LICENSE_DIR);
			if (licenseDirPath != null && !licenseDirPath.trim().isEmpty()) {
				baseDir = new File(licenseDirPath.trim());
				log.info("通过系统属性获取基础许可证目录: " + baseDir.getAbsolutePath());
			}
		}

		// 方法3：使用默认路径
		if (baseDir == null) {
			String polarionHome = System.getProperty(POLARION_HOME);
			if (polarionHome != null && !polarionHome.trim().isEmpty()) {
				baseDir = new File(polarionHome.trim(), "license");
				log.warn("使用默认基础许可证目录: " + baseDir.getAbsolutePath());
			} else {
				// 无法确定许可证目录，抛出异常
				throw new RuntimeException("无法确定许可证目录，请检查系统配置");
			}
		}

		// 在基础目录下添加fasnote子目录
		File fasnoteDir = new File(baseDir, "fasnote");
		log.info("添加fasnote子目录，最终许可证目录: " + fasnoteDir.getAbsolutePath());

		// 确保目录存在
		if (!fasnoteDir.exists()) {
			boolean created = fasnoteDir.mkdirs();
			if (created) {
				log.info("成功创建fasnote子目录");
			} else {
				log.warn("无法创建fasnote子目录，将使用基础目录");
				return baseDir; // 如果无法创建子目录，则回退到基础目录
			}
		}

		return fasnoteDir;
	}

	/**
	 * 检查是否为集群环境
	 *
	 * @return true如果是集群环境，false如果是单机环境
	 */
	public boolean isClusterEnvironment() {
		if (cachedIsCluster == null) {
			lock.writeLock().lock();
			try {
				if (cachedIsCluster == null) {
					cachedIsCluster = detectClusterEnvironment();
				}
			} finally {
				lock.writeLock().unlock();
			}
		}

		lock.readLock().lock();
		try {
			return cachedIsCluster;
		} finally {
			lock.readLock().unlock();
		}
	}

	/**
	 * 检查目录是否可用（存在且可读写，或者可以创建）
	 */
	private boolean isDirectoryUsable(File dir) {
		if (dir.exists()) {
			return dir.isDirectory() && dir.canRead() && dir.canWrite();
		} else {
			// 尝试创建目录
			try {
				return dir.mkdirs();
			} catch (Exception e) {
				log.debug("无法创建目录 " + dir.getAbsolutePath() + ": " + e.getMessage());
				return false;
			}
		}
	}
}
