# 插件管理系统前端

基于Vue 3 + Element Plus的插件管理系统前端界面。

## 功能特性

- 📊 **仪表板** - 显示系统统计信息和快速操作
- 📋 **插件列表** - 查看所有已注册的插件及其状态
- 🔍 **插件详情** - 查看单个插件的详细许可证信息
- 🔄 **许可证管理** - 验证和更新插件许可证
- 📱 **响应式设计** - 适配不同屏幕尺寸

## 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **Element Plus** - Vue 3组件库
- **Vue Router** - 官方路由管理器
- **Axios** - HTTP客户端
- **Vite** - 现代前端构建工具

## 开发环境设置

### 前置要求

- Node.js 16+ 
- npm 或 yarn

### 安装依赖

```bash
cd frontend
npm install
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:3000

### 构建生产版本

```bash
npm run build
```

构建产物将输出到 `../webapp` 目录，可直接被Tomcat服务。

## 项目结构

```
frontend/
├── src/
│   ├── api/           # API接口定义
│   ├── components/    # 可复用组件
│   ├── views/         # 页面组件
│   ├── router/        # 路由配置
│   ├── App.vue        # 根组件
│   └── main.js        # 入口文件
├── public/            # 静态资源
├── package.json       # 项目配置
└── vite.config.js     # Vite配置
```

## API接口

前端通过以下API与后端通信：

- `GET /api/plugins` - 获取所有插件
- `GET /api/plugins/{id}/license` - 获取插件许可证信息
- `POST /api/plugins/{id}/license` - 更新插件许可证
- `GET /api/plugins/{id}/validate` - 验证插件许可证
- `GET /api/framework/statistics` - 获取框架统计信息
- `POST /api/framework/refresh` - 刷新所有许可证

## 部署说明

1. 运行 `npm run build` 构建生产版本
2. 构建产物自动输出到 `../webapp` 目录
3. 确保后端Spring MVC服务正常运行
4. 访问 `http://your-server/polarion/plugin-manage/`

## 开发注意事项

- 开发时使用代理转发API请求到后端
- 生产环境下前后端部署在同一域名下
- 支持热重载，修改代码后自动刷新页面
