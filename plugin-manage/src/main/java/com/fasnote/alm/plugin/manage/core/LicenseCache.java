package com.fasnote.alm.plugin.manage.core;

import java.util.Map;

import com.fasnote.alm.plugin.manage.model.PluginLicense;

/**
 * 许可证缓存数据结构 包含解析后的许可证元数据和解密后的类数据
 */
public class LicenseCache {

	private final String pluginId;
	private final String licenseFilePath;
	private final PluginLicense licenseMetadata;
	private final Map<String, byte[]> decryptedClasses;
	private final long lastModified;
	private final long cacheTime;

	public LicenseCache(String pluginId, String licenseFilePath, PluginLicense licenseMetadata,
			Map<String, byte[]> decryptedClasses, long lastModified) {
		this.pluginId = pluginId;
		this.licenseFilePath = licenseFilePath;
		this.licenseMetadata = licenseMetadata;
		this.decryptedClasses = decryptedClasses;
		this.lastModified = lastModified;
		this.cacheTime = System.currentTimeMillis();
	}

	public long getCacheTime() {
		return cacheTime;
	}

	public Map<String, byte[]> getDecryptedClasses() {
		return decryptedClasses;
	}

	public long getLastModified() {
		return lastModified;
	}

	public String getLicenseFilePath() {
		return licenseFilePath;
	}

	public PluginLicense getLicenseMetadata() {
		return licenseMetadata;
	}

	// Getters
	public String getPluginId() {
		return pluginId;
	}

	/**
	 * 检查许可证是否包含加密类
	 */
	public boolean hasEncryptedClasses() {
		return decryptedClasses != null && !decryptedClasses.isEmpty();
	}

	/**
	 * 检查缓存是否仍然有效
	 */
	public boolean isValid() {
		if (licenseFilePath == null) {
			return true; // 内存中的许可证始终有效
		}

		java.io.File file = new java.io.File(licenseFilePath);
		return file.exists() && file.lastModified() == lastModified;
	}

	@Override
	public String toString() {
		return String.format("LicenseCache{pluginId='%s', filePath='%s', hasClasses=%s, cacheTime=%d}", pluginId,
				licenseFilePath, hasEncryptedClasses(), cacheTime);
	}
}
