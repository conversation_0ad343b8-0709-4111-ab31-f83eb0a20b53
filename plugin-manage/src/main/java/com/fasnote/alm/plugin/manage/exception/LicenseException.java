package com.fasnote.alm.plugin.manage.exception;

/**
 * 许可证异常基类 所有许可证相关的异常都继承自此类
 */
public class LicenseException extends RuntimeException {

	private static final long serialVersionUID = 1L;

	private String errorCode;
	private Object[] parameters;

	/**
	 * 构造函数
	 *
	 * @param message 异常消息
	 */
	public LicenseException(String message) {
		super(message);
	}

	/**
	 * 构造函数
	 *
	 * @param message   异常消息
	 * @param errorCode 错误码
	 */
	public LicenseException(String message, String errorCode) {
		super(message);
		this.errorCode = errorCode;
	}

	/**
	 * 构造函数
	 *
	 * @param message    异常消息
	 * @param errorCode  错误码
	 * @param parameters 参数
	 */
	public LicenseException(String message, String errorCode, Object... parameters) {
		super(message);
		this.errorCode = errorCode;
		this.parameters = parameters;
	}

	/**
	 * 构造函数
	 *
	 * @param message   异常消息
	 * @param errorCode 错误码
	 * @param cause     原因异常
	 */
	public LicenseException(String message, String errorCode, Throwable cause) {
		super(message, cause);
		this.errorCode = errorCode;
	}

	/**
	 * 构造函数
	 *
	 * @param message 异常消息
	 * @param cause   原因异常
	 */
	public LicenseException(String message, Throwable cause) {
		super(message, cause);
	}

	/**
	 * 获取错误码
	 *
	 * @return 错误码
	 */
	public String getErrorCode() {
		return errorCode;
	}

	/**
	 * 获取格式化的错误消息
	 *
	 * @return 格式化的错误消息
	 */
	public String getFormattedMessage() {
		StringBuilder sb = new StringBuilder();
		sb.append(getMessage());

		if (errorCode != null) {
			sb.append(" [错误码: ").append(errorCode).append("]");
		}

		if (parameters != null && parameters.length > 0) {
			sb.append(" [参数: ");
			for (int i = 0; i < parameters.length; i++) {
				if (i > 0) {
					sb.append(", ");
				}
				sb.append(parameters[i]);
			}
			sb.append("]");
		}

		return sb.toString();
	}

	/**
	 * 获取参数
	 *
	 * @return 参数数组
	 */
	public Object[] getParameters() {
		return parameters;
	}

	/**
	 * 设置错误码
	 *
	 * @param errorCode 错误码
	 */
	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	/**
	 * 设置参数
	 *
	 * @param parameters 参数数组
	 */
	public void setParameters(Object... parameters) {
		this.parameters = parameters;
	}

	@Override
	public String toString() {
		return getClass().getSimpleName() + ": " + getFormattedMessage();
	}
}
