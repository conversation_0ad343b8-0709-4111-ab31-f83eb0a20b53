package com.fasnote.alm.plugin.manage.test;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Properties;

import com.fasnote.alm.plugin.manage.model.PluginLicense;

/**
 * 测试工具类 提供创建测试对象和数据的便捷方法
 */
public class TestUtils {

	/**
	 * 清理测试环境的系统属性
	 */
	public static void cleanupTestSystemProperties() {
		System.clearProperty("license.test.mode");
		System.clearProperty("license.development.mode");
		System.clearProperty("license.skip.machine.binding");
		System.clearProperty("license.skip.signature.validation");
	}

	/**
	 * 创建测试配置文件
	 */
	public static File createTestConfigFile(File directory, Properties properties) throws IOException {
		File configFile = new File(directory, "license-config.properties");
		try (FileOutputStream fos = new FileOutputStream(configFile)) {
			properties.store(fos, "Test Configuration");
		}
		return configFile;
	}

	/**
	 * 创建测试用的加密许可证数据
	 */
	public static byte[] createTestEncryptedLicenseData(String pluginId, boolean expired) throws Exception {
		String licenseJson = createTestLicenseJson(pluginId, expired);
		return licenseJson.getBytes("UTF-8");
	}

	/**
	 * 创建测试用的PluginLicense
	 */
	public static PluginLicense createTestLicense(String pluginId, boolean expired) {
		// 创建许可证JSON数据
		LocalDateTime currentTime = LocalDateTime.now();
		LocalDateTime expiryTime = expired ? currentTime.minusDays(1) : currentTime.plusYears(1);

		String licenseJson = String.format(
				"{\n" + "  \"pluginId\": \"%s\",\n" + "  \"productName\": \"Test Product\",\n"
						+ "  \"licenseType\": \"COMMERCIAL\",\n" + "  \"expiryDate\": \"%s\",\n"
						+ "  \"maxUsers\": 100,\n" + "  \"machineCode\": \"TEST_MACHINE_CODE\"\n" + "}",
				pluginId, expiryTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

		// 使用正确的构造函数
		PluginLicense license = new PluginLicense(pluginId, licenseJson);

		// 设置过期时间
		license.setExpiryDate(expiryTime);

		// 设置机器码
		license.setMachineCode("TEST_MACHINE_CODE");

		return license;
	}

	/**
	 * 创建测试许可证文件
	 */
	public static File createTestLicenseFile(File directory, String pluginId, String content) throws IOException {
		File licenseFile = new File(directory, pluginId + ".license");
		try (FileOutputStream fos = new FileOutputStream(licenseFile)) {
			fos.write(content.getBytes("UTF-8"));
		}
		return licenseFile;
	}

	/**
	 * 创建测试用的许可证JSON数据
	 */
	public static String createTestLicenseJson(String pluginId, boolean expired) {
		LocalDateTime currentTime = LocalDateTime.now();
		LocalDateTime expiryTime = expired ? currentTime.minusDays(1) : currentTime.plusYears(1);

		return String.format(
				"{\n" + "  \"pluginId\": \"%s\",\n" + "  \"productName\": \"Test Product\",\n"
						+ "  \"licenseType\": \"COMMERCIAL\",\n" + "  \"expiryDate\": \"%s\",\n"
						+ "  \"maxUsers\": 100,\n" + "  \"machineCode\": \"TEST_MACHINE_CODE\",\n"
						+ "  \"features\": {\"FEATURE_A\": true, \"FEATURE_B\": true},\n"
						+ "  \"signature\": \"TEST_SIGNATURE\"\n" + "}",
				pluginId, expiryTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
	}

	/**
	 * 创建测试用的Properties配置
	 */
	public static Properties createTestProperties(File tempDir) {
		Properties props = new Properties();
		props.setProperty("license.directory", tempDir.getAbsolutePath());
		props.setProperty("license.encryption.enabled", "false");
		props.setProperty("license.machine.binding.enabled", "false");
		props.setProperty("license.signature.validation.enabled", "false");
		props.setProperty("license.cache.size", "100");
		props.setProperty("license.cache.ttl", "3600");
		props.setProperty("license.audit.enabled", "true");
		props.setProperty("license.check.interval", "300");
		return props;
	}

	/**
	 * 创建测试用的RSA密钥对
	 */
	public static KeyPair generateTestKeyPair() throws Exception {
		KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
		keyGen.initialize(2048);
		return keyGen.generateKeyPair();
	}

	/**
	 * 获取简单类名
	 */
	public static String getSimpleClassName(String fullClassName) {
		int lastDotIndex = fullClassName.lastIndexOf('.');
		return lastDotIndex >= 0 ? fullClassName.substring(lastDotIndex + 1) : fullClassName;
	}

	/**
	 * 打印测试环境信息
	 */
	public static void printTestEnvironmentInfo() {
		System.out.println("=== 测试环境信息 ===");
		System.out.println("Java版本: " + System.getProperty("java.version"));
		System.out.println("操作系统: " + System.getProperty("os.name"));
		System.out.println("工作目录: " + System.getProperty("user.dir"));
		System.out.println("测试模式: " + System.getProperty("license.test.mode", "false"));
		System.out.println("开发模式: " + System.getProperty("license.development.mode", "false"));
		System.out.println("==================");
	}

	/**
	 * 创建测试环境的系统属性
	 */
	public static void setupTestSystemProperties() {
		System.setProperty("license.test.mode", "true");
		System.setProperty("license.development.mode", "true");
		System.setProperty("license.skip.machine.binding", "true");
		System.setProperty("license.skip.signature.validation", "true");
	}

	/**
	 * 验证核心类是否存在
	 */
	public static boolean validateCoreClasses() {
		String[] coreClasses = { "com.fasnote.alm.plugin.manage.core.LicenseManager",
				"com.fasnote.alm.plugin.manage.injection.LicenseValidatorImpl",
				"com.fasnote.alm.plugin.manage.config.LicenseConfiguration",
				"com.fasnote.alm.plugin.manage.model.PluginLicense",
				"com.fasnote.alm.plugin.manage.model.ValidationResult" };

		int existingClasses = 0;
		for (String className : coreClasses) {
			try {
				Class.forName(className);
				existingClasses++;
			} catch (ClassNotFoundException e) {
				System.out.println("核心类不存在: " + className);
			}
		}

		return existingClasses == coreClasses.length;
	}
}
