            <!-- License Builder Plugin -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>license-processing</id>
                        <phase>package</phase>
                        <goals>
                            <goal>java</goal>
                        </goals>
                        <configuration>
                            <mainClass>com.fasnote.alm.license.builder.LicenseBuilderMain</mainClass>
                            <args>
                                <arg>--input</arg>
                                {{#if enable_obfuscation}}
                                <arg>${project.build.directory}/${project.build.finalName}-obfuscated.jar</arg>
                                {{else}}
                                <arg>${project.build.directory}/${project.build.finalName}.jar</arg>
                                {{/if}}
                                <arg>--output</arg>
                                <arg>${project.build.directory}/${project.build.finalName}-licensed.jar</arg>
                                <arg>--config</arg>
                                <arg>${license.builder.config}</arg>
                                <arg>--validity</arg>
                                <arg>${license_validity}</arg>
                                <arg>--machine-binding</arg>
                                <arg>${machine_binding_enabled}</arg>
                            </args>
                            <classpathScope>runtime</classpathScope>
                            <additionalClasspathElements>
                                <additionalClasspathElement>${license.builder.jar}</additionalClasspathElement>
                            </additionalClasspathElements>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
