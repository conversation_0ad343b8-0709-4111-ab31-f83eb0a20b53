<!-- Maven JAR签名插件配置 -->
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-jarsigner-plugin</artifactId>
    <version>3.0.0</version>
    <executions>
        <execution>
            <id>sign</id>
            <goals>
                <goal>sign</goal>
            </goals>
            <phase>install</phase>
            <configuration>
                <keystore>${signing_keystore_path}</keystore>
                <alias>${signing_alias}</alias>
                <storepass>${signing_storepass}</storepass>
                <keypass>${signing_keypass}</keypass>
                <verbose>${signing_verbose}</verbose>
                <!-- 直接指定要签名的混淆后JAR文件 -->
                <archive>${project.build.directory}/${project.build.finalName}-obfuscated.jar</archive>
                <arguments>
                    <argument>-tsa</argument>
                    <argument>${signing_tsa_url}</argument>
                </arguments>
            </configuration>
        </execution>
        <execution>
            <id>verify</id>
            <goals>
                <goal>verify</goal>
            </goals>
            <phase>install</phase>
            <configuration>
                <archive>${project.build.directory}/${project.build.finalName}-obfuscated.jar</archive>
                <verbose>${signing_verbose}</verbose>
            </configuration>
        </execution>
    </executions>
</plugin>
