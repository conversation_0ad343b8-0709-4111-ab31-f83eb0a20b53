# 增强注解使用指南

## 概述

通过组合注解技术，现在 `@LicenseImplementation` 和 `@FallbackImplementation` 注解自动包含了 `@Service` 注解的功能，业务方只需要使用一个注解即可完成服务注册和许可证管理。

## 核心改进

### ✅ 组合注解设计
- `@LicenseImplementation` 自动包含 `@Service` 功能
- `@FallbackImplementation` 自动包含 `@Service` 功能
- 业务方只需要写一个注解，无需手动添加 `@Service`

### ✅ 统一扫描机制
- 移除重复的扫描逻辑
- DI 框架统一管理所有服务
- 通过拦截器实现许可证感知的服务选择

## 使用方式

### 1. 回退实现（简化版）

```java
@FallbackImplementation(
    value = IFeishuAuthenticatorEnhancer.class,
    description = "飞书认证增强默认实现",
    priority = 100
)
public class FeishuDefaultAuthenticatorEnhancer implements IFeishuAuthenticatorEnhancer {
    // 实现代码
}
```

### 2. 许可证实现（简化版）

```java
@LicenseImplementation(
    level = "PREMIUM", 
    description = "飞书认证增强功能", 
    premium = true
)
public class FeishuLicensedAuthenticatorEnhancer implements IFeishuAuthenticatorEnhancer {
    // 实现代码
}
```

### 3. 高级配置（可选）

```java
@FallbackImplementation(
    value = IMyService.class,
    description = "我的服务回退实现",
    priority = 100,
    interfaces = {IMyService.class, IAdditionalInterface.class},
    serviceName = "myFallbackService",
    serviceScope = Service.Scope.PROTOTYPE
)
public class MyFallbackService implements IMyService, IAdditionalInterface {
    // 实现代码
}
```

## 注解属性说明

### @FallbackImplementation 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `value` | `Class<?>` | 必填 | 服务接口类型 |
| `description` | `String` | `""` | 功能描述 |
| `name` | `String` | `""` | 服务名称（旧版本兼容） |
| `priority` | `int` | `100` | 优先级（数字越小优先级越高） |
| `interfaces` | `Class<?>[]` | `{}` | 服务接口类型数组 |
| `serviceName` | `String` | `""` | 服务名称（新版本） |
| `serviceScope` | `Service.Scope` | `SINGLETON` | 生命周期 |

### @LicenseImplementation 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `description` | `String` | `""` | 功能描述 |
| `level` | `String` | `"STANDARD"` | 许可证级别 |
| `premium` | `boolean` | `false` | 是否为高级功能 |
| `interfaces` | `Class<?>[]` | `{}` | 服务接口类型数组 |
| `serviceName` | `String` | `""` | 服务名称 |
| `scope` | `Service.Scope` | `SINGLETON` | 生命周期 |

## 工作原理

### 1. 注解处理
- `LicenseAnnotationProcessor` 处理组合注解
- 自动提取 `@Service` 相关配置
- 转换为统一的服务配置信息

### 2. 服务选择
- `LicenseAwareServiceInterceptor` 拦截服务创建
- 根据许可证状态选择合适的实现
- 支持优先级排序和智能选择

### 3. 生命周期管理
- 由 DI 容器统一管理实例生命周期
- 支持单例和原型模式
- 自动依赖注入

## 迁移指南

### 从旧版本迁移

**旧版本（需要两个注解）：**
```java
@Service(interfaces = {IMyService.class})
@FallbackImplementation(value = IMyService.class, priority = 100)
public class MyFallbackService implements IMyService { }
```

**新版本（只需一个注解）：**
```java
@FallbackImplementation(value = IMyService.class, priority = 100)
public class MyFallbackService implements IMyService { }
```

### 兼容性说明

- 现有代码无需修改，保持向后兼容
- 新代码建议使用组合注解方式
- 逐步迁移，降低风险

## 优势总结

1. **简化使用** - 业务方只需要一个注解
2. **统一管理** - DI 容器统一管理所有服务
3. **智能选择** - 根据许可证状态自动选择实现
4. **扩展性强** - 支持更多 DI 框架特性
5. **向后兼容** - 现有代码无需修改
