package com.fasnote.alm.license.builder.validation.validators;

import com.fasnote.alm.license.builder.model.BundleInfo;
import com.fasnote.alm.license.builder.validation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.Set;
import java.util.jar.JarFile;
import java.util.jar.Manifest;
import java.util.jar.Attributes;

/**
 * 依赖解析验证器
 * 验证插件的依赖关系是否正确配置和解析
 */
public class DependencyResolutionValidator implements PhaseAwareValidator {
    
    private static final Logger logger = LoggerFactory.getLogger(DependencyResolutionValidator.class);
    
    @Override
    public String getName() {
        return "DEPENDENCY_RESOLUTION";
    }
    
    @Override
    public Set<BuildPhase> getSupportedPhases() {
        return Set.of(BuildPhase.POST_OBFUSCATION);
    }
    
    @Override
    public Set<PluginType> getSupportedPluginTypes() {
        return Set.of(PluginType.ALL);
    }
    
    @Override
    public int getPriority() {
        return 150; // 中高优先级
    }
    
    @Override
    public boolean isApplicable(ValidationContext context) {
        // 对所有构建都适用
        return true;
    }
    
    @Override
    public String getDescription() {
        return "依赖解析验证器 - 验证插件的依赖关系配置";
    }
    
    @Override
    public ValidationResult validate(BundleInfo bundleInfo, ValidationContext context) {
        logger.info("开始执行依赖解析验证...");
        
        try {
            ValidationResult.Builder resultBuilder = ValidationResult.builder()
                    .validatorName(getName());
            
            // 验证Bundle信息
            if (bundleInfo == null) {
                return resultBuilder
                        .success(false)
                        .message("Bundle信息为空")
                        .build();
            }
            
            // 获取JAR文件路径
            String jarPath = getJarPath(context);
            if (jarPath == null) {
                return resultBuilder
                        .success(false)
                        .message("未指定JAR文件路径")
                        .build();
            }
            
            File jarFile = new File(jarPath);
            if (!jarFile.exists()) {
                return resultBuilder
                        .success(false)
                        .message("JAR文件不存在: " + jarPath)
                        .build();
            }
            
            // 验证Manifest中的依赖信息
            ValidationResult manifestValidation = validateManifestDependencies(jarFile, bundleInfo, resultBuilder);
            if (!manifestValidation.isSuccess()) {
                return manifestValidation;
            }
            
            // 验证Bundle信息中的依赖
            ValidationResult bundleValidation = validateBundleDependencies(bundleInfo, resultBuilder);
            if (!bundleValidation.isSuccess()) {
                return bundleValidation;
            }
            
            logger.info("依赖解析验证通过");
            return resultBuilder
                    .success(true)
                    .message("依赖解析验证通过")
                    .build();
            
        } catch (Exception e) {
            logger.error("依赖解析验证失败", e);
            return ValidationResult.failure(getName(), "依赖解析验证过程中发生异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取JAR文件路径
     */
    private String getJarPath(ValidationContext context) {
        // 优先从属性中获取
        String jarPath = context.getProperty("inputJar", String.class);
        if (jarPath != null) {
            return jarPath;
        }
        
        // 从配置中获取
        if (context.getConfiguration() != null && 
            context.getConfiguration().getInput() != null) {
            return context.getConfiguration().getInput().getSourceJar();
        }
        
        return null;
    }
    
    /**
     * 验证Manifest中的依赖信息
     */
    private ValidationResult validateManifestDependencies(File jarFile, BundleInfo bundleInfo, 
                                                         ValidationResult.Builder resultBuilder) {
        try (JarFile jar = new JarFile(jarFile)) {
            Manifest manifest = jar.getManifest();
            if (manifest == null) {
                return resultBuilder
                        .success(false)
                        .message("JAR文件缺少Manifest文件")
                        .build();
            }
            
            Attributes mainAttributes = manifest.getMainAttributes();
            
            // 检查Bundle-SymbolicName
            String bundleSymbolicName = mainAttributes.getValue("Bundle-SymbolicName");
            if (bundleSymbolicName == null || bundleSymbolicName.trim().isEmpty()) {
                return resultBuilder
                        .success(false)
                        .message("Manifest中缺少Bundle-SymbolicName")
                        .build();
            }
            
            // 验证Bundle-SymbolicName与BundleInfo一致性
            String expectedSymbolicName = bundleInfo.getSymbolicName();
            if (expectedSymbolicName != null && !bundleSymbolicName.contains(expectedSymbolicName)) {
                resultBuilder.warning("Bundle-SymbolicName与Bundle信息不完全匹配: " +
                                    bundleSymbolicName + " vs " + expectedSymbolicName);
            }
            
            resultBuilder.message("Bundle-SymbolicName: " + bundleSymbolicName);
            
            // 检查Bundle-Version
            String bundleVersion = mainAttributes.getValue("Bundle-Version");
            if (bundleVersion == null || bundleVersion.trim().isEmpty()) {
                resultBuilder.warning("Manifest中缺少Bundle-Version");
            } else {
                resultBuilder.message("Bundle-Version: " + bundleVersion);
            }
            
            // 检查Require-Bundle
            String requireBundle = mainAttributes.getValue("Require-Bundle");
            if (requireBundle != null && !requireBundle.trim().isEmpty()) {
                resultBuilder.message("Require-Bundle: " + requireBundle);
                
                // 检查是否包含必要的依赖
                if (!requireBundle.contains("org.eclipse.core.runtime")) {
                    resultBuilder.warning("缺少org.eclipse.core.runtime依赖");
                }
            } else {
                resultBuilder.warning("Manifest中没有Require-Bundle配置");
            }
            
            // 检查Import-Package
            String importPackage = mainAttributes.getValue("Import-Package");
            if (importPackage != null && !importPackage.trim().isEmpty()) {
                resultBuilder.message("Import-Package配置存在");
            }
            
            return resultBuilder.success(true).build();
            
        } catch (IOException e) {
            return resultBuilder
                    .success(false)
                    .message("读取Manifest文件时发生错误: " + e.getMessage())
                    .exception(e)
                    .build();
        }
    }
    
    /**
     * 验证Bundle信息中的依赖
     */
    private ValidationResult validateBundleDependencies(BundleInfo bundleInfo, 
                                                       ValidationResult.Builder resultBuilder) {
        try {
            // 检查Bundle SymbolicName
            String symbolicName = bundleInfo.getSymbolicName();
            if (symbolicName == null || symbolicName.trim().isEmpty()) {
                return resultBuilder
                        .success(false)
                        .message("Bundle SymbolicName为空")
                        .build();
            }

            resultBuilder.message("Bundle SymbolicName: " + symbolicName);
            
            // 检查Bundle版本
            String version = bundleInfo.getVersion();
            if (version == null || version.trim().isEmpty()) {
                resultBuilder.warning("Bundle版本信息为空");
            } else {
                resultBuilder.message("Bundle版本: " + version);
            }
            
            return resultBuilder.success(true).build();
            
        } catch (Exception e) {
            return resultBuilder
                    .success(false)
                    .message("验证Bundle依赖信息时发生错误: " + e.getMessage())
                    .exception(e)
                    .build();
        }
    }
}
