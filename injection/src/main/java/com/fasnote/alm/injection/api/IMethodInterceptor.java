package com.fasnote.alm.injection.api;

import java.lang.reflect.Method;

/**
 * 方法拦截器接口
 * 
 * 扩展服务拦截器，支持方法级别的拦截
 * 在OSGi环境下提供AOP功能
 */
public interface IMethodInterceptor {
    
    /**
     * 是否应该拦截指定的方法
     * 
     * @param target 目标对象
     * @param method 被调用的方法
     * @param args 方法参数
     * @return true表示需要拦截
     */
    boolean shouldInterceptMethod(Object target, Method method, Object[] args);
    
    /**
     * 方法调用前拦截
     * 
     * @param target 目标对象
     * @param method 被调用的方法
     * @param args 方法参数
     * @return 拦截结果，如果返回非null则直接返回该值，不执行原方法
     */
    default Object beforeMethodInvocation(Object target, Method method, Object[] args) {
        return null;
    }
    
    /**
     * 方法调用后拦截
     * 
     * @param target 目标对象
     * @param method 被调用的方法
     * @param args 方法参数
     * @param result 方法执行结果
     * @return 处理后的结果
     */
    default Object afterMethodInvocation(Object target, Method method, Object[] args, Object result) {
        return result;
    }
    
    /**
     * 方法调用异常拦截
     * 
     * @param target 目标对象
     * @param method 被调用的方法
     * @param args 方法参数
     * @param exception 抛出的异常
     * @return 处理后的异常，如果返回null则不抛出异常
     */
    default Throwable onMethodException(Object target, Method method, Object[] args, Throwable exception) {
        return exception;
    }
    
    /**
     * 方法拦截器优先级（数字越小优先级越高）
     *
     * @return 方法拦截优先级
     */
    default int getMethodInterceptorPriority() {
        return 100;
    }
}
