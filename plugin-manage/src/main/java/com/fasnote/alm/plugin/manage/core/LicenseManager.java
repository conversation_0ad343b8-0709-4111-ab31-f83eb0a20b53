package com.fasnote.alm.plugin.manage.core;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import org.osgi.framework.Bundle;
import org.osgi.framework.BundleContext;
import org.osgi.framework.wiring.BundleWiring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.license.crypto.RSAKeyManager;
import com.fasnote.alm.license.crypto.RSALicenseEncryption;
import com.fasnote.alm.plugin.manage.api.LicenseAware;
import com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader;
import com.fasnote.alm.plugin.manage.model.LicenseInfo;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.registry.LicenseServiceRegistry;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;
import com.fasnote.alm.plugin.manage.util.LicenseDirectoryManager;

/**
 * 运行时许可证管理器 负责运行时的许可证验证和服务管理
 *
 * 核心功能： 1. 许可证验证和缓存 2. 运行时服务注入 3. 安全验证（机器码绑定、过期时间） 4. 许可证状态监控
 */
public class LicenseManager {

	private static final Logger logger = LoggerFactory.getLogger(LicenseManager.class);

	// 已加载的插件许可证缓存
	private final Map<String, PluginLicense> pluginLicenses = new ConcurrentHashMap<>();

	// 许可证验证器
	private SecurityValidator securityValidator;

	// 加密类加载器映射（插件ID -> 类加载器）
	private final Map<String, EncryptedClassLoader> classLoaders = new ConcurrentHashMap<>();

	// OSGi Bundle上下文
	private BundleContext bundleContext;

	// 统一许可证处理器
	private final UnifiedLicenseProcessor licenseProcessor;

	// 许可证服务注册表
	private final LicenseServiceRegistry serviceRegistry;

	// 初始化状态标志
	private volatile boolean initialized = false;

	/**
	 * 构造函数（依赖注入）
	 *
	 * @param bundleContext OSGi Bundle上下文（必填）
	 */
	public LicenseManager(BundleContext bundleContext) {
		if (bundleContext == null) {
			throw new IllegalArgumentException("BundleContext不能为null");
		}

		this.bundleContext = bundleContext;
		this.licenseProcessor = new UnifiedLicenseProcessor();
		this.serviceRegistry = new LicenseServiceRegistry();

		SecurityValidator validator = new SecurityValidator();
		try {
			validator.initialize();
		} catch (Exception e) {
			logger.error("SecurityValidator 初始化失败", e);
		}
		this.securityValidator = validator;

		logger.info("LicenseManager创建完成，BundleContext: {}", bundleContext.getBundle().getSymbolicName());

		// 立即初始化，因为BundleContext已经可用
		initialize();
	}

	/**
	 * 构造函数（支持依赖注入）
	 *
	 * @param securityValidator 安全验证器
	 */
	public LicenseManager(SecurityValidator securityValidator) {
		this.securityValidator = securityValidator;
		this.licenseProcessor = new UnifiedLicenseProcessor();
		this.serviceRegistry = new LicenseServiceRegistry();

		// 延迟初始化，等待BundleContext设置后再初始化
		logger.info("LicenseManager创建完成（带参数），等待BundleContext设置后初始化");
	}

	/**
	 * 激活插件许可证（加载实现类）
	 *
	 * @param pluginId 插件ID
	 * @return 激活结果
	 */
	public ValidationResult activatePluginLicense(String pluginId) {
		logger.info("激活插件许可证: {}", pluginId);

		try {
			Optional<PluginLicense> licenseOpt = getPluginLicense(pluginId);
			if (!licenseOpt.isPresent()) {
				return ValidationResult.failure("许可证不存在: " + pluginId);
			}

			PluginLicense license = licenseOpt.get();

			// 验证许可证
			ValidationResult validationResult = validateLicense(license);
			if (!validationResult.isValid()) {
				return validationResult;
			}

			// 设置运行时环境（加载实现类）
			// 优先使用缓存中的数据
			LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
			if (cache != null) {
				setupRuntimeEnvironment(pluginId, cache);
			} else {
				// 如果缓存中没有，使用原始数据（兼容性处理）
				setupRuntimeEnvironment(pluginId, license.getRawLicenseData());
			}

			logger.info("插件许可证激活成功: {}", pluginId);
			return ValidationResult.success("许可证激活成功");

		} catch (Exception e) {
			String errorMessage = "许可证激活失败: " + e.getMessage();
			logger.error(errorMessage, e);
			return ValidationResult.failure(errorMessage);
		}
	}

	/**
	 * 自动扫描并预加载许可证文件（仅解密验证，不加载实现类）
	 */
	private void autoScanAndLoadLicenses() {
		try {
			logger.info("开始自动扫描许可证文件");

			// 获取许可证目录
			java.io.File licenseDir = LicenseDirectoryManager.getInstance().getLicenseDir();

			// 扫描许可证文件
			LicenseScanner scanner = new LicenseScanner(licenseDir);
			Map<String, String> licenseFiles = scanner.scanLicenseFiles();

			// 预加载每个许可证文件（仅解密验证，不加载实现类）
			for (Map.Entry<String, String> entry : licenseFiles.entrySet()) {
				String pluginId = entry.getKey();
				String licenseFilePath = entry.getValue();

				try {
					preloadLicenseFromFile(pluginId, licenseFilePath);
				} catch (Exception e) {
					logger.error("预加载许可证文件失败: {}", licenseFilePath, e);
				}
			}

			logger.info("自动扫描许可证文件完成，共预加载 {} 个许可证", licenseFiles.size());

		} catch (Exception e) {
			logger.error("自动扫描许可证文件失败", e);
		}
	}

	/**
	 * 清理资源
	 */
	public void cleanup() {
		logger.info("清理许可证管理器资源");

		try {
			// 清理类加载器
			classLoaders.clear();

			// 清理许可证缓存
			pluginLicenses.clear();

			logger.info("许可证管理器资源清理完成");

		} catch (Exception e) {
			logger.error("清理许可证管理器资源失败", e);
		}
	}

	/**
	 * 从许可证缓存创建加密类加载器（主要实现） 用于加载许可证中的加密实现类
	 *
	 * @param pluginId 插件ID
	 * @param cache    许可证缓存
	 * @return 加密类加载器的Optional包装，如果无加密类则返回empty
	 */
	private Optional<EncryptedClassLoader> createEncryptedClassLoader(String pluginId, LicenseCache cache) {
		try {
			// 检查是否包含加密类
			if (!cache.hasEncryptedClasses()) {
				logger.info("许可证中未包含加密类数据，将使用标准类加载器: {}", pluginId);
				return Optional.empty();
			}

			// 直接使用缓存中的解密结果
			Map<String, byte[]> decryptedClasses = cache.getDecryptedClasses();
			byte[] decryptedJarData = createJarFromClasses(decryptedClasses);

			// 获取业务插件Bundle的ClassLoader作为父类加载器
			ClassLoader businessPluginClassLoader = getBusinessPluginClassLoader(pluginId);
			logger.info("为插件 {} 使用ClassLoader: {}", pluginId, businessPluginClassLoader.getClass().getName());

			EncryptedClassLoader classLoader = new EncryptedClassLoader(businessPluginClassLoader);
			// 添加解密后的JAR数据到类加载器
			classLoader.addDecryptedJar("license-classes.jar", decryptedJarData);
			logger.info("成功创建加密类加载器: {}", pluginId);
			return Optional.of(classLoader);

		} catch (Exception e) {
			logger.error("创建加密类加载器失败: {}", pluginId, e);
			return Optional.empty();
		}
	}

	/**
	 * 为指定插件创建加密类加载器
	 *
	 * @param pluginId 插件ID
	 * @return 加密类加载器的Optional包装，如果无加密类则返回empty
	 */
	public Optional<EncryptedClassLoader> createEncryptedClassLoaderForPlugin(String pluginId) {
		PluginLicense license = pluginLicenses.get(pluginId);
		if (license == null) {
			logger.warn("未找到插件许可证: {}", pluginId);
			return Optional.empty();
		}

		if (!license.hasEncryptedClasses()) {
			logger.debug("插件许可证中没有加密类数据: {}", pluginId);
			return Optional.empty();
		}

		try {
			// 获取许可证文件路径
			String licenseFilePath = license.getLicenseFilePath();
			if (licenseFilePath == null || licenseFilePath.trim().isEmpty()) {
				logger.warn("插件许可证缺少文件路径: {}", pluginId);
				return Optional.empty();
			}
			return createEncryptedClassLoaderFromFile(pluginId, licenseFilePath);
		} catch (Exception e) {
			logger.error("为插件创建加密类加载器失败: {}", pluginId, e);
			return Optional.empty();
		}
	}

	/**
	 * 从许可证文件路径创建加密类加载器（使用crypto模块的文件路径接口） 用于加载许可证中的加密实现类
	 *
	 * @param pluginId        插件ID
	 * @param licenseFilePath 许可证文件路径
	 * @return 加密类加载器的Optional包装，如果无加密类则返回empty
	 */
	private Optional<EncryptedClassLoader> createEncryptedClassLoaderFromFile(String pluginId, String licenseFilePath) {
		try {
			// 获取插件许可证对象
			PluginLicense license = pluginLicenses.get(pluginId);
			if (license == null || !license.hasEncryptedClasses()) {
				logger.info("许可证中未包含加密类数据，将使用标准类加载器: {}", pluginId);
				return Optional.empty();
			}

			// 使用crypto模块的文件路径接口直接解密许可证文件
			byte[] decryptedJarData = decryptLicenseFile(licenseFilePath);

			// 获取业务插件Bundle的ClassLoader作为父类加载器
			ClassLoader businessPluginClassLoader = getBusinessPluginClassLoader(pluginId);
			logger.info("为插件 {} 使用ClassLoader: {}", pluginId, businessPluginClassLoader.getClass().getName());

			EncryptedClassLoader classLoader = new EncryptedClassLoader(businessPluginClassLoader);
			// 添加解密后的JAR数据到类加载器
			classLoader.addDecryptedJar("license-classes.jar", decryptedJarData);
			logger.info("成功创建加密类加载器: {}", pluginId);
			return Optional.of(classLoader);

		} catch (Exception e) {
			logger.error("创建加密类加载器失败: {}", pluginId, e);
			return Optional.empty();
		}
	}

	/**
	 * 从许可证中创建实现类实例
	 */
	private Object createImplementationInstanceFromLicense(String pluginId, PluginLicense license,
			Class<?> serviceInterface, String implementationName) throws Exception {

		// 获取已经设置好的加密类加载器
		Optional<EncryptedClassLoader> classLoaderOpt = getEncryptedClassLoader(pluginId);
		if (!classLoaderOpt.isPresent()) {
			// 这种情况不应该发生，因为调用方应该已经通过ensureRuntimeEnvironmentInitialized确保了初始化
			String error = String.format("插件%s没有加密类加载器，这表明运行时环境未正确初始化。" + "请确保在调用此方法前已经调用了setupRuntimeEnvironment()方法。",
					pluginId);
			logger.error(error);
			throw new Exception(error);
		}

		EncryptedClassLoader classLoader = classLoaderOpt.get();

		try {
			// 加载实现类
			Class<?> implementationClass = classLoader.loadClass(implementationName);

			// 验证实现类确实实现了服务接口
			if (!serviceInterface.isAssignableFrom(implementationClass)) {
				logger.warn("实现类{}未实现接口{}", implementationName, serviceInterface.getName());
				return null;
			}

			// 创建实例
			Object instance = implementationClass.getDeclaredConstructor().newInstance();

			// 如果实例实现了LicenseAware接口，注入许可证信息
			if (instance instanceof LicenseAware) {
				((LicenseAware) instance).setLicenseInfo(license);
			}

			logger.info("成功从许可证创建服务实例: {} -> {} (插件: {})", serviceInterface.getName(), implementationName, pluginId);

			return instance;

		} catch (ClassNotFoundException e) {
			logger.warn("许可证中未找到实现类: {} (插件: {})", implementationName, pluginId);
			return null;
		} catch (Exception e) {
			logger.error("创建许可证服务实例失败: {} (插件: {})", implementationName, pluginId, e);
			return null;
		}
	}

	/**
	 * 将类字节码打包成JAR
	 */
	private byte[] createJarFromClasses(Map<String, byte[]> classes) {
		try {
			java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();

			try (java.util.jar.JarOutputStream jos = new java.util.jar.JarOutputStream(baos)) {
				for (Map.Entry<String, byte[]> entry : classes.entrySet()) {
					String className = entry.getKey();
					byte[] classBytes = entry.getValue();

					// 转换类名为路径格式
					String classPath = className.replace('.', '/') + ".class";

					java.util.jar.JarEntry jarEntry = new java.util.jar.JarEntry(classPath);
					jarEntry.setSize(classBytes.length);

					jos.putNextEntry(jarEntry);
					jos.write(classBytes);
					jos.closeEntry();
				}
			}

			return baos.toByteArray();
		} catch (Exception e) {
			logger.error("创建JAR包失败", e);
			throw new RuntimeException("创建JAR包失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 根据服务接口从许可证中创建服务实例 供许可证拦截器使用的公共方法
	 *
	 * @param serviceInterface 服务接口
	 * @return 服务实例，如果许可证中没有对应实现则返回null
	 */
	public Object createServiceInstanceFromLicense(Class<?> serviceInterface) {
		if (serviceInterface == null) {
			logger.debug("服务接口为空");
			return null;
		}

		// 遍历所有已注册的插件许可证
		for (String pluginId : getRegisteredPluginIds()) {
			Optional<PluginLicense> licenseOpt = getPluginLicense(pluginId);

			if (!licenseOpt.isPresent() || !licenseOpt.get().isValid()) {
				continue; // 跳过无效许可证
			}

			PluginLicense license = licenseOpt.get();

			// 检查许可证是否包含服务映射
			if (!license.hasServiceMappings()) {
				continue;
			}

			// 查找匹配的服务接口
			Map<String, String> serviceMappings = license.getServiceMappings();
			String interfaceName = serviceInterface.getName();
			String implementationName = serviceMappings.get(interfaceName);

			if (implementationName != null) {
				// 找到匹配的实现，确保运行时环境已初始化
				if (!ensureRuntimeEnvironmentInitialized(pluginId)) {
					logger.warn("插件运行时环境初始化失败，跳过服务创建: {} (插件: {})", interfaceName, pluginId);
					continue; // 尝试下一个许可证
				}

				// 尝试创建实例
				try {
					return createImplementationInstanceFromLicense(pluginId, license, serviceInterface,
							implementationName);
				} catch (Exception e) {
					logger.warn("从许可证创建服务实例失败: {} -> {} (插件: {})", interfaceName, implementationName, pluginId, e);
					continue; // 尝试下一个许可证
				}
			}
		}

		return null; // 没有找到匹配的实现
	}

	/**
	 * 使用crypto模块统一接口解密许可证文件
	 *
	 * @param licenseFilePath 许可证文件路径
	 * @return 解密后的JAR数据
	 * @throws Exception 解密失败时抛出异常
	 */
	private byte[] decryptLicenseFile(String licenseFilePath) throws Exception {
		logger.debug("开始解密许可证文件: {}", licenseFilePath);

		// 使用crypto模块的统一接口（文件路径接口）
		RSAKeyManager keyManager = new RSAKeyManager(); // 使用硬编码公钥
		RSALicenseEncryption rsaEncryption = new RSALicenseEncryption(keyManager);

		try {
			// 直接解密并验证许可证文件（使用文件路径接口）
			Map<String, byte[]> decryptedClasses = rsaEncryption.decryptAndVerifyLicenseFile(licenseFilePath);

			// 将解密的类数据转换为JAR格式
			return createJarFromClasses(decryptedClasses);
		} catch (Exception e) {
			logger.error("解密许可证失败: {}", e.getMessage(), e);
			throw new RuntimeException("解密许可证失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 确保插件的运行时环境已经初始化 如果没有初始化，则自动进行初始化
	 *
	 * @param pluginId 插件ID
	 * @return 是否成功初始化或已经初始化
	 */
	private boolean ensureRuntimeEnvironmentInitialized(String pluginId) {
		// 检查是否已经有加密类加载器
		Optional<EncryptedClassLoader> classLoaderOpt = getEncryptedClassLoader(pluginId);
		if (classLoaderOpt.isPresent()) {
			return true; // 已经初始化
		}

		// 尝试自动初始化
		try {
			logger.info("检测到插件运行时环境未初始化，开始自动初始化: {}", pluginId);

			// 优先使用缓存中的数据
			LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
			if (cache != null) {
				setupRuntimeEnvironment(pluginId, cache);
			} else {
				// 如果缓存不可用，尝试从已注册的许可证重新初始化
				Optional<PluginLicense> licenseOpt = getPluginLicense(pluginId);
				if (licenseOpt.isPresent() && licenseOpt.get().getRawLicenseData() != null) {
					setupRuntimeEnvironment(pluginId, licenseOpt.get().getRawLicenseData());
				} else {
					logger.warn("无法自动初始化运行时环境：缓存和原始数据都不可用: {}", pluginId);
					return false;
				}
			}

			// 验证初始化是否成功
			classLoaderOpt = getEncryptedClassLoader(pluginId);
			boolean success = classLoaderOpt.isPresent();

			if (success) {
				logger.info("成功自动初始化插件运行时环境: {}", pluginId);
			} else {
				logger.warn("自动初始化插件运行时环境失败: {}", pluginId);
			}

			return success;

		} catch (Exception e) {
			logger.error("自动初始化插件运行时环境异常: {}", pluginId, e);
			return false;
		}
	}

	/**
	 * 根据插件ID查找Bundle
	 *
	 * @param pluginId 插件ID
	 * @return 找到的Bundle，如果未找到则返回null
	 */
	private Bundle findBundleByPluginId(String pluginId) {
		// 查找对应的Bundle
		return findBundleBySymbolicName(pluginId);
	}

	/**
	 * 根据符号名称查找Bundle
	 *
	 * @param symbolicName Bundle符号名称
	 * @return 找到的Bundle，如果未找到则返回null
	 */
	private Bundle findBundleBySymbolicName(String symbolicName) {
		if (bundleContext == null) {
			return null;
		}

		Bundle[] bundles = bundleContext.getBundles();
		logger.debug("查找Bundle: {}，当前共有 {} 个Bundle", symbolicName, bundles.length);

		// 先列出所有相关的Bundle（用于调试）
		for (Bundle bundle : bundles) {
			String bundleName = bundle.getSymbolicName();
			if (bundleName != null && (bundleName.contains("fasnote") || bundleName.contains("feishu"))) {
				logger.debug("相关Bundle: {} (状态: {})", bundleName, getBundleStateName(bundle.getState()));
			}
		}

		for (Bundle bundle : bundles) {
			if (symbolicName.equals(bundle.getSymbolicName())) {
				// 检查Bundle状态 - 允许STARTING状态，因为ClassLoader通常已经可用
				if (bundle.getState() == Bundle.ACTIVE || bundle.getState() == Bundle.RESOLVED
						|| bundle.getState() == Bundle.STARTING) {
					logger.info("找到Bundle: {} (状态: {})", symbolicName, getBundleStateName(bundle.getState()));
					return bundle;
				} else {
					logger.warn("Bundle状态不正确: {} (状态: {})", symbolicName, getBundleStateName(bundle.getState()));
					return bundle; // 即使状态不理想，也尝试使用它
				}
			}
		}

		logger.warn("未找到Bundle: {}", symbolicName);
		return null;
	}

	/**
	 * 获取Bundle状态名称
	 *
	 * @param state Bundle状态
	 * @return 状态名称
	 */
	private String getBundleStateName(int state) {
		switch (state) {
		case Bundle.UNINSTALLED:
			return "UNINSTALLED";
		case Bundle.INSTALLED:
			return "INSTALLED";
		case Bundle.RESOLVED:
			return "RESOLVED";
		case Bundle.STARTING:
			return "STARTING";
		case Bundle.STOPPING:
			return "STOPPING";
		case Bundle.ACTIVE:
			return "ACTIVE";
		default:
			return "UNKNOWN(" + state + ")";
		}
	}

	/**
	 * 获取业务插件Bundle的ClassLoader
	 *
	 * @param pluginId 插件ID
	 * @return 业务插件Bundle的ClassLoader，如果未找到则返回当前ClassLoader
	 */
	private ClassLoader getBusinessPluginClassLoader(String pluginId) {
		try {
			// 如果没有BundleContext，返回当前ClassLoader
			if (bundleContext == null) {
				logger.warn("BundleContext未设置，使用当前ClassLoader: {}", pluginId);
				return getClass().getClassLoader();
			}

			// 查找对应的Bundle
			Bundle targetBundle = findBundleBySymbolicName(pluginId);
			if (targetBundle == null) {
				logger.warn("未找到Bundle: {} (插件: {})", pluginId, pluginId);
				return getClass().getClassLoader();
			}

			// 获取Bundle的ClassLoader
			BundleWiring bundleWiring = targetBundle.adapt(BundleWiring.class);
			if (bundleWiring != null) {
				ClassLoader bundleClassLoader = bundleWiring.getClassLoader();
				logger.info("成功获取业务插件ClassLoader: {} -> {}", pluginId, pluginId);
				return bundleClassLoader;
			} else {
				logger.warn("无法获取Bundle的ClassLoader: {}", pluginId);
				return getClass().getClassLoader();
			}

		} catch (Exception e) {
			logger.error("获取业务插件ClassLoader失败: {}", pluginId, e);
			return getClass().getClassLoader();
		}
	}

	/**
	 * 获取插件的加密类加载器
	 *
	 * @param pluginId 插件ID
	 * @return 加密类加载器的Optional包装，如果不存在则返回empty
	 */
	public Optional<EncryptedClassLoader> getEncryptedClassLoader(String pluginId) {
		if (pluginId == null || pluginId.trim().isEmpty()) {
			logger.warn("插件ID为空，无法获取类加载器");
			return Optional.empty();
		}

		EncryptedClassLoader classLoader = classLoaders.get(pluginId);
		if (classLoader == null) {
			logger.debug("未找到插件的加密类加载器: {}", pluginId);
		}

		return Optional.ofNullable(classLoader);
	}

	/**
	 * 获取许可证信息
	 *
	 * @param pluginId 插件ID
	 * @return 许可证信息的Optional包装，如果不存在则返回empty
	 */
	public Optional<LicenseInfo> getLicenseInfo(String pluginId) {
		Optional<PluginLicense> licenseOpt = getPluginLicense(pluginId);
		if (!licenseOpt.isPresent()) {
			logger.debug("未找到插件许可证信息: {}", pluginId);
			return Optional.empty();
		}

		PluginLicense license = licenseOpt.get();
		LicenseInfo info = new LicenseInfo();
		info.setPluginId(pluginId);
		info.setLicenseData(license.getLicenseData());
		info.setLoadTime(LocalDateTime.now());

		return Optional.of(info);
	}

	/**
	 * 获取插件许可证
	 *
	 * @param pluginId 插件ID
	 * @return 许可证对象的Optional包装，如果不存在则返回empty
	 */
	public Optional<PluginLicense> getPluginLicense(String pluginId) {
		if (pluginId == null || pluginId.trim().isEmpty()) {
			logger.warn("插件ID为空，无法获取许可证");
			return Optional.empty();
		}

		PluginLicense license = pluginLicenses.get(pluginId);
		if (license == null) {
			logger.debug("未找到插件许可证: {}", pluginId);
		}

		return Optional.ofNullable(license);
	}

	/**
	 * 获取所有已注册的插件ID
	 *
	 * @return 插件ID列表
	 */
	public List<String> getRegisteredPluginIds() {
		return new ArrayList<>(pluginLicenses.keySet());
	}

	/**
	 * 获取许可证服务注册表
	 *
	 * @return 服务注册表实例
	 */
	public LicenseServiceRegistry getServiceRegistry() {
		return serviceRegistry;
	}

	/**
	 * 获取统计信息
	 *
	 * @return 统计信息
	 */
	public Map<String, Object> getStatistics() {
		Map<String, Object> stats = new ConcurrentHashMap<>();
		stats.put("registeredPlugins", pluginLicenses.size());
		stats.put("activeClassLoaders", classLoaders.size());
		stats.put("lastUpdate", LocalDateTime.now());

		return stats;
	}

	/**
	 * 检查插件是否有许可证（不验证有效性）
	 *
	 * @param pluginId 插件ID
	 * @return 是否存在许可证
	 */
	public boolean hasPluginLicense(String pluginId) {
		return pluginLicenses.containsKey(pluginId);
	}

	/**
	 * 检查插件是否有有效许可证
	 *
	 * @param pluginId 插件ID
	 * @return 是否有效
	 */
	public boolean hasValidLicense(String pluginId) {
		Optional<PluginLicense> licenseOpt = getPluginLicense(pluginId);
		if (!licenseOpt.isPresent()) {
			logger.debug("插件许可证不存在: {}", pluginId);
			return false;
		}

		ValidationResult result = validateLicense(licenseOpt.get());
		return result.isValid();
	}

	/**
	 * 初始化许可证管理器
	 */
	private void initialize() {
		if (initialized) {
			logger.debug("许可证管理器已经初始化，跳过重复初始化");
			return;
		}

		logger.info("初始化许可证管理器");

		// 自动扫描并加载许可证文件
		autoScanAndLoadLicenses();

		initialized = true;
		logger.info("许可证管理器初始化完成");
	}

	/**
	 * 检查功能是否可用
	 *
	 * @param featureName 功能名称
	 * @return 是否可用
	 */
	public boolean isFeatureEnabled(String featureName) {
		// 遍历所有许可证检查功能是否可用
		for (PluginLicense license : pluginLicenses.values()) {
			ValidationResult result = validateLicense(license);
			if (result.isValid()) {
				// 这里可以扩展更复杂的功能检查逻辑
				// 目前简单返回true表示有有效许可证就启用功能
				return true;
			}
		}
		return false;
	}

	/**
	 * 检查Bundle是否需要许可证验证 统一检查Bundle的MANIFEST.MF中的ALM-License-Required属性
	 *
	 * @param bundle   Bundle对象
	 * @param pluginId 插件ID（用于日志）
	 * @return 是否需要许可证验证
	 */
	public boolean isPluginRequiresLicense(Bundle bundle, String pluginId) {
		if (bundle == null) {
			logger.debug("Bundle为空，默认不需要许可证验证: {}", pluginId);
			return false;
		}

		try {
			String licenseRequired = bundle.getHeaders().get("ALM-License-Required");
			if ("true".equalsIgnoreCase(licenseRequired)) {
				logger.debug("Bundle MANIFEST.MF中标记需要许可证: {}", pluginId);
				return true;
			} else {
				logger.debug("Bundle MANIFEST.MF中标记不需要许可证或未设置: {}", pluginId);
				return false;
			}
		} catch (Exception e) {
			logger.warn("检查Bundle MANIFEST.MF属性时发生异常: {}", pluginId, e);
			return false;
		}
	}

	/**
	 * 检查插件是否需要许可证验证 统一检查Bundle的MANIFEST.MF中的ALM-License-Required属性
	 *
	 * @param pluginId 插件ID
	 * @return 是否需要许可证验证
	 */
	public boolean isPluginRequiresLicense(String pluginId) {
		if (pluginId == null || pluginId.trim().isEmpty()) {
			return false;
		}

		try {
			Bundle bundle = findBundleByPluginId(pluginId);
			return isPluginRequiresLicense(bundle, pluginId);
		} catch (Exception e) {
			logger.warn("检查Bundle MANIFEST.MF属性时发生异常: {}", pluginId, e);
		}

		// 如果无法获取Bundle信息，默认不需要许可证验证
		logger.debug("无法获取Bundle信息，默认不需要许可证验证: {}", pluginId);
		return false;
	}

	/**
	 * 从文件加载并激活许可证
	 *
	 * @param pluginId 插件ID
	 * @return 激活结果
	 */
	public ValidationResult loadAndActivateLicenseFromFile(String pluginId) {
		try {
			logger.info("从文件加载并激活许可证: {}", pluginId);

			// 获取许可证目录
			java.io.File licenseDir = LicenseDirectoryManager.getInstance().getLicenseDir();

			// 扫描许可证文件
			LicenseScanner scanner = new LicenseScanner(licenseDir);
			Map<String, String> licenseFiles = scanner.scanLicenseFiles();

			String licenseFilePath = licenseFiles.get(pluginId);
			if (licenseFilePath == null) {
				return ValidationResult.failure("未找到许可证文件: " + pluginId);
			}

			// 从文件加载许可证
			loadLicenseFromFile(pluginId, licenseFilePath);

			return ValidationResult.success("许可证加载并激活成功");

		} catch (Exception e) {
			String errorMessage = "从文件加载并激活许可证失败: " + e.getMessage();
			logger.error(errorMessage, e);
			return ValidationResult.failure(errorMessage);
		}
	}

	/**
	 * 从许可证缓存中加载并注册实现类（主要实现） 注意：许可证验证已在UnifiedLicenseProcessor中完成，此方法专注于服务注册
	 *
	 * @param pluginId    插件ID
	 * @param cache       许可证缓存
	 * @param classLoader 加密类加载器
	 */
	private void loadAndRegisterImplementations(String pluginId, LicenseCache cache, EncryptedClassLoader classLoader) {
		try {
			PluginLicense license = cache.getLicenseMetadata();
			if (license == null || !license.hasServiceMappings()) {
				logger.info("插件 {} 的许可证中未定义服务映射，跳过服务注册", pluginId);
				return;
			}

			logger.info("开始注册许可证服务: {}", pluginId);

			// 遍历许可证中定义的服务映射
			Map<String, String> serviceMappings = license.getServiceMappings();
			int successCount = 0;
			int totalCount = serviceMappings.size();

			for (Map.Entry<String, String> mapping : serviceMappings.entrySet()) {
				String interfaceName = mapping.getKey();
				String implementationName = mapping.getValue();

				try {
					// 从许可证的加密类加载器中加载服务接口
					Class<?> serviceInterface = classLoader.loadClass(interfaceName);

					// 使用createImplementationInstanceFromLicense创建服务实例
					try {
						Object serviceInstance = createImplementationInstanceFromLicense(pluginId, license,
								serviceInterface, implementationName);

						// 注册服务到服务注册表
						registerServiceInstance(serviceInterface, serviceInstance, interfaceName, implementationName,
								pluginId);
						successCount++;

					} catch (Exception e) {
						logger.warn("创建服务实例失败，跳过注册: {} -> {} (插件: {})", interfaceName, implementationName, pluginId, e);
					}

				} catch (ClassNotFoundException e) {
					logger.error("加载许可证中的服务类失败: {} -> {} (插件: {})", interfaceName, implementationName, pluginId, e);
				} catch (Exception e) {
					logger.error("注册许可证服务失败: {} -> {} (插件: {})", interfaceName, implementationName, pluginId, e);
				}
			}

			logger.info("许可证服务注册完成: {}，成功 {}/{}", pluginId, successCount, totalCount);

		} catch (Exception e) {
			logger.error("从缓存加载和注册许可证实现类失败: {}", pluginId, e);
		}
	}

	/**
	 * 从文件加载许可证（完整加载，包括实现类）
	 *
	 * @param pluginId        插件ID
	 * @param licenseFilePath 许可证文件路径
	 */
	private void loadLicenseFromFile(String pluginId, String licenseFilePath) {
		try {
			logger.info("从文件加载许可证: {} -> {}", pluginId, licenseFilePath);

			// 使用统一处理器进行许可证处理
			ValidationResult result = licenseProcessor.loadAndProcessLicense(pluginId, licenseFilePath);
			if (result.isValid()) {
				// 从缓存中获取处理结果
				LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
				if (cache != null) {
					// 存储许可证元数据
					pluginLicenses.put(pluginId, cache.getLicenseMetadata());

					// 设置运行时环境（加载实现类）
					setupRuntimeEnvironment(pluginId, cache);

					logger.info("成功从文件加载许可证: {}", pluginId);
				}
			} else {
				logger.warn("从文件加载许可证失败: {}, 原因: {}", pluginId, result.getMessage());
			}

		} catch (Exception e) {
			logger.error("从文件加载许可证异常: {}", pluginId, e);
		}
	}

	/**
	 * 预加载许可证文件（仅解密验证，不加载实现类）
	 *
	 * @param pluginId        插件ID
	 * @param licenseFilePath 许可证文件路径
	 */
	private void preloadLicenseFromFile(String pluginId, String licenseFilePath) {
		try {
			logger.info("预加载许可证文件: {} -> {}", pluginId, licenseFilePath);

			// 使用统一处理器进行许可证处理
			ValidationResult result = licenseProcessor.loadAndProcessLicense(pluginId, licenseFilePath);
			if (result.isValid()) {
				// 从缓存中获取处理结果并存储到本地缓存
				LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
				if (cache != null) {
					pluginLicenses.put(pluginId, cache.getLicenseMetadata());
					logger.info("成功预加载许可证: {}", pluginId);
				}
			} else {
				logger.warn("预加载许可证失败: {}, 原因: {}", pluginId, result.getMessage());
			}

		} catch (Exception e) {
			logger.error("预加载许可证异常: {}", pluginId, e);
		}
	}

	/**
	 * 预加载插件许可证（仅解密验证，不加载实现类）
	 *
	 * @param pluginId    插件ID
	 * @param licenseData 许可证数据（加密）
	 * @return 注册结果
	 */
	public ValidationResult preloadPluginLicense(String pluginId, byte[] licenseData) {
		logger.info("预加载插件许可证: {}", pluginId);

		// 直接使用统一处理器
		ValidationResult result = licenseProcessor.loadAndProcessLicense(pluginId, licenseData);
		if (result.isValid()) {
			// 从缓存中获取处理结果并存储到本地缓存
			LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
			if (cache != null) {
				pluginLicenses.put(pluginId, cache.getLicenseMetadata());

				// 设置运行时环境（创建类加载器）
				// 这对于许可证拦截器正常工作是必需的
				setupRuntimeEnvironment(pluginId, cache);

				logger.info("插件许可证预加载成功: {}", pluginId);
			}
		} else {
			logger.warn("插件许可证预加载失败: {}, 原因: {}", pluginId, result.getMessage());
		}

		return result;
	}

	/**
	 * 从文件预加载插件许可证（仅解密验证，不加载实现类）
	 *
	 * @param pluginId        插件ID
	 * @param licenseFilePath 许可证文件路径
	 * @return 注册结果
	 */
	public ValidationResult preloadPluginLicenseFromFile(String pluginId, String licenseFilePath) {
		logger.info("从文件预加载插件许可证: {} -> {}", pluginId, licenseFilePath);

		// 直接使用统一处理器
		ValidationResult result = licenseProcessor.loadAndProcessLicense(pluginId, licenseFilePath);
		if (result.isValid()) {
			// 从缓存中获取处理结果并存储到本地缓存
			LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
			if (cache != null) {
				pluginLicenses.put(pluginId, cache.getLicenseMetadata());

				// 设置运行时环境（创建类加载器）
				// 这对于许可证拦截器正常工作是必需的
				setupRuntimeEnvironment(pluginId, cache);

				logger.info("插件许可证预加载成功: {}", pluginId);
			}
		} else {
			logger.warn("插件许可证预加载失败: {}, 原因: {}", pluginId, result.getMessage());
		}

		return result;
	}

	/**
	 * 刷新所有许可证状态
	 */
	public void refreshAllLicenses() {
		logger.info("刷新所有许可证状态");

		for (String pluginId : pluginLicenses.keySet()) {
			try {
				PluginLicense license = pluginLicenses.get(pluginId);
				ValidationResult result = validateLicense(license);
				logger.info("插件 {} 许可证状态: {}", pluginId, result.isValid() ? "有效" : "无效");
			} catch (Exception e) {
				logger.error("刷新插件许可证状态失败: {}", pluginId, e);
			}
		}
	}

	/**
	 * 注册插件许可证（完整注册，包括加载实现类）
	 *
	 * @param pluginId    插件ID
	 * @param licenseData 许可证数据（加密）
	 * @return 验证结果
	 */
	public ValidationResult registerPluginLicense(String pluginId, byte[] licenseData) {
		logger.info("注册插件许可证: {}", pluginId);

		// 直接使用统一处理器
		ValidationResult result = licenseProcessor.loadAndProcessLicense(pluginId, licenseData);
		if (result.isValid()) {
			// 从缓存中获取处理结果并存储到本地缓存
			LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
			if (cache != null) {
				pluginLicenses.put(pluginId, cache.getLicenseMetadata());

				// 设置运行时环境（加载实现类）
				setupRuntimeEnvironment(pluginId, cache);

				logger.info("插件许可证注册成功: {}", pluginId);
			}
		} else {
			logger.warn("插件许可证注册失败: {}, 原因: {}", pluginId, result.getMessage());
		}

		return result;
	}

	/**
	 * 注册服务实例到服务注册表
	 *
	 * @param serviceInterface   服务接口类
	 * @param serviceInstance    服务实例
	 * @param interfaceName      接口名称
	 * @param implementationName 实现类名称
	 * @param pluginId           插件ID
	 */
	private void registerServiceInstance(Class<?> serviceInterface, Object serviceInstance, String interfaceName,
			String implementationName, String pluginId) {
		try {
			// 按接口类型注册服务
			@SuppressWarnings("unchecked")
			Class<Object> genericInterface = (Class<Object>) serviceInterface;
			serviceRegistry.registerService(genericInterface, serviceInstance);

			// 按服务名称注册服务（格式：插件ID.接口名）
			String serviceName = pluginId + "." + interfaceName;
			serviceRegistry.registerService(serviceName, serviceInstance);

			logger.info("成功注册许可证服务: {} -> {} (插件: {})", interfaceName, implementationName, pluginId);

		} catch (Exception e) {
			logger.error("注册服务实例失败: {} -> {} (插件: {})", interfaceName, implementationName, pluginId, e);
			throw e; // 重新抛出异常，让调用者处理
		}
	}

	/**
	 * 移除插件许可证
	 *
	 * @param pluginId 插件ID
	 */
	public void removePluginLicense(String pluginId) {
		logger.info("移除插件许可证: {}", pluginId);

		// 清理相关资源
		pluginLicenses.remove(pluginId);
		classLoaders.remove(pluginId);

		logger.info("插件许可证已移除: {}", pluginId);
	}

	/**
	 * 重新扫描并加载新的许可证文件
	 *
	 * @return 新加载的许可证数量
	 */
	public int rescanLicenseFiles() {
		try {
			logger.info("重新扫描许可证文件");

			// 获取许可证目录
			java.io.File licenseDir = LicenseDirectoryManager.getInstance().getLicenseDir();

			// 创建扫描器并获取当前已加载的许可证
			LicenseScanner scanner = new LicenseScanner(licenseDir);
			Map<String, String> currentLicenses = new HashMap<>();
			for (String pluginId : pluginLicenses.keySet()) {
				currentLicenses.put(pluginId, ""); // 简化处理，只关心插件ID
			}

			// 扫描新的许可证文件
			Map<String, String> newLicenseFiles = scanner.refreshScan(currentLicenses);

			// 加载新发现的许可证文件
			int loadedCount = 0;
			for (Map.Entry<String, String> entry : newLicenseFiles.entrySet()) {
				String pluginId = entry.getKey();
				String licenseFilePath = entry.getValue();

				try {
					loadLicenseFromFile(pluginId, licenseFilePath);
					loadedCount++;
				} catch (Exception e) {
					logger.error("重新加载许可证文件失败: {}", licenseFilePath, e);
				}
			}

			logger.info("重新扫描完成，新加载 {} 个许可证文件", loadedCount);
			return loadedCount;

		} catch (Exception e) {
			logger.error("重新扫描许可证文件失败", e);
			return 0;
		}
	}

	/**
	 * 设置安全验证器（用于依赖注入）
	 *
	 * @param securityValidator 安全验证器
	 */
	public void setSecurityValidator(SecurityValidator securityValidator) {
		this.securityValidator = securityValidator;
	}

	/**
	 * 从许可证缓存设置运行时环境（主要实现）
	 *
	 * @param pluginId 插件ID
	 * @param cache    许可证缓存
	 */
	private void setupRuntimeEnvironment(String pluginId, LicenseCache cache) {
		try {
			logger.info("从缓存设置运行时环境: {}", pluginId);

			// 检查是否有加密类需要加载
			if (!cache.hasEncryptedClasses()) {
				logger.info("插件 {} 没有加密类，将使用标准类加载方式", pluginId);
				return;
			}

			// 创建加密类加载器
			Optional<EncryptedClassLoader> encryptedClassLoaderOpt = createEncryptedClassLoader(pluginId, cache);

			if (encryptedClassLoaderOpt.isPresent()) {
				EncryptedClassLoader encryptedClassLoader = encryptedClassLoaderOpt.get();
				classLoaders.put(pluginId, encryptedClassLoader);
				logger.info("插件 {} 的加密类加载器已创建并存储", pluginId);

				// 加载并注册许可证中定义的服务实现
				loadAndRegisterImplementations(pluginId, cache, encryptedClassLoader);
			} else {
				logger.info("插件 {} 没有加密类，将使用标准类加载方式", pluginId);
			}

			logger.info("运行时环境设置完成: {}", pluginId);
		} catch (Exception e) {
			logger.error("设置运行时环境失败: {}", pluginId, e);
		}
	}

	/**
	 * 设置运行时环境 从许可证数据设置运行时环境（兼容性包装器） 内部优先使用缓存，如果缓存不可用则解析许可证数据
	 *
	 * @param pluginId    插件ID
	 * @param licenseData 许可证数据
	 */
	private void setupRuntimeEnvironment(String pluginId, String licenseData) {
		// 优先使用缓存中的数据
		LicenseCache cache = licenseProcessor.getCachedLicense(pluginId);
		if (cache != null) {
			logger.info("使用缓存数据设置运行时环境: {}", pluginId);
			setupRuntimeEnvironment(pluginId, cache);
		} else {
			// 兼容性处理：如果缓存不可用，尝试从许可证数据重新处理
			logger.info("缓存不可用，使用许可证数据设置运行时环境: {}", pluginId);
			try {
				// 重新处理许可证数据并缓存
				ValidationResult result = licenseProcessor.loadAndProcessLicense(pluginId,
						licenseData.getBytes("UTF-8"));
				if (result.isValid()) {
					cache = licenseProcessor.getCachedLicense(pluginId);
					if (cache != null) {
						setupRuntimeEnvironment(pluginId, cache);
					}
				} else {
					logger.error("重新处理许可证数据失败: {}, 原因: {}", pluginId, result.getMessage());
				}
			} catch (Exception e) {
				logger.error("设置运行时环境失败: {}", pluginId, e);
			}
		}
	}

	/**
	 * 验证许可证
	 *
	 * @param license 许可证对象
	 * @return 验证结果
	 */
	public ValidationResult validateLicense(PluginLicense license) {
		if (license == null) {
			return ValidationResult.failure("许可证为空");
		}

		// 使用安全验证器进行验证
		return securityValidator.validateLicense(license);
	}
}