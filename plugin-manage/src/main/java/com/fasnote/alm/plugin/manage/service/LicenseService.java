package com.fasnote.alm.plugin.manage.service;

import java.util.Optional;

import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;

/**
 * 许可证核心服务接口 职责：许可证验证和查询
 */
public interface LicenseService {

	/**
	 * 获取插件许可证
	 *
	 * @param pluginId 插件ID
	 * @return 许可证对象的Optional包装
	 */
	Optional<PluginLicense> getLicense(String pluginId);

	/**
	 * 检查插件是否有有效许可证
	 *
	 * @param pluginId 插件ID
	 * @return 是否有效
	 */
	boolean hasValidLicense(String pluginId);

	/**
	 * 检查功能是否可用
	 *
	 * @param featureName 功能名称
	 * @return 是否可用
	 */
	boolean isFeatureEnabled(String featureName);

	/**
	 * 刷新许可证状态
	 */
	void refreshLicenseStatus();

	/**
	 * 验证许可证
	 *
	 * @param license 许可证对象
	 * @return 验证结果
	 */
	ValidationResult validateLicense(PluginLicense license);

	/**
	 * 验证插件许可证
	 *
	 * @param pluginId 插件ID
	 * @return 验证结果
	 */
	ValidationResult validatePluginLicense(String pluginId);
}