package com.fasnote.alm.auth.feishu;

import java.io.IOException;
import java.util.Map;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jetbrains.annotations.NotNull;

import com.fasnote.alm.auth.feishu.config.FeishuConfigurationAdapter;
import com.fasnote.alm.auth.feishu.utils.FeishuStateUtils;
import com.fasnote.alm.auth.feishu.utils.OAuth2UrlHandler;
import com.polarion.core.util.logging.Logger;
import com.polarion.platform.config.ConfigurationException;
import com.polarion.platform.security.auth.IAuthenticationContext;
import com.polarion.platform.security.auth.IAuthenticator;
import com.polarion.platform.security.auth.impl.HttpUtils;
import com.polarion.platform.security.auth.impl.oauth2.OAuth2Configuration;

/**
 * 飞书 OAuth2 认证器
 * 
 * 统一的 OAuth2 认证器，提供以下功能： 1. 统一处理已认证用户的 fromUrl 重定向，解决单点登录重复进入 404 问题 2.
 * 根据配置ID判断是否使用飞书特定逻辑 3. 如果是飞书配置，使用飞书特定的认证逻辑 4. 如果不是飞书配置，使用标准OAuth2认证器
 */
public class FeishuOAuth2Authenticator implements IAuthenticator {

	@NotNull
	private static final Logger log = Logger.getLogger(FeishuOAuth2Authenticator.class);

	@NotNull
	private final IAuthenticator originalOAuth2Authenticator;

	public FeishuOAuth2Authenticator(@NotNull IAuthenticator originalOAuth2Authenticator) {
		this.originalOAuth2Authenticator = originalOAuth2Authenticator;
	}

	@Override
	public boolean authenticate(@NotNull IAuthenticationContext context, @NotNull HttpServletRequest request,
			@NotNull HttpServletResponse response) {

		try {
			// 统一处理已认证用户的 fromUrl 重定向，解决单点登录重复进入 404 问题
			if (context.checkForCachedAuthentication() && !isReauthRequest(request)) {
				String redirectUrl = OAuth2UrlHandler.handleAuthenticatedUserRedirect(request);
				if (redirectUrl != null) {
					log.info("OAuth2 authenticator: redirecting authenticated user to: " + redirectUrl);
					response.sendRedirect(response.encodeRedirectURL(redirectUrl));
					return false;
				}
			}

			String configId = context.getAuthenticatorId();

			// 检查是否为飞书配置
			if (FeishuConfigurationAdapter.isFeishuConfig(configId)) {
				log.debug("Using Feishu authentication for config ID: " + configId);
				return authenticateFeishu(context, request, response);
			} else {
				log.debug("Using standard OAuth2 authentication for config ID: " + configId);
				return originalOAuth2Authenticator.authenticate(context, request, response);
			}

		} catch (IOException e) {
			log.error("Error in OAuth2 authenticator", e);
			return originalOAuth2Authenticator.authenticate(context, request, response);
		}
	}

	/**
	 * 飞书特定的认证逻辑
	 */
	private boolean authenticateFeishu(@NotNull IAuthenticationContext context, @NotNull HttpServletRequest request,
			@NotNull HttpServletResponse response) {
		try {
			String authenticatorId = context.getAuthenticatorId();

			// 获取标准OAuth2配置
			OAuth2Configuration config = FeishuConfigurationAdapter.createOAuth2Configuration(authenticatorId);

			// 创建飞书OAuth2登录客户端
			FeishuOAuth2LoginClient loginClient = new FeishuOAuth2LoginClient(config);

			// 执行飞书特定的认证逻辑
			boolean isReauthRequest = request.getRequestURI().equals("/polarion/reauth");
			if (context.checkForCachedAuthentication() && !isReauthRequest) {
				return true;
			} else {
				String authorizationCode = request.getParameter("code");
				if (authorizationCode == null) {
					return initiateFeishuAuthentication(request, response, loginClient);
				} else {
					return loginClient.exchangeAuthorizationCode(authorizationCode, request, response, context);
				}
			}

		} catch (Exception e) {
			log.error("Feishu authentication failed", e);
			throw new ConfigurationException("Feishu not configured properly", e);
		}
	}

	/**
	 * 启动飞书认证流程
	 */
	private boolean initiateFeishuAuthentication(@NotNull HttpServletRequest request,
			@NotNull HttpServletResponse response, @NotNull FeishuOAuth2LoginClient loginClient) throws IOException {

		// 获取目标重定向URL
		String targetUrl = OAuth2UrlHandler.extractTargetUrlFromSsoRequest(request);
		log.debug("Found target URL from SSO request: " + targetUrl);

		// 创建登录处理器
		FeishuOAuth2DefaultLoginHandler loginHandler = new FeishuOAuth2DefaultLoginHandler(targetUrl);

		// 获取认证参数并构建表单
		Map<String, String> authParams = loginClient.getAuthenticationParams(loginHandler, targetUrl);

		(new HttpUtils(request, response)).getForm(loginClient.getAuthorizeUrl(), authParams);
		return false;
	}

	@Override
	public Optional<String> isExpectedCallBack(@NotNull HttpServletRequest request,
			@NotNull HttpServletResponse response) {

		// 首先尝试飞书回调检测
		Optional<String> feishuCallback = checkFeishuCallback(request);
		if (feishuCallback.isPresent()) {
			String configId = feishuCallback.get();
			if (FeishuConfigurationAdapter.isFeishuConfig(configId)) {
				log.debug("Detected Feishu OAuth2 callback for config ID: " + configId);
				return feishuCallback;
			}
		}

		// 如果不是飞书回调，使用标准OAuth2回调检测
		return originalOAuth2Authenticator.isExpectedCallBack(request, response);
	}

	/**
	 * 检查飞书回调
	 */
	private Optional<String> checkFeishuCallback(@NotNull HttpServletRequest request) {
		try {
			String authorizationCode = request.getParameter("code");
			if (authorizationCode == null || authorizationCode.trim().isEmpty()) {
				return Optional.empty();
			}

			String state = request.getParameter("state");
			if (state != null) {
				// 检查是否为增强的state格式（包含target信息）
				if (FeishuStateUtils.hasTargetUrl(state)) {
					log.debug("Detected Feishu OAuth2 callback with enhanced state: " + state);
					return Optional.of("feishu");
				}
			}

			return Optional.empty();

		} catch (Exception e) {
			log.warn("Error checking if request is expected Feishu callback", e);
			return Optional.empty();
		}
	}

	/**
	 * 检查是否为重新认证请求
	 */
	private boolean isReauthRequest(@NotNull HttpServletRequest request) {
		return request.getRequestURI().equals("/polarion/reauth");
	}
}
