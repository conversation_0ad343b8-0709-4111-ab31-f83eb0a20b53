package com.fasnote.alm.injection.example;

import com.fasnote.alm.injection.facade.DI;

/**
 * 用户API使用示例
 * 展示如何使用门面API来访问依赖注入服务
 *
 * 注意：InjectionUtils工具类已废弃，请使用DI门面API
 */
public class UsageExample {
    
    /**
     * 推荐的现代API使用方式
     */
    public void modernApiExample() {
        // 最简单的服务获取
        MyService service = DI.get(MyService.class);
        
        // 获取命名服务
        MyService premiumService = DI.get(MyService.class, "premium");
        
        // 创建对象并注入依赖
        MyComponent component = DI.create(MyComponent.class);
        
        // 检查DI框架是否可用
        if (DI.isAvailable()) {
            // 安全使用
        }
    }
    
    /**
     * 传统工厂API迁移示例
     * 展示如何从Services.create()迁移到DI.get()
     */
    public void factoryApiMigrationExample() {
        // 旧方式（已删除）：Services.create(MyService.class)
        // 新方式：
        MyService service = DI.get(MyService.class);

        // 旧方式（已删除）：Services.create(MyService.class, "special")
        // 新方式：
        MyService namedService = DI.get(MyService.class, "special");
    }

    /**
     * InjectionUtils迁移指南
     * 展示如何从已废弃的InjectionUtils迁移到DI门面API
     */
    public void migrationGuide() {
        // 旧方式（已废弃）：InjectionUtils.getService(MyService.class)
        // 新方式：
        MyService service = DI.get(MyService.class);

        // 旧方式（已废弃）：InjectionUtils.createInstance(MyComponent.class)
        // 新方式：
        MyComponent component = DI.create(MyComponent.class);

        // 旧方式（已废弃）：InjectionUtils.isAvailable()
        // 新方式：
        boolean available = DI.isAvailable();

        // 高级用例：需要直接访问IDependencyInjector时
        // 旧方式（已废弃）：InjectionUtils.getDependencyInjector()
        // 新方式：
        var injector = DI.getInjector();
    }
    
    // 示例服务接口
    public interface MyService {
        void doSomething();
    }
    
    // 示例组件类
    public static class MyComponent {
        // 依赖注入注解
        private MyService service;
        
        public void useService() {
            if (service != null) {
                service.doSomething();
            }
        }
    }
}