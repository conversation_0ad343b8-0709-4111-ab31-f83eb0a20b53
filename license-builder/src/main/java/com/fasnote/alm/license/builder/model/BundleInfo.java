package com.fasnote.alm.license.builder.model;

import java.util.ArrayList;
import java.util.List;
import java.util.jar.Manifest;
import com.fasnote.alm.license.builder.util.XmlConfigParser;

/**
 * OSGi Bundle信息
 * 保留所有字段以兼容现有代码，但ManifestParser只解析必要的字段
 */
public class BundleInfo {

    private String symbolicName;
    private String version;
    private String name;
    private String vendor;
    private String activator;
    private List<String> requiredBundles = new ArrayList<>();
    private List<String> exportedPackages = new ArrayList<>();
    private List<String> importedPackages = new ArrayList<>();
    private String executionEnvironment;
    private String bundleClassPath;
    private Manifest manifest; // 原始MANIFEST.MF文件
    private XmlConfigParser.XmlConfigInfo xmlConfigInfo; // XML配置文件信息
    
    public String getSymbolicName() { return symbolicName; }
    public void setSymbolicName(String symbolicName) { this.symbolicName = symbolicName; }

    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getVendor() { return vendor; }
    public void setVendor(String vendor) { this.vendor = vendor; }

    public String getActivator() { return activator; }
    public void setActivator(String activator) { this.activator = activator; }

    public List<String> getRequiredBundles() { return requiredBundles; }
    public void setRequiredBundles(List<String> requiredBundles) { this.requiredBundles = requiredBundles; }

    public List<String> getExportedPackages() { return exportedPackages; }
    public void setExportedPackages(List<String> exportedPackages) { this.exportedPackages = exportedPackages; }

    public List<String> getImportedPackages() { return importedPackages; }
    public void setImportedPackages(List<String> importedPackages) { this.importedPackages = importedPackages; }

    public String getExecutionEnvironment() { return executionEnvironment; }
    public void setExecutionEnvironment(String executionEnvironment) { this.executionEnvironment = executionEnvironment; }

    public String getBundleClassPath() { return bundleClassPath; }
    public void setBundleClassPath(String bundleClassPath) { this.bundleClassPath = bundleClassPath; }

    public Manifest getManifest() { return manifest; }
    public void setManifest(Manifest manifest) { this.manifest = manifest; }

    public XmlConfigParser.XmlConfigInfo getXmlConfigInfo() { return xmlConfigInfo; }
    public void setXmlConfigInfo(XmlConfigParser.XmlConfigInfo xmlConfigInfo) { this.xmlConfigInfo = xmlConfigInfo; }
    
    /**
     * 获取完整标识符
     */
    public String getFullIdentifier() {
        return symbolicName + "_" + version;
    }
    
    @Override
    public String toString() {
        return "BundleInfo{" +
                "symbolicName='" + symbolicName + '\'' +
                ", version='" + version + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}