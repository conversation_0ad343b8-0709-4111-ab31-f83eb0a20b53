package com.fasnote.alm.auth.feishu;

import java.util.HashMap;
import java.util.Map;

import com.fasnote.alm.plugin.manage.annotation.LicenseImplementation;
import com.fasnote.alm.plugin.manage.annotation.PremiumFeature;
import com.polarion.core.util.logging.Logger;
import com.polarion.platform.security.auth.IAuthenticator;

/**
 * 飞书许可证认证器增强实现类（许可证验证通过）
 *
 * 当许可证验证通过时，使用此实现类提供完整的飞书认证增强功能。
 * 使用 @LicenseImplementation 组合注解，自动包含 @Service 功能。
 */
@LicenseImplementation(level = "PREMIUM", description = "飞书认证增强功能", premium = true)
public class FeishuLicensedAuthenticatorEnhancer implements IFeishuAuthenticatorEnhancer {
    
    private static final Logger log = Logger.getLogger(FeishuLicensedAuthenticatorEnhancer.class);
    
    @Override
    @PremiumFeature(name = "feishu-authentication", description = "飞书认证增强功能", requiredLevel = "PREMIUM")
    public Map<String, IAuthenticator> createEnhancedAuthenticatorMap(Map<String, IAuthenticator> originalAuthenticators) {
        log.info("License validated successfully, enabling Feishu authentication enhancement");
        
        Map<String, IAuthenticator> enhancedAuthenticators = new HashMap<>(originalAuthenticators);
        
        // 替换OAuth2认证器为增强版本
        replaceOAuth2Authenticator(enhancedAuthenticators);
        
        return enhancedAuthenticators;
    }
    
    /**
     * 替换OAuth2认证器为增强版本
     *
     * @param authenticatorMap 认证器映射
     */
    private void replaceOAuth2Authenticator(Map<String, IAuthenticator> authenticatorMap) {
        try {
            // 检查是否存在OAuth2认证器
            if (authenticatorMap.containsKey("oauth2")) {
                IAuthenticator originalOAuth2 = authenticatorMap.get("oauth2");
                // 创建增强版OAuth2认证器
                IAuthenticator enhancedOAuth2 = new FeishuOAuth2Authenticator(originalOAuth2);

                // 替换原有的OAuth2认证器
                authenticatorMap.put("oauth2", enhancedOAuth2);

                log.debug("OAuth2 authenticator enhanced with Feishu support");
            }

        } catch (Exception e) {
            log.error("Failed to enhance OAuth2 authenticator", e);
        }
    }
    
    @Override
    public String getEnhancerType() {
        return "Licensed Premium Enhancer";
    }
}