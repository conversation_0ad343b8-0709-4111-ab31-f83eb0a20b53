package com.fasnote.alm.injection.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 限定符注解
 * 用于区分同一接口的不同实现
 */
@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface Qualifier {
    
    /**
     * 限定符名称
     */
    String value() default "";
    
    /**
     * 优先级（数字越小优先级越高）
     */
    int priority() default 0;
}