<?xml version="1.0" encoding="UTF-8"?>
<module id="com.fasnote.alm.auth.feishu" version="1.0.0">

  <!-- 飞书认证器管理器拦截器工厂 -->
    <service-point id="feishuAuthenticatorManagerInterceptorFactory"
                   interface="org.apache.hivemind.ServiceInterceptorFactory" visibility="private">
        <invoke-factory>
            <construct class="com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory"/>
        </invoke-factory>
    </service-point>

    <!-- 拦截认证器管理器，动态注册飞书认证器 -->
    <implementation service-id="com.polarion.platform.authenticatorProviderManager">
        <interceptor service-id="feishuAuthenticatorManagerInterceptorFactory"/>
    </implementation>

</module>
