package com.fasnote.alm.license.builder.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 包名处理工具类
 * 统一处理包名提取、匹配等常用操作
 */
public class PackageUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(PackageUtils.class);
    
    /**
     * 从导出包声明中提取包名
     * @param exportDeclaration 导出包声明，格式如: "com.example.api;version=1.0.0"
     * @return 包名
     */
    public static String extractPackageName(String exportDeclaration) {
        if (exportDeclaration == null || exportDeclaration.trim().isEmpty()) {
            return "";
        }
        
        String trimmed = exportDeclaration.trim();
        int semicolonIndex = trimmed.indexOf(';');
        if (semicolonIndex > 0) {
            return trimmed.substring(0, semicolonIndex).trim();
        }
        return trimmed;
    }
    
    /**
     * 从类名中获取包名
     * @param className 完整类名，如: "com.example.MyClass"
     * @return 包名，如: "com.example"
     */
    public static String getPackageName(String className) {
        if (className == null || className.trim().isEmpty()) {
            return "";
        }
        
        String trimmed = className.trim();
        int lastDotIndex = trimmed.lastIndexOf('.');
        return lastDotIndex > 0 ? trimmed.substring(0, lastDotIndex) : "";
    }
    
    /**
     * 检查类是否在导出的包中
     * @param className 类名
     * @param exportedPackages 导出包列表
     * @return 如果在导出包中返回true
     */
    public static boolean isInExportedPackage(String className, List<String> exportedPackages) {
        if (className == null || exportedPackages == null || exportedPackages.isEmpty()) {
            return false;
        }
        
        String packageName = getPackageName(className);
        
        for (String exportedPackage : exportedPackages) {
            String exportPackageName = extractPackageName(exportedPackage);
            if (packageName.equals(exportPackageName) || packageName.startsWith(exportPackageName + ".")) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查包名是否匹配模式
     * @param packageName 包名
     * @param pattern 模式，支持*通配符
     * @return 如果匹配返回true
     */
    public static boolean matchesPattern(String packageName, String pattern) {
        if (packageName == null || pattern == null) {
            return false;
        }
        
        if (pattern.startsWith("*")) {
            String suffix = pattern.substring(1);
            return packageName.endsWith(suffix);
        } else if (pattern.endsWith("*")) {
            String prefix = pattern.substring(0, pattern.length() - 1);
            return packageName.startsWith(prefix);
        } else {
            return packageName.equals(pattern);
        }
    }
    
    /**
     * 检查类名是否匹配模式
     * @param className 类名
     * @param pattern 模式，支持*通配符
     * @return 如果匹配返回true
     */
    public static boolean classMatchesPattern(String className, String pattern) {
        if (className == null || pattern == null) {
            return false;
        }
        
        if (pattern.startsWith("*")) {
            String suffix = pattern.substring(1);
            return className.endsWith(suffix);
        } else if (pattern.endsWith("*")) {
            String prefix = pattern.substring(0, pattern.length() - 1);
            return className.startsWith(prefix);
        } else {
            return className.equals(pattern);
        }
    }
    
    /**
     * 检查包名是否在包列表中
     * @param packageName 包名
     * @param packageList 包列表
     * @return 如果在列表中返回true
     */
    public static boolean isInPackageList(String packageName, List<String> packageList) {
        if (packageName == null || packageList == null || packageList.isEmpty()) {
            return false;
        }
        
        for (String pkg : packageList) {
            if (packageName.equals(pkg) || packageName.startsWith(pkg + ".")) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 标准化包名（移除多余的空白字符）
     * @param packageName 包名
     * @return 标准化后的包名
     */
    public static String normalizePackageName(String packageName) {
        if (packageName == null) {
            return "";
        }
        return packageName.trim().replaceAll("\\s+", "");
    }
}