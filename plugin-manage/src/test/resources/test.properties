# 测试配置文件

# 许可证存储配置
license.storage.directory=src/test/resources/test-licenses
license.backup.directory=src/test/resources/test-licenses/backup

# 缓存配置
license.cache.size=50
license.cache.ttl=1800

# 安全配置
security.validation.enabled=true
security.machine.code.binding=false

# 功能模式配置（测试环境简化）
license.strict.features=TestPremiumFeature
license.lenient.features=TestAdvancedFeature
license.basic.features=TestBasicFeature

# 默认降级策略
license.default.fallback=STUB
license.ui.fallback=STUB

# 监控配置
license.metrics.enabled=false
license.audit.enabled=true

# 测试专用配置
test.license.auto.cleanup=true
test.license.mock.security=true