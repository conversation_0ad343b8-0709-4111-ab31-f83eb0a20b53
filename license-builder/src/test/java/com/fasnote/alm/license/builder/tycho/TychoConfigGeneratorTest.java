package com.fasnote.alm.license.builder.tycho;

import com.fasnote.alm.license.builder.exception.BuildException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TychoConfigGenerator 单元测试
 * 专注于测试公共接口和完整的配置生成流程
 */
public class TychoConfigGeneratorTest {

    @TempDir
    Path tempDir;

    private Path pluginDir;
    private Path polarionPath;
    private TychoConfigGenerator generator;

    @BeforeEach
    void setUp() throws IOException {
        // 创建测试目录结构
        pluginDir = tempDir.resolve("test-plugin");
        polarionPath = tempDir.resolve("polarion");
        
        Files.createDirectories(pluginDir);
        Files.createDirectories(polarionPath.resolve("plugins"));
        
        // 创建基本的 MANIFEST.MF
        Path metaInfDir = pluginDir.resolve("META-INF");
        Files.createDirectories(metaInfDir);
        
        String manifestContent = "Manifest-Version: 1.0\n" +
            "Bundle-ManifestVersion: 2\n" +
            "Bundle-Name: Test Plugin\n" +
            "Bundle-SymbolicName: com.example.test.plugin\n" +
            "Bundle-Version: 1.0.0.qualifier\n" +
            "Bundle-Vendor: Test Vendor\n" +
            "Bundle-RequiredExecutionEnvironment: JavaSE-11\n" +
            "Require-Bundle: org.eclipse.core.runtime,\n" +
            " org.eclipse.ui\n";
        Files.writeString(metaInfDir.resolve("MANIFEST.MF"), manifestContent);
        
        // 创建基本的 .classpath 文件
        String classpathContent = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
            "<classpath>\n" +
            "    <classpathentry kind=\"con\" path=\"org.eclipse.jdt.launching.JRE_CONTAINER\"/>\n" +
            "    <classpathentry kind=\"con\" path=\"org.eclipse.pde.core.requiredPlugins\"/>\n" +
            "    <classpathentry kind=\"src\" path=\"src\"/>\n" +
            "    <classpathentry kind=\"output\" path=\"bin\"/>\n" +
            "</classpath>\n";
        Files.writeString(pluginDir.resolve(".classpath"), classpathContent);
        
        generator = new TychoConfigGenerator(pluginDir, polarionPath);
    }

    @Test
    void testConstructor() {
        assertNotNull(generator);
        // 验证构造函数正确设置了路径
    }

    @Test
    void testGenerateTychoConfigurationWithJunitClasspath() throws BuildException, IOException {
        // 添加 JUnit 依赖到 .classpath 来测试清理功能
        String classpathWithJunit = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
            "<classpath>\n" +
            "    <classpathentry kind=\"con\" path=\"org.eclipse.jdt.launching.JRE_CONTAINER\"/>\n" +
            "    <classpathentry kind=\"con\" path=\"org.eclipse.pde.core.requiredPlugins\"/>\n" +
            "    <classpathentry kind=\"con\" path=\"org.eclipse.jdt.junit.JUNIT_CONTAINER/5\"/>\n" +
            "    <classpathentry kind=\"src\" path=\"src\"/>\n" +
            "    <classpathentry kind=\"src\" path=\"src/test/java\"/>\n" +
            "    <classpathentry kind=\"output\" path=\"bin\"/>\n" +
            "</classpath>\n";
        Files.writeString(pluginDir.resolve(".classpath"), classpathWithJunit);

        // 执行完整配置生成
        generator.generateTychoConfiguration();

        // 验证所有文件都被创建
        assertTrue(Files.exists(pluginDir.resolve("pom.xml")));
        assertTrue(Files.exists(pluginDir.resolve("build.properties")));
        assertTrue(Files.exists(pluginDir.resolve("polarion-platform.target")));

        // 验证 .classpath 文件被正确处理
        String fixedContent = Files.readString(pluginDir.resolve(".classpath"));
        assertFalse(fixedContent.contains("JUNIT_CONTAINER"));
        assertFalse(fixedContent.contains("src/test/java"));
        assertTrue(fixedContent.contains("org.eclipse.jdt.launching.JRE_CONTAINER"));

        // 验证备份文件被创建
        assertTrue(Files.exists(pluginDir.resolve(".classpath.backup")));

        // 验证生成的文件内容
        String pomContent = Files.readString(pluginDir.resolve("pom.xml"));
        assertTrue(pomContent.contains("com.example.test.plugin"));
        assertTrue(pomContent.contains("1.0.0-SNAPSHOT"));
        assertTrue(pomContent.contains("tycho"));

        String buildPropsContent = Files.readString(pluginDir.resolve("build.properties"));
        assertTrue(buildPropsContent.contains("source.. = src/"));
        assertTrue(buildPropsContent.contains("output.. = bin/"));

        String targetContent = Files.readString(pluginDir.resolve("polarion-platform.target"));
        assertTrue(targetContent.contains("<?xml version=\"1.0\" encoding=\"UTF-8\""));
        assertTrue(targetContent.contains("target"));
    }

    @Test
    void testGenerateTychoConfiguration() throws BuildException {
        // 测试完整的配置生成流程
        assertDoesNotThrow(() -> {
            generator.generateTychoConfiguration();
        });
        
        // 验证所有必要文件都被创建
        assertTrue(Files.exists(pluginDir.resolve("pom.xml")));
        assertTrue(Files.exists(pluginDir.resolve("build.properties")));
        assertTrue(Files.exists(pluginDir.resolve("polarion-platform.target")));
    }

    @Test
    void testSettersAndGetters() {
        // 测试配置设置方法
        generator.setSkipObfuscation(false); // 启用混淆才会生成混淆相关内容
        generator.setLicenseValidity("DAYS_30");
        generator.setObfuscationTool("PROGUARD");
        generator.setObfuscationLevel("AGGRESSIVE");

        // 通过生成配置来验证设置是否生效
        assertDoesNotThrow(() -> {
            generator.generateTychoConfiguration();

            // 验证配置在生成的 pom.xml 中生效
            String pomContent = Files.readString(pluginDir.resolve("pom.xml"));

            // 调试：打印生成的内容
            System.out.println("=== 生成的 pom.xml 内容 ===");
            System.out.println(pomContent);
            System.out.println("=== 内容结束 ===");

            // 由于模板使用条件渲染，检查实际包含的内容
            // PROGUARD 应该出现在条件判断中
            boolean hasProguardPlugin = pomContent.contains("proguard-maven-plugin") ||
                                       pomContent.contains("proguard");
            assertTrue(hasProguardPlugin, "应该包含ProGuard相关内容");
        });
    }

    @Test
    void testErrorHandling() {
        // 测试错误处理 - 无效的插件目录
        Path invalidDir = tempDir.resolve("nonexistent");
        TychoConfigGenerator invalidGenerator = new TychoConfigGenerator(invalidDir, polarionPath);

        assertThrows(BuildException.class, () -> {
            invalidGenerator.generateTychoConfiguration();
        });
    }

    @Test
    void testCleanupAfterGeneration() throws BuildException {
        // 先生成配置（这会创建备份文件）
        generator.generateTychoConfiguration();
        assertTrue(Files.exists(pluginDir.resolve(".classpath.backup")));

        // 测试清理功能
        generator.cleanup();

        // 验证备份文件被删除
        assertFalse(Files.exists(pluginDir.resolve(".classpath.backup")));
    }
}
