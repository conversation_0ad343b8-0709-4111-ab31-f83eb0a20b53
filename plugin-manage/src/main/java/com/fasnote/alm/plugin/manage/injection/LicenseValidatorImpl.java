package com.fasnote.alm.plugin.manage.injection;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.ILicenseValidator;
import com.fasnote.alm.injection.api.ValidationResult;
import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.model.PluginLicense;

/**
 * 许可证验证器实现
 *
 * 实现DI框架定义的许可证验证接口，提供具体的许可证验证逻辑 作为manage框架与DI框架之间的适配器
 */
public class LicenseValidatorImpl implements ILicenseValidator {

	private static final Logger logger = LoggerFactory.getLogger(LicenseValidatorImpl.class);
	private static final String LOG_PREFIX = "[LicenseValidatorImpl] ";

	private final LicenseManager licenseManager;

	public LicenseValidatorImpl(LicenseManager licenseManager) {
		this.licenseManager = licenseManager;
	}

	/**
	 * 获取许可证详细信息
	 *
	 * @param pluginId 插件ID
	 * @return 许可证详细信息
	 */
	public String getLicenseDetails(String pluginId) {
		try {
			Optional<PluginLicense> licenseOpt = licenseManager.getPluginLicense(pluginId);
			if (!licenseOpt.isPresent()) {
				return "许可证不存在: " + pluginId;
			}

			PluginLicense license = licenseOpt.get();
			StringBuilder details = new StringBuilder();
			details.append("插件ID: ").append(license.getPluginId()).append("\n");
			details.append("产品名称: ").append(license.getProductName()).append("\n");
			details.append("许可证类型: ").append(license.getLicenseType()).append("\n");
			details.append("过期时间: ").append(license.getExpiryDate()).append("\n");
			details.append("最大用户数: ").append(license.getMaxUsers()).append("\n");

			return details.toString();

		} catch (Exception e) {
			logger.error(LOG_PREFIX + "获取许可证详细信息时发生异常: " + pluginId, e);
			return "获取许可证详细信息失败: " + e.getMessage();
		}
	}

	/**
	 * 获取许可证管理器
	 *
	 * @return 许可证管理器实例
	 */
	public LicenseManager getLicenseManager() {
		return licenseManager;
	}

	@Override
	public String getLicenseStatus(String pluginId) {
		try {
			// 检查插件是否需要许可证验证
			if (!licenseManager.isPluginRequiresLicense(pluginId)) {
				return "插件不需要许可证验证";
			}

			Optional<PluginLicense> licenseOpt = licenseManager.getPluginLicense(pluginId);
			if (!licenseOpt.isPresent()) {
				return "许可证不存在";
			}

			com.fasnote.alm.plugin.manage.model.ValidationResult result = licenseManager
					.validateLicense(licenseOpt.get());
			if (result.isValid()) {
				return "许可证有效";
			} else {
				return "许可证无效: " + result.getMessage();
			}

		} catch (Exception e) {
			logger.error(LOG_PREFIX + "获取许可证状态时发生异常: " + pluginId, e);
			return "许可证状态未知: " + e.getMessage();
		}
	}

	@Override
	public boolean isLicenseExists(String pluginId) {
		try {
			// 检查插件是否需要许可证验证
			if (!licenseManager.isPluginRequiresLicense(pluginId)) {
				logger.debug(LOG_PREFIX + "插件不需要许可证验证，返回许可证存在: {}", pluginId);
				return true;
			}

			Optional<PluginLicense> licenseOpt = licenseManager.getPluginLicense(pluginId);
			return licenseOpt.isPresent();
		} catch (Exception e) {
			logger.error(LOG_PREFIX + "检查许可证是否存在时发生异常: " + pluginId, e);
			return false;
		}
	}

	/**
	 * 刷新许可证状态
	 *
	 * @param pluginId 插件ID
	 * @return 刷新是否成功
	 */
	public boolean refreshLicenseStatus(String pluginId) {
		try {
			// 可以在这里添加许可证刷新逻辑
			logger.debug(LOG_PREFIX + "刷新许可证状态: " + pluginId);
			return true;
		} catch (Exception e) {
			logger.error(LOG_PREFIX + "刷新许可证状态时发生异常: " + pluginId, e);
			return false;
		}
	}

	@Override
	public boolean validateFeaturePermission(String pluginId, String featureName) {
		try {
			// 检查插件是否需要许可证验证
			if (!licenseManager.isPluginRequiresLicense(pluginId)) {
				logger.debug(LOG_PREFIX + "插件不需要许可证验证，功能权限验证通过: {} (功能: {})", pluginId, featureName);
				return true;
			}

			Optional<PluginLicense> licenseOpt = licenseManager.getPluginLicense(pluginId);
			if (!licenseOpt.isPresent()) {
				logger.warn(LOG_PREFIX + "插件许可证不存在，功能权限验证失败: {} (功能: {})", pluginId, featureName);
				return false;
			}

			PluginLicense license = licenseOpt.get();

			// 首先验证基本许可证
			com.fasnote.alm.plugin.manage.model.ValidationResult basicResult = licenseManager.validateLicense(license);
			if (!basicResult.isValid()) {
				logger.warn(LOG_PREFIX + "基本许可证验证失败，功能权限验证失败: {} (功能: {})", pluginId, featureName);
				return false;
			}

			// 然后验证功能权限（这里可以扩展更复杂的功能权限逻辑）
			// 目前简化为：如果基本许可证有效，则所有功能都可用
			logger.debug(LOG_PREFIX + "功能权限验证通过: {} (功能: {})", pluginId, featureName);
			return true;

		} catch (Exception e) {
			logger.error(LOG_PREFIX + "验证功能权限时发生异常: {} (功能: {})", pluginId, featureName, e);
			return false;
		}
	}

	@Override
	public boolean validatePluginLicense(String pluginId) {
		try {
			ValidationResult result = validatePluginLicenseDetailed(pluginId);
			return result.isValid();
		} catch (Exception e) {
			logger.error(LOG_PREFIX + "验证插件许可证时发生异常: " + pluginId, e);
			return false;
		}
	}

	@Override
	public ValidationResult validatePluginLicenseDetailed(String pluginId) {
		if (pluginId == null || pluginId.trim().isEmpty()) {
			return ValidationResult.failure("插件ID为空");
		}

		try {
			// 检查插件是否需要许可证验证
			if (!licenseManager.isPluginRequiresLicense(pluginId)) {
				logger.debug(LOG_PREFIX + "插件不需要许可证验证，直接返回成功: {}", pluginId);
				return ValidationResult.success("插件不需要许可证验证: " + pluginId);
			}

			Optional<PluginLicense> licenseOpt = licenseManager.getPluginLicense(pluginId);
			if (licenseOpt.isEmpty()) {
				return ValidationResult.failure("插件许可证不存在: " + pluginId);
			}

			com.fasnote.alm.plugin.manage.model.ValidationResult manageResult = licenseManager
					.validateLicense(licenseOpt.get());

			// 转换manage框架的ValidationResult到DI框架的ValidationResult
			if (manageResult.isValid()) {
				return ValidationResult.success(manageResult.getMessage());
			} else {
				return ValidationResult.failure(manageResult.getMessage(), manageResult.getErrorCode());
			}

		} catch (Exception e) {
			logger.error(LOG_PREFIX + "验证插件许可证详情时发生异常: " + pluginId, e);
			return ValidationResult.failure("许可证验证异常: " + e.getMessage(), "VALIDATION_EXCEPTION");
		}
	}
}
