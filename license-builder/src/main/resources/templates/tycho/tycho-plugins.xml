            <plugin>
                <groupId>org.eclipse.tycho</groupId>
                <artifactId>tycho-maven-plugin</artifactId>
                <version>${tycho_version}</version>
                <extensions>true</extensions>
            </plugin>

            <!-- Tycho 打包插件 - 处理 qualifier 时间戳 -->
            <plugin>
                <groupId>org.eclipse.tycho</groupId>
                <artifactId>tycho-packaging-plugin</artifactId>
                <version>${tycho_version}</version>
                <configuration>
                    <format>yyyyMMddHHmm</format>
                </configuration>
            </plugin>

            <!-- 目标平台配置 -->
            <plugin>
                <groupId>org.eclipse.tycho</groupId>
                <artifactId>target-platform-configuration</artifactId>
                <version>${tycho_version}</version>
                <configuration>
                    <target>
                        <file>polarion-platform.target</file>
                    </target>
                    <environments>
                        <environment>
                            <os>linux</os>
                            <ws>gtk</ws>
                            <arch>x86_64</arch>
                        </environment>
                        <environment>
                            <os>macosx</os>
                            <ws>cocoa</ws>
                            <arch>aarch64</arch>
                        </environment>
                    </environments>
                </configuration>
            </plugin>
            
            <!-- Tycho Surefire 插件 - 跳过测试 -->
            <plugin>
                <groupId>org.eclipse.tycho</groupId>
                <artifactId>tycho-surefire-plugin</artifactId>
                <version>${tycho_version}</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
