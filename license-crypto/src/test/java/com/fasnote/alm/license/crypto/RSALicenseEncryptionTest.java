package com.fasnote.alm.license.crypto;

import com.fasnote.alm.license.crypto.model.LicenseMetadata;
import org.junit.Before;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * RSA许可证加密解密单元测试
 * 测试完整的加密解密流程，确保builder和manage端逻辑一致
 */
public class RSALicenseEncryptionTest {

    private RSALicenseEncryption encryption;
    private RSAKeyManager keyManager;

    @Before
    public void setUp() {
        keyManager = new RSAKeyManager(); // 使用硬编码公钥
        encryption = new RSALicenseEncryption(keyManager);
    }

    @Test
    public void testEncryptAndDecryptLicensePackage() throws Exception {
        // 准备测试数据
        Map<String, byte[]> testClasses = new HashMap<>();
        testClasses.put("com.test.TestClass", "test class content".getBytes());
        testClasses.put("com.test.AnotherClass", "another class content".getBytes());

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("pluginId", "com.test.plugin");
        metadata.put("pluginName", "Test Plugin");
        metadata.put("pluginVersion", "1.0.0");
        metadata.put("contentHash", "testHash123");
        metadata.put("customProperties", new HashMap<String, Object>() {{
            put("encryptionVersion", "1.0");
            put("keyVersion", "v1");
        }});

        // 测试加密
        RSALicenseEncryption.EncryptedLicensePackage encryptedPackage =
            encryption.encryptAndSignLicensePackage(testClasses, metadata);

        assertNotNull(encryptedPackage);
        assertNotNull(encryptedPackage.getEncryptedClassData());
        assertNotNull(encryptedPackage.getDigitalSignature());
        assertEquals("1.0", encryptedPackage.getEncryptionVersion());
        assertEquals("v1", encryptedPackage.getKeyVersion());

        System.out.println("加密包创建成功:");
        System.out.println("- 加密版本: " + encryptedPackage.getEncryptionVersion());
        System.out.println("- 密钥版本: " + encryptedPackage.getKeyVersion());
        System.out.println("- 签名长度: " + encryptedPackage.getDigitalSignature().length());

        // 测试解密
        Map<String, byte[]> decryptedClasses = encryption.decryptAndVerifyLicensePackage(encryptedPackage);

        assertNotNull(decryptedClasses);
        assertEquals(testClasses.size(), decryptedClasses.size());

        for (Map.Entry<String, byte[]> entry : testClasses.entrySet()) {
            assertTrue(decryptedClasses.containsKey(entry.getKey()));
            assertArrayEquals(entry.getValue(), decryptedClasses.get(entry.getKey()));
        }

        System.out.println("解密验证成功:");
        System.out.println("- 解密类数量: " + decryptedClasses.size());
        for (String className : decryptedClasses.keySet()) {
            System.out.println("- 类: " + className + " (大小: " + decryptedClasses.get(className).length + " 字节)");
        }
    }

    @Test
    public void testLicenseMetadataSignatureContent() {
        // 测试LicenseMetadata签名内容生成
        LicenseMetadata metadata = new LicenseMetadata();
        metadata.setContentHash("testHash123");

        Map<String, Object> customProps = new HashMap<>();
        customProps.put("encryptionVersion", "1.0");
        customProps.put("keyVersion", "v1");
        metadata.setCustomProperties(customProps);

        String signatureContent = metadata.createSignatureContent();

        assertNotNull(signatureContent);
        assertTrue(signatureContent.contains("contentHash=testHash123"));
        assertTrue(signatureContent.contains("customProperties="));

        System.out.println("签名内容: " + signatureContent);
        System.out.println("签名内容长度: " + signatureContent.length());
    }

    @Test
    public void testDecryptAndVerifyLicenseFile() throws Exception {
        // 模拟许可证文件内容
        Map<String, Object> licenseData = new HashMap<>();
        licenseData.put("contentHash", "testHash123");
        licenseData.put("encryptedClassData", "testEncryptedData");

        Map<String, Object> customProperties = new HashMap<>();
        customProperties.put("encryptionVersion", "1.0");
        customProperties.put("keyVersion", "v1");
        customProperties.put("digitalSignature", "dGVzdFNpZ25hdHVyZQ=="); // Base64编码的"testSignature"
        customProperties.put("signatureAlgorithm", "SHA256withRSA");
        licenseData.put("customProperties", customProperties);

        // 这个测试主要验证接口调用不会出错
        // 实际的签名验证会失败，因为我们使用的是测试数据
        try {
            Map<String, byte[]> result = encryption.decryptAndVerifyLicenseFile(licenseData);
            // 如果没有异常，说明接口调用成功
            System.out.println("简化接口调用成功");
        } catch (SecurityException e) {
            // 预期的签名验证失败
            System.out.println("预期的签名验证失败: " + e.getMessage());
            assertTrue(e.getMessage().contains("数字签名验证失败"));
        }
    }



    @Test(expected = IllegalStateException.class)
    public void shouldThrowExceptionWhenEncryptingWithoutPrivateKey() throws Exception {
        // Given - 创建一个只有公钥的RSAKeyManager
        RSAKeyManager publicOnlyKeyManager = new RSAKeyManager(); // 只加载公钥
        RSALicenseEncryption publicOnlyEncryption = new RSALicenseEncryption(publicOnlyKeyManager);

        Map<String, byte[]> licensedClasses = new HashMap<>();
        licensedClasses.put("TestClass", "test class data".getBytes());

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("pluginId", "test.plugin");

        // When & Then - 应该抛出异常因为没有私钥
        publicOnlyEncryption.encryptAndSignLicensePackage(licensedClasses, metadata);
    }

    @Test(expected = Exception.class)
    public void shouldHandleInvalidDigitalSignature() throws Exception {
        // Given
        RSALicenseEncryption.EncryptedLicensePackage encryptedPackage =
            new RSALicenseEncryption.EncryptedLicensePackage();

        encryptedPackage.setEncryptionVersion("3.0");
        encryptedPackage.setKeyVersion("v1");
        encryptedPackage.setSignatureAlgorithm("SHA256withRSA");
        encryptedPackage.setDigitalSignature("invalid-signature");
        encryptedPackage.setEncryptedClassData("dGVzdCBkYXRh"); // base64 "test data"

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("pluginId", "test.plugin");
        encryptedPackage.setLicenseMetadata(metadata);

        // When & Then
        encryption.decryptAndVerifyLicensePackage(encryptedPackage);
    }

    @Test
    public void shouldBeSerializable() {
        RSALicenseEncryption.EncryptedLicensePackage encryptedPackage =
            new RSALicenseEncryption.EncryptedLicensePackage();

        // 验证类实现了Serializable接口
        assertTrue(encryptedPackage instanceof java.io.Serializable);
    }
}
