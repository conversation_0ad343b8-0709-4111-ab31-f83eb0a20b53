# 飞书 OAuth2 认证插件

## 项目简介

本项目是一个专为 Polarion ALM 开发的飞书 OAuth2 认证插件，实现了与飞书开放平台的无缝集成，为用户提供便捷的单点登录体验。

## 核心功能

### 🔐 OAuth2 认证集成
- 完整的 OAuth2 授权码流程实现
- 支持飞书开放平台标准认证协议
- 自动处理访问令牌获取和验证

### 🎯 智能重定向处理
- 支持 SPA（单页应用）路由重定向
- 解决单点登录重复进入 404 问题
- 智能识别和处理目标 URL 格式

### 🔄 状态参数编码
- 在 OAuth2 state 参数中安全编码目标 URL
- 避免 redirect_uri 包含复杂查询参数
- 确保认证流程的安全性和可靠性

### 📡 用户信息获取
- 内置飞书用户信息获取 Servlet
- 标准化用户信息映射
- 支持多种用户标识字段选择

## 技术架构

### 主要组件

#### 1. FeishuOAuth2Authenticator
- **功能**: 统一的 OAuth2 认证器
- **特性**: 
  - 根据配置 ID 自动识别飞书认证
  - 处理已认证用户的重定向逻辑
  - 集成标准 OAuth2 认证流程

#### 2. FeishuOAuth2LoginClient
- **功能**: 飞书 OAuth2 登录客户端
- **特性**:
  - 包装原始 OAuth2LoginClient
  - 在 state 参数中编码目标 URL

#### 3. FeishuOAuth2DefaultLoginHandler
- **功能**: 飞书 OAuth2 登录处理器
- **特性**:
  - 增强的重定向逻辑
  - 支持多种 URL 格式解析
  - SPA 路由正确处理

#### 4. FeishuUserInfoServlet
- **功能**: 用户信息获取服务
- **路径**: `/polarion/oauth-feishu/userinfo`
- **特性**:
  - Bearer Token 认证
  - 标准化 JSON 响应格式
  - 错误处理和日志记录

### 工具类

#### FeishuStateUtils
- state 参数编码/解码
- 目标 URL 安全处理
- 调试信息生成

#### OAuth2UrlHandler
- URL 参数处理
- SPA 路由转换
- 安全验证

#### FeishuApiClient
- 飞书 API 调用封装
- HTTP 连接管理
- 错误处理

#### FeishuUserInfoMapper
- 用户信息字段映射
- JSON 格式转换
- 动态 ID 字段选择

## 安装配置

### 1. 部署插件

将编译后的插件 JAR 文件部署到 Polarion 的插件目录：
```
${POLARION_HOME}/polarion/plugins/
```

### 2. 配置认证器

在 Polarion 的认证配置文件中添加飞书 OAuth2 配置：

```xml
<oauth2 id="feishu">
  <authorizeUrl>https://passport.feishu.cn/suite/passport/oauth/authorize</authorizeUrl>
  <tokenUrl>https://passport.feishu.cn/suite/passport/oauth/token</tokenUrl>
  <userUrl>https://your-polarion-domain/polarion/oauth-feishu/userinfo</userUrl>
  <clientId>YOUR_FEISHU_CLIENT_ID</clientId>
  <clientSecret>YOUR_FEISHU_CLIENT_SECRET</clientSecret>
  <nonce />
  <view>
    <text>飞书认证登录</text>
  </view>
  <responseParser>jsonpath</responseParser>
  <mapping>
    <id>$.id</id>
    <name>$.name</name>
    <email>$.email</email>
  </mapping>
</oauth2>
```

### 3. 飞书应用配置

在飞书开放平台配置应用回调地址：
```
https://your-polarion-domain/polarion/
```

## 配置说明

### 认证器 ID 规则
- 必须以 "feishu" 开头
- 支持多环境配置：`feishu-prod`、`feishu-test` 等

### 用户信息映射
插件支持以下用户字段映射：
- `id`: 用户唯一标识（支持 user_id、open_id、union_id）
- `name`: 用户姓名
- `email`: 邮箱地址（优先使用企业邮箱）
- `avatar`: 头像 URL
- `mobile`: 手机号码

#### 用户 ID 字段选择
插件支持三种用户 ID 字段，可通过系统属性配置：

**配置方法**：
在 Polarion 配置文件中添加：
```
feishu.user.id.field=user_id
```

**支持的字段类型**：
- `open_id`（默认）：应用内用户唯一标识，适用于单一应用场景
- `user_id`：企业内用户唯一标识，适用于企业内部集成
- `union_id`：跨应用用户唯一标识，适用于多应用统一身份

**选择建议**：
- 企业内部使用：推荐 `user_id`
- 多应用集成：推荐 `union_id`
- 单一应用：使用默认 `open_id`

### 必需配置项
- `userUrl`: 用户信息端点，必须配置为完整的 URL 路径，如：`https://your-polarion-domain/polarion/oauth-feishu/userinfo`

### 可选配置项
- `scope`: OAuth2 授权范围（默认使用飞书标准范围）

## 前端自动重定向配置

### Polarion 登录页面 JS 集成
为支持未登录用户自动重定向到飞书 SSO，需要在 Polarion 登录界面添加以下 JavaScript 代码：

```javascript
var str = window.location.href;
if(str.indexOf("from=feishu") != -1){
    // 获取当前页面的完整URL路径（包括hash部分）
    var currentPath = window.location.pathname + window.location.search + window.location.hash;
    // 移除from=feishu参数
    var cleanPath = currentPath.replace(/[?&]from=feishu/g, '');
    // 对URL进行编码
    var encodedFromUrl = encodeURIComponent(cleanPath);
    // 跳转到飞书SSO登录端点，携带原始URL作为fromUrl参数
    window.location="/polarion/ssoLogin/feishu?fromUrl=" + encodedFromUrl;
}
```

### 使用方法
1. **添加位置**：将上述代码添加到 Polarion 登录页面 `/opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/authapp/login.jsp`的 `<head>` 或页面底部
2. **触发方式**：在需要重定向的链接中添加 `from=feishu` 参数
3. **示例链接**：`https://your-polarion-domain/polarion/#/project/elibrary/workitem?id=EL-198&from=feishu`

### 工作原理
1. 检测 URL 中是否包含 `from=feishu` 参数
2. 获取当前完整路径（包括 hash 路由）
3. 清理 `from=feishu` 参数避免循环重定向
4. 将清理后的路径作为 `fromUrl` 参数传递给飞书 SSO 端点
5. 认证完成后自动重定向回原始页面

## OAuth2 认证流程

### 1. 用户访问受保护资源
```
用户 -> Polarion -> 检测未认证 -> 重定向到飞书登录
```

### 2. 飞书认证授权
```
飞书登录页面 -> 用户授权 -> 返回授权码到 Polarion
```

### 3. 令牌交换
```
Polarion -> 飞书令牌端点 -> 获取访问令牌
```

### 4. 用户信息获取
```
Polarion -> 插件 UserInfo Servlet -> 飞书用户信息 API -> 返回标准化用户信息
```

### 5. 认证完成重定向
```
Polarion -> 解析 state 参数 -> 重定向到原始目标页面
```

## 飞书集成技术细节

### API 端点
- **授权端点**: `https://passport.feishu.cn/suite/passport/oauth/authorize`
- **令牌端点**: `https://passport.feishu.cn/suite/passport/oauth/token`
- **用户信息端点**: `https://open.feishu.cn/open-apis/authen/v1/user_info`

### 认证流程特点
1. **state 编码**: 将目标 URL 编码到 state 参数中
2. **Bearer Token**: 使用标准 Bearer Token 方式调用用户信息 API
3. **错误处理**: 完善的错误处理和日志记录机制

### 安全特性
- URL 验证防止开放重定向攻击
- state 参数防 CSRF 攻击
- 访问令牌安全传输
- 用户信息脱敏日志记录

## 使用示例

### 基本登录流程
1. 用户访问 Polarion 工作项页面
2. 系统检测到未认证，重定向到飞书登录
3. 用户在飞书完成认证授权
4. 系统获取用户信息并创建会话
5. 自动重定向回原始工作项页面

### 多环境配置示例
```xml
<!-- 生产环境 -->
<oauth2 id="feishu-prod">
  <clientId>PROD_CLIENT_ID</clientId>
  <clientSecret>PROD_CLIENT_SECRET</clientSecret>
  <!-- 其他配置... -->
</oauth2>

<!-- 测试环境 -->
<oauth2 id="feishu-test">
  <clientId>TEST_CLIENT_ID</clientId>
  <clientSecret>TEST_CLIENT_SECRET</clientSecret>
  <!-- 其他配置... -->
</oauth2>
```

## 故障排除

### 常见问题

#### 1. 认证失败
- 检查飞书应用配置是否正确
- 验证回调地址是否匹配
- 确认客户端 ID 和密钥是否有效

#### 2. 重定向问题
- 检查目标 URL 格式是否正确
- 验证 SPA 路由配置
- 查看日志中的 URL 处理信息

#### 3. 用户信息获取失败
- 确认访问令牌是否有效
- 检查飞书 API 调用权限
- 验证用户信息映射配置

### 日志调试
启用调试日志查看详细信息：
```
com.fasnote.alm.auth.feishu=DEBUG
```

## 开发信息

- **开发语言**: Java 11+
- **框架依赖**: Polarion Platform API
- **构建工具**: Eclipse PDE
- **版本**: 1.0.0

