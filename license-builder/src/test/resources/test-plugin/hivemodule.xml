<?xml version="1.0" encoding="UTF-8"?>
<module id="feishu-auth" version="1.0.0">
    
    <service-point id="FeishuAuthService" interface="com.fasnote.alm.auth.feishu.api.IFeishuAuthService">
        <construct class="com.fasnote.alm.auth.feishu.service.FeishuAuthServiceImpl"/>
    </service-point>
    
    <service-point id="FeishuUserService" interface="com.fasnote.alm.auth.feishu.api.IFeishuUserService">
        <construct class="com.fasnote.alm.auth.feishu.service.FeishuUserServiceImpl"/>
    </service-point>
    
    <service-point id="FeishuTokenManager" interface="com.fasnote.alm.auth.feishu.api.IFeishuTokenManager">
        <construct class="com.fasnote.alm.auth.feishu.service.FeishuTokenManagerImpl"/>
    </service-point>
    
    <configuration-point id="FeishuConfig">
        <construct class="com.fasnote.alm.auth.feishu.config.FeishuConfigurationProvider"/>
    </configuration-point>

</module>