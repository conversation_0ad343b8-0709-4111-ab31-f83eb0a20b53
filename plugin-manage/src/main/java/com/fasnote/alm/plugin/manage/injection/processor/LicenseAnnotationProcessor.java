package com.fasnote.alm.plugin.manage.injection.processor;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.annotations.Service;
import com.fasnote.alm.plugin.manage.annotation.FallbackImplementation;
import com.fasnote.alm.plugin.manage.annotation.LicenseImplementation;

/**
 * 许可证注解处理器
 * 
 * 负责处理组合注解，将 @LicenseImplementation 和 @FallbackImplementation 
 * 注解转换为等效的 @Service 注解配置
 */
public class LicenseAnnotationProcessor {

    private static final Logger logger = LoggerFactory.getLogger(LicenseAnnotationProcessor.class);

    /**
     * 检查类是否有许可证相关注解
     */
    public static boolean hasLicenseAnnotation(Class<?> clazz) {
        return clazz.isAnnotationPresent(LicenseImplementation.class) ||
               clazz.isAnnotationPresent(FallbackImplementation.class);
    }

    /**
     * 从许可证注解中提取服务配置信息
     */
    public static ServiceConfig extractServiceConfig(Class<?> clazz) {
        if (clazz.isAnnotationPresent(LicenseImplementation.class)) {
            return extractFromLicenseImplementation(clazz);
        } else if (clazz.isAnnotationPresent(FallbackImplementation.class)) {
            return extractFromFallbackImplementation(clazz);
        }
        return null;
    }

    /**
     * 从 @LicenseImplementation 注解提取服务配置
     */
    private static ServiceConfig extractFromLicenseImplementation(Class<?> clazz) {
        LicenseImplementation annotation = clazz.getAnnotation(LicenseImplementation.class);
        
        ServiceConfig config = new ServiceConfig();
        config.setImplementationClass(clazz);
        config.setPriority(50); // 许可证实现默认高优先级
        config.setLicenseImplementation(true);
        
        // 提取服务接口
        Class<?>[] interfaces = annotation.interfaces();
        if (interfaces.length > 0) {
            config.setInterfaces(interfaces);
        } else {
            // 自动推断接口
            Class<?>[] inferredInterfaces = clazz.getInterfaces();
            if (inferredInterfaces.length > 0) {
                config.setInterfaces(new Class<?>[]{inferredInterfaces[0]});
            }
        }
        
        // 提取服务名称
        String serviceName = annotation.serviceName();
        if (!serviceName.isEmpty()) {
            config.setServiceName(serviceName);
        }
        
        // 提取生命周期
        config.setScope(convertScope(annotation.scope()));
        
        logger.debug("从 @LicenseImplementation 提取服务配置: {}", config);
        return config;
    }

    /**
     * 从 @FallbackImplementation 注解提取服务配置
     */
    private static ServiceConfig extractFromFallbackImplementation(Class<?> clazz) {
        FallbackImplementation annotation = clazz.getAnnotation(FallbackImplementation.class);
        
        ServiceConfig config = new ServiceConfig();
        config.setImplementationClass(clazz);
        config.setPriority(annotation.priority());
        config.setFallbackImplementation(true);
        
        // 提取服务接口
        Class<?>[] interfaces = annotation.interfaces();
        if (interfaces.length > 0) {
            config.setInterfaces(interfaces);
        } else {
            // 使用 value() 属性作为接口
            config.setInterfaces(new Class<?>[]{annotation.value()});
        }
        
        // 提取服务名称
        String serviceName = annotation.serviceName();
        if (!serviceName.isEmpty()) {
            config.setServiceName(serviceName);
        } else {
            String name = annotation.name();
            if (!name.isEmpty()) {
                config.setServiceName(name + "_fallback");
            }
        }
        
        // 提取生命周期
        config.setScope(convertScope(annotation.serviceScope()));
        
        logger.debug("从 @FallbackImplementation 提取服务配置: {}", config);
        return config;
    }

    /**
     * 转换生命周期枚举
     */
    private static ServiceConfig.Scope convertScope(Service.Scope scope) {
        switch (scope) {
            case SINGLETON:
                return ServiceConfig.Scope.SINGLETON;
            case PROTOTYPE:
                return ServiceConfig.Scope.PROTOTYPE;
            default:
                return ServiceConfig.Scope.SINGLETON;
        }
    }

    /**
     * 检查注解是否为组合注解（包含 @Service）
     */
    public static boolean isComposedAnnotation(Class<? extends Annotation> annotationType) {
        return annotationType.isAnnotationPresent(Service.class);
    }

    /**
     * 从组合注解中提取 @Service 注解信息
     */
    public static Service extractServiceAnnotation(Class<? extends Annotation> annotationType) {
        return annotationType.getAnnotation(Service.class);
    }

    /**
     * 服务配置信息
     */
    public static class ServiceConfig {
        private Class<?> implementationClass;
        private Class<?>[] interfaces = new Class<?>[0];
        private String serviceName = "";
        private int priority = 100;
        private Scope scope = Scope.SINGLETON;
        private boolean isLicenseImplementation = false;
        private boolean isFallbackImplementation = false;

        public enum Scope {
            SINGLETON, PROTOTYPE
        }

        // Getters and Setters
        public Class<?> getImplementationClass() { return implementationClass; }
        public void setImplementationClass(Class<?> implementationClass) { this.implementationClass = implementationClass; }

        public Class<?>[] getInterfaces() { return interfaces; }
        public void setInterfaces(Class<?>[] interfaces) { this.interfaces = interfaces; }

        public String getServiceName() { return serviceName; }
        public void setServiceName(String serviceName) { this.serviceName = serviceName; }

        public int getPriority() { return priority; }
        public void setPriority(int priority) { this.priority = priority; }

        public Scope getScope() { return scope; }
        public void setScope(Scope scope) { this.scope = scope; }

        public boolean isLicenseImplementation() { return isLicenseImplementation; }
        public void setLicenseImplementation(boolean licenseImplementation) { isLicenseImplementation = licenseImplementation; }

        public boolean isFallbackImplementation() { return isFallbackImplementation; }
        public void setFallbackImplementation(boolean fallbackImplementation) { isFallbackImplementation = fallbackImplementation; }

        @Override
        public String toString() {
            return String.format("ServiceConfig{class=%s, interfaces=%s, name='%s', priority=%d, scope=%s, license=%s, fallback=%s}",
                    implementationClass != null ? implementationClass.getSimpleName() : "null",
                    interfaces.length > 0 ? interfaces[0].getSimpleName() : "[]",
                    serviceName, priority, scope, isLicenseImplementation, isFallbackImplementation);
        }
    }
}
