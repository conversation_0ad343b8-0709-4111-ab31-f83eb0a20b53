# YGuard Rules for ${obfuscation_level} level obfuscation

# Keep main classes
<keep>
    <class name="${bundle_activator}"/>
    {{#each exported_packages}}
    <class name="${package_name}.**"/>
    {{/each}}
</keep>

# Obfuscation level: ${obfuscation_level}
{{#if_equals obfuscation_level "LIGHT"}}
<rename>
    <property name="obfuscation-prefix" value="a"/>
    <property name="language-conformity" value="legal"/>
</rename>
{{/if_equals}}

{{#if_equals obfuscation_level "STANDARD"}}
<rename>
    <property name="obfuscation-prefix" value=""/>
    <property name="language-conformity" value="compatible"/>
</rename>
{{/if_equals}}

{{#if_equals obfuscation_level "AGGRESSIVE"}}
<rename>
    <property name="obfuscation-prefix" value=""/>
    <property name="language-conformity" value="none"/>
    <property name="naming-scheme" value="small"/>
</rename>
{{/if_equals}}
