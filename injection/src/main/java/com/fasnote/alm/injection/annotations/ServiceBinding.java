package com.fasnote.alm.injection.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 服务绑定注解
 * 用于在业务插件中声明接口和实现类的绑定关系
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ServiceBinding {
    
    /**
     * 接口类型
     */
    Class<?> value();
    
    /**
     * 服务名称（可选，支持一个接口多个实现）
     */
    String name() default "";
    
    /**
     * 生命周期
     */
    Scope scope() default Scope.SINGLETON;
    
    /**
     * 优先级（数字越小优先级越高）
     */
    int priority() default 100;
    
    /**
     * 生命周期枚举
     */
    enum Scope {
        SINGLETON,  // 单例
        PROTOTYPE   // 原型（每次创建新实例）
    }
}