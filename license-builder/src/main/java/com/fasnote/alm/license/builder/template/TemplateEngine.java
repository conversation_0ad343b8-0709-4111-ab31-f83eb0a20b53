package com.fasnote.alm.license.builder.template;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 模板引擎 - 支持变量替换、条件渲染、循环渲染和模板包含
 */
public class TemplateEngine {
    private static final Logger logger = LoggerFactory.getLogger(TemplateEngine.class);
    
    // 模板语法正则表达式
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");
    private static final Pattern IF_EQUALS_PATTERN = Pattern.compile("\\{\\{#if_equals\\s+([^\\s]+)\\s+\"([^\"]+)\"\\}\\}([\\s\\S]*?)\\{\\{/if_equals\\}\\}");
    private static final Pattern EACH_PATTERN = Pattern.compile("\\{\\{#each\\s+([^}]+)\\}\\}([\\s\\S]*?)\\{\\{/each\\}\\}");
    private static final Pattern INCLUDE_PATTERN = Pattern.compile("\\{\\{>([^}]+)\\}\\}");
    
    private final List<Path> templatePaths;
    private final Map<String, String> templateCache;
    private boolean enableCache;
    private boolean useClasspath;
    private String classpathRoot;
    
    public TemplateEngine(List<Path> templatePaths) {
        this.templatePaths = new ArrayList<>(templatePaths);
        this.templateCache = new HashMap<>();
        this.enableCache = true;
        this.useClasspath = false;
        this.classpathRoot = null;
    }

    public TemplateEngine(Path templatePath) {
        this(Arrays.asList(templatePath));
    }

    public TemplateEngine() {
        this(Arrays.asList(Paths.get("templates")));
    }

    /**
     * 创建使用 classpath 资源的模板引擎
     * @param classpathRoot classpath 中的模板根目录，例如 "templates/"
     */
    public static TemplateEngine createClasspathEngine(String classpathRoot) {
        TemplateEngine engine = new TemplateEngine(new ArrayList<>());
        engine.useClasspath = true;
        engine.classpathRoot = classpathRoot.endsWith("/") ? classpathRoot : classpathRoot + "/";
        return engine;
    }

    /**
     * 设置模板根目录（用于测试）
     */
    public void setTemplateRoot(Path templateRoot) {
        this.templatePaths.clear();
        this.templatePaths.add(templateRoot);
        this.templateCache.clear(); // 清除缓存
    }
    
    /**
     * 渲染模板
     */
    public String render(String templateName, TemplateContext context) throws IOException {
        String template = loadTemplate(templateName);
        return renderTemplate(template, context);
    }

    /**
     * 直接处理模板字符串（用于测试）
     */
    public String process(String template, TemplateContext context) {
        if (template == null) {
            throw new IllegalArgumentException("模板不能为空");
        }
        if (context == null) {
            throw new IllegalArgumentException("模板上下文不能为空");
        }
        return renderTemplate(template, context);
    }
    
    /**
     * 渲染模板内容
     */
    public String renderTemplate(String template, TemplateContext context) {
        if (template == null) {
            throw new IllegalArgumentException("模板不能为空");
        }
        if (context == null) {
            throw new IllegalArgumentException("模板上下文不能为空");
        }

        String result = template;
        
        // 1. 处理模板包含 {{>template-name}}
        result = processIncludes(result, context);
        
        // 2. 处理条件渲染 {{#if condition}}...{{/if}}
        result = processConditions(result, context);
        
        // 3. 处理条件等值渲染 {{#if_equals var "value"}}...{{/if_equals}}
        result = processIfEquals(result, context);
        
        // 4. 处理循环渲染 {{#each items}}...{{/each}}
        result = processLoops(result, context);
        
        // 5. 处理变量替换 ${variable}
        result = processVariables(result, context);
        
        return result;
    }
    
    /**
     * 加载模板文件
     */
    private String loadTemplate(String templateName) throws IOException {
        // 检查缓存
        if (enableCache && templateCache.containsKey(templateName)) {
            return templateCache.get(templateName);
        }

        String content;
        if (useClasspath) {
            content = loadTemplateFromClasspath(templateName);
        } else {
            content = loadTemplateFromFileSystem(templateName);
        }

        if (enableCache) {
            templateCache.put(templateName, content);
        }

        return content;
    }

    /**
     * 从 classpath 加载模板文件
     */
    private String loadTemplateFromClasspath(String templateName) throws IOException {
        String resourcePath = classpathRoot + templateName;

        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(resourcePath)) {
            if (inputStream == null) {
                throw new IOException("模板文件不存在: " + templateName + "\n资源路径: " + resourcePath);
            }

            byte[] bytes = inputStream.readAllBytes();
            String content = new String(bytes, StandardCharsets.UTF_8);
            logger.debug("从 classpath 加载模板文件: {}", resourcePath);
            return content;
        }
    }

    /**
     * 从文件系统加载模板文件
     */
    private String loadTemplateFromFileSystem(String templateName) throws IOException {
        // 尝试从各个路径加载模板
        for (Path basePath : templatePaths) {
            Path templatePath = basePath.resolve(templateName);
            if (Files.exists(templatePath)) {
                String content = Files.readString(templatePath);
                logger.debug("加载模板文件: {}", templatePath.toAbsolutePath());
                return content;
            }
        }

        // 如果找不到模板文件，抛出异常
        StringBuilder pathsChecked = new StringBuilder();
        for (Path path : templatePaths) {
            pathsChecked.append("\n  - ").append(path.resolve(templateName).toAbsolutePath());
        }
        throw new IOException("模板文件不存在: " + templateName + "\n已检查路径:" + pathsChecked.toString());
    }
    
    /**
     * 处理模板包含
     */
    private String processIncludes(String template, TemplateContext context) {
        Matcher matcher = INCLUDE_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String includeName = matcher.group(1).trim();
            try {
                String includeContent = loadTemplate(includeName);
                String renderedInclude = renderTemplate(includeContent, context);
                matcher.appendReplacement(result, Matcher.quoteReplacement(renderedInclude));
            } catch (IOException e) {
                logger.warn("无法加载包含模板: {}", includeName, e);
                matcher.appendReplacement(result, "<!-- 模板包含失败: " + includeName + " -->");
            }
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 处理条件渲染
     */
    private String processConditions(String template, TemplateContext context) {
        // 使用递归方法处理嵌套条件
        return processNestedConditions(template, context);
    }

    /**
     * 递归处理嵌套条件
     */
    private String processNestedConditions(String template, TemplateContext context) {
        String result = template;
        boolean hasChanges = true;

        // 重复处理直到没有更多的条件语句
        while (hasChanges) {
            hasChanges = false;

            // 首先处理带else的条件
            String beforeElse = result;
            result = processIfElseConditions(result, context);
            if (!result.equals(beforeElse)) {
                hasChanges = true;
            }

            // 然后处理简单的if条件
            String beforeIf = result;
            result = processSimpleIfConditions(result, context);
            if (!result.equals(beforeIf)) {
                hasChanges = true;
            }
        }

        return result;
    }

    /**
     * 处理简单的if条件（不包含else）
     */
    private String processSimpleIfConditions(String template, TemplateContext context) {
        // 找到最内层的if条件进行处理
        int startPos = 0;
        StringBuilder result = new StringBuilder();

        while (startPos < template.length()) {
            int ifStart = template.indexOf("{{#if ", startPos);
            if (ifStart == -1) {
                // 没有更多的if语句
                result.append(template.substring(startPos));
                break;
            }

            // 添加if语句之前的内容
            result.append(template.substring(startPos, ifStart));

            // 找到对应的条件名称
            int conditionStart = ifStart + 6; // "{{#if ".length()
            int conditionEnd = template.indexOf("}}", conditionStart);
            if (conditionEnd == -1) {
                // 格式错误，跳过
                result.append(template.substring(ifStart, ifStart + 6));
                startPos = ifStart + 6;
                continue;
            }

            String condition = template.substring(conditionStart, conditionEnd).trim();

            // 找到匹配的{{/if}}
            int contentStart = conditionEnd + 2;
            int ifEnd = findMatchingEndIf(template, contentStart);

            if (ifEnd == -1) {
                // 没有找到匹配的{{/if}}，跳过
                result.append(template.substring(ifStart, contentStart));
                startPos = contentStart;
                continue;
            }

            String content = template.substring(contentStart, ifEnd);

            // 处理条件
            if (context.isTrue(condition)) {
                String renderedContent = renderTemplate(content, context);
                result.append(renderedContent);
            }

            startPos = ifEnd + 7; // "{{/if}}".length()
        }

        return result.toString();
    }

    /**
     * 找到匹配的{{/if}}标记
     */
    private int findMatchingEndIf(String template, int startPos) {
        int depth = 1;
        int pos = startPos;

        while (pos < template.length() && depth > 0) {
            int nextIf = template.indexOf("{{#if", pos);
            int nextEndIf = template.indexOf("{{/if}}", pos);

            if (nextEndIf == -1) {
                // 没有找到{{/if}}
                return -1;
            }

            if (nextIf != -1 && nextIf < nextEndIf) {
                // 找到嵌套的{{#if}}
                depth++;
                pos = nextIf + 5;
            } else {
                // 找到{{/if}}
                depth--;
                if (depth == 0) {
                    return nextEndIf;
                }
                pos = nextEndIf + 7;
            }
        }

        return -1;
    }

    /**
     * 处理带else的条件渲染
     */
    private String processIfElseConditions(String template, TemplateContext context) {
        // 使用手动解析来处理嵌套的if-else条件
        int startPos = 0;
        StringBuilder result = new StringBuilder();

        while (startPos < template.length()) {
            int ifStart = template.indexOf("{{#if ", startPos);
            if (ifStart == -1) {
                // 没有更多的if语句
                result.append(template.substring(startPos));
                break;
            }

            // 检查是否包含else
            int conditionStart = ifStart + 6;
            int conditionEnd = template.indexOf("}}", conditionStart);
            if (conditionEnd == -1) {
                result.append(template.substring(startPos, ifStart + 6));
                startPos = ifStart + 6;
                continue;
            }

            String condition = template.substring(conditionStart, conditionEnd).trim();
            int contentStart = conditionEnd + 2;

            // 找到匹配的{{/if}}
            int ifEnd = findMatchingEndIf(template, contentStart);
            if (ifEnd == -1) {
                result.append(template.substring(startPos, contentStart));
                startPos = contentStart;
                continue;
            }

            String fullContent = template.substring(contentStart, ifEnd);

            // 检查是否包含{{else}}
            int elsePos = findElseAtSameLevel(fullContent);

            if (elsePos != -1) {
                // 包含else的条件
                result.append(template.substring(startPos, ifStart));

                String ifContent = fullContent.substring(0, elsePos);
                String elseContent = fullContent.substring(elsePos + 8); // "{{else}}".length()

                if (context.isTrue(condition)) {
                    String renderedContent = renderTemplate(ifContent, context);
                    result.append(renderedContent);
                } else {
                    String renderedContent = renderTemplate(elseContent, context);
                    result.append(renderedContent);
                }

                startPos = ifEnd + 7; // "{{/if}}".length()
            } else {
                // 不包含else，跳过这个if语句，让简单if处理器处理
                result.append(template.substring(startPos, ifStart + 6));
                startPos = ifStart + 6;
            }
        }

        return result.toString();
    }

    /**
     * 在同一层级找到{{else}}标记
     */
    private int findElseAtSameLevel(String content) {
        int depth = 0;
        int pos = 0;

        while (pos < content.length()) {
            int nextIf = content.indexOf("{{#if", pos);
            int nextElse = content.indexOf("{{else}}", pos);
            int nextEndIf = content.indexOf("{{/if}}", pos);

            // 找到最近的标记
            int nextPos = content.length();
            String nextType = "";

            if (nextIf != -1 && nextIf < nextPos) {
                nextPos = nextIf;
                nextType = "if";
            }
            if (nextElse != -1 && nextElse < nextPos) {
                nextPos = nextElse;
                nextType = "else";
            }
            if (nextEndIf != -1 && nextEndIf < nextPos) {
                nextPos = nextEndIf;
                nextType = "endif";
            }

            if (nextPos == content.length()) {
                break;
            }

            switch (nextType) {
                case "if":
                    depth++;
                    pos = nextPos + 5;
                    break;
                case "else":
                    if (depth == 0) {
                        return nextPos;
                    }
                    pos = nextPos + 8;
                    break;
                case "endif":
                    depth--;
                    pos = nextPos + 7;
                    break;
            }
        }

        return -1;
    }
    
    /**
     * 处理条件等值渲染
     */
    private String processIfEquals(String template, TemplateContext context) {
        Matcher matcher = IF_EQUALS_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String variable = matcher.group(1).trim();
            String expectedValue = matcher.group(2);
            String content = matcher.group(3);
            
            String actualValue = context.getString(variable);
            if (expectedValue.equals(actualValue)) {
                String renderedContent = renderTemplate(content, context);
                matcher.appendReplacement(result, Matcher.quoteReplacement(renderedContent));
            } else {
                matcher.appendReplacement(result, "");
            }
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 处理循环渲染
     */
    private String processLoops(String template, TemplateContext context) {
        Matcher matcher = EACH_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String collectionName = matcher.group(1).trim();
            String itemTemplate = matcher.group(2);
            
            List<?> collection = context.getList(collectionName);
            if (collection != null && !collection.isEmpty()) {
                StringBuilder loopResult = new StringBuilder();
                for (Object item : collection) {
                    TemplateContext itemContext = context.createChild();
                    if (item instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> itemMap = (Map<String, Object>) item;
                        for (Map.Entry<String, Object> entry : itemMap.entrySet()) {
                            itemContext.put(entry.getKey(), entry.getValue());
                        }
                    } else {
                        itemContext.put("item", item);
                        // 如果是字符串，也设置为集合名的单数形式
                        String itemName = collectionName.endsWith("s") ? 
                            collectionName.substring(0, collectionName.length() - 1) : "item";
                        itemContext.put(itemName, item);
                    }
                    
                    String renderedItem = renderTemplate(itemTemplate, itemContext);
                    loopResult.append(renderedItem);
                }
                matcher.appendReplacement(result, Matcher.quoteReplacement(loopResult.toString()));
            } else {
                matcher.appendReplacement(result, "");
            }
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 处理变量替换
     */
    private String processVariables(String template, TemplateContext context) {
        Matcher matcher = VARIABLE_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String variableName = matcher.group(1);
            String value = context.getString(variableName);
            if (value != null) {
                matcher.appendReplacement(result, Matcher.quoteReplacement(value));
            } else {
                // 对于 Maven 属性（如 project.build.directory），不发出警告，直接保持原样
                if (!isMavenProperty(variableName)) {
                    logger.warn("模板变量未定义: {}", variableName);
                }
                matcher.appendReplacement(result, Matcher.quoteReplacement("${" + variableName + "}"));
            }
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * 判断是否为 Maven 属性
     */
    private boolean isMavenProperty(String variableName) {
        return variableName.startsWith("project.") ||
               variableName.startsWith("java.") ||
               variableName.equals("unqualifiedVersion") ||
               variableName.equals("buildQualifier") ||
               variableName.equals("yguard.jar");
    }
    
    /**
     * 清除模板缓存
     */
    public void clearCache() {
        templateCache.clear();
    }
    
    /**
     * 设置是否启用缓存
     */
    public void setEnableCache(boolean enableCache) {
        this.enableCache = enableCache;
        if (!enableCache) {
            clearCache();
        }
    }
}
