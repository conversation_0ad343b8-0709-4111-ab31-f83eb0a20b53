package com.fasnote.alm.license.builder.tycho;

import com.fasnote.alm.license.builder.exception.BuildException;
import com.fasnote.alm.license.builder.model.BundleInfo;
import com.fasnote.alm.license.builder.scanner.InvocationHandlerScanner;
import com.fasnote.alm.license.builder.scanner.SourceCodeScanner;
import com.fasnote.alm.license.builder.template.TemplateEngine;
import com.fasnote.alm.license.builder.template.TemplateContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Tycho 配置生成器
 * 负责生成 Maven Tycho 构建所需的配置文件
 */
public class TychoConfigGenerator {
    private static final Logger logger = LoggerFactory.getLogger(TychoConfigGenerator.class);

    private static final String TYCHO_VERSION = "2.7.5";
    private static final String JAVA_VERSION = "11";

    private final Path pluginDir;
    private final Path polarionPath;
    private boolean skipObfuscation = false;
    private String licenseValidity = "DAYS_90";
    private String obfuscationTool = "YGUARD";
    private String obfuscationLevel = "STANDARD";
    private String buildMode = "all";

    // 备份文件路径
    private Path classpathBackupFile;
    private boolean classpathModified = false;

    // 模板引擎
    private final TemplateEngine templateEngine;

    public TychoConfigGenerator(Path pluginDir, Path polarionPath) {
        this.pluginDir = pluginDir;
        this.polarionPath = polarionPath;
        this.classpathBackupFile = pluginDir.resolve(".classpath.backup");

        // 从系统属性读取配置
        this.skipObfuscation = Boolean.parseBoolean(System.getProperty("skip.obfuscation", "false"));
        this.licenseValidity = System.getProperty("license.validity", "DAYS_90");
        this.obfuscationTool = System.getProperty("obfuscation.tool", "YGUARD");
        this.obfuscationLevel = System.getProperty("obfuscation.level", "STANDARD");
        this.buildMode = System.getProperty("build.mode", "all");

        // 初始化模板引擎
        this.templateEngine = initializeTemplateEngine();

        // 调试信息
        logger.debug("TychoConfigGenerator 初始化 - 混淆工具: {}, 混淆级别: {}",
                    this.obfuscationTool, this.obfuscationLevel);
        logger.debug("系统属性 obfuscation.tool: {}", System.getProperty("obfuscation.tool"));
        logger.debug("实例变量 obfuscationTool: {}", this.obfuscationTool);
    }

    /**
     * 初始化模板引擎
     */
    private TemplateEngine initializeTemplateEngine() {
        // 使用 classpath 方式加载模板，从 JAR 包内的 resources/templates/ 目录读取
        TemplateEngine engine = TemplateEngine.createClasspathEngine("templates");
        logger.debug("模板引擎初始化完成，使用 classpath 模式，模板根目录: templates/");
        return engine;
    }

    // Setter 方法用于测试和配置
    public void setSkipObfuscation(boolean skipObfuscation) {
        this.skipObfuscation = skipObfuscation;
    }

    public void setLicenseValidity(String licenseValidity) {
        this.licenseValidity = licenseValidity;
    }

    public void setObfuscationTool(String obfuscationTool) {
        this.obfuscationTool = obfuscationTool;
    }

    public void setObfuscationLevel(String obfuscationLevel) {
        this.obfuscationLevel = obfuscationLevel;
    }

    /**
     * 生成完整的 Tycho 配置
     */
    public void generateTychoConfiguration() throws BuildException {
        try {
            logger.info("开始生成 Tycho 配置...");

            // 1. 解析 Bundle 信息
            BundleInfo bundleInfo = parseBundleInfo();
            logger.info("解析到 Bundle 信息: {} v{}", bundleInfo.getSymbolicName(), bundleInfo.getVersion());

            // 2. 处理版本号
            String mavenVersion = processBundleVersion(bundleInfo.getVersion());
            logger.info("处理版本号: {} -> {}", bundleInfo.getVersion(), mavenVersion);

            // 3. 清理和修复 .classpath 文件
            fixClasspathFile();

            // 4. 生成 pom.xml
            generatePomXml(bundleInfo, mavenVersion);

            // 5. 生成 build.properties
            generateBuildProperties();

            // 6. 生成目标平台文件
            generateTargetPlatform();

            logger.info("Tycho 配置生成完成");

        } catch (Exception e) {
            throw new BuildException("生成 Tycho 配置失败", e);
        }
    }

    /**
     * 解析 Bundle 信息并保存到构建配置
     */
    private BundleInfo parseBundleInfo() throws IOException {
        try {
            // 使用新的 BundleInfoResolver 解析
            BundleInfo bundleInfo = com.fasnote.alm.license.builder.util.BundleInfoResolver.getBundleInfo(pluginDir, true);

            // 将Bundle信息保存到构建配置文件，供许可证构建器使用
            saveBundleInfoToConfig(bundleInfo);

            // 保存 Bundle 信息以供 Maven 属性传递
            saveBundleInfoForMaven(bundleInfo);

            return bundleInfo;
        } catch (com.fasnote.alm.license.builder.exception.BuildException e) {
            throw new IOException("解析 Bundle 信息失败", e);
        }
    }

    /**
     * 将Bundle信息保存到构建配置文件
     */
    private void saveBundleInfoToConfig(BundleInfo bundleInfo) {
        try {
            Path configFile = pluginDir.resolve("target/bundle-info.yaml");
            Files.createDirectories(configFile.getParent());

            TemplateContext context = TemplateContext.fromBundleInfo(bundleInfo);
            String configContent = templateEngine.render("config/bundle-info.yaml", context);

            Files.writeString(configFile, configContent);
            logger.debug("Bundle信息已保存到: {}", configFile);

        } catch (IOException e) {
            logger.warn("保存Bundle信息失败: {}", e.getMessage());
        }
    }



    /**
     * 保存 Bundle 信息以供 Maven 属性传递
     */
    private void saveBundleInfoForMaven(BundleInfo bundleInfo) {
        try {
            Path propertiesFile = pluginDir.resolve("target/bundle-metadata.properties");
            Files.createDirectories(propertiesFile.getParent());

            java.util.Properties properties = new java.util.Properties();

            if (bundleInfo.getSymbolicName() != null) {
                properties.setProperty("bundle.symbolicName", bundleInfo.getSymbolicName());
            }
            if (bundleInfo.getVersion() != null) {
                properties.setProperty("bundle.version", bundleInfo.getVersion());
            }
            if (bundleInfo.getActivator() != null) {
                properties.setProperty("bundle.activator", bundleInfo.getActivator());
            }

            // 导出包列表转换为逗号分隔的字符串
            if (!bundleInfo.getExportedPackages().isEmpty()) {
                String exportedPackages = String.join(",", bundleInfo.getExportedPackages());
                properties.setProperty("bundle.exportedPackages", exportedPackages);
            }

            try (java.io.FileOutputStream fos = new java.io.FileOutputStream(propertiesFile.toFile())) {
                properties.store(fos, "Bundle metadata for Maven property passing");
            }

            logger.debug("Bundle 信息已保存到: {}", propertiesFile);

        } catch (IOException e) {
            logger.warn("保存 Bundle 信息失败: {}", e.getMessage());
        }
    }

    /**
     * 处理 Bundle 版本号
     * 对于 .qualifier 版本，保持原样让 Tycho 处理时间戳替换
     */
     private String processBundleVersion(String bundleVersion) {
        return com.fasnote.alm.license.builder.util.VersionUtils.processBundleVersionForMaven(bundleVersion);
    }

    /**
     * 生成最终的 JAR 文件名（让 Tycho 处理 qualifier 替换）
     */
    private String generateFinalName(BundleInfo bundleInfo, String mavenVersion) {
        return com.fasnote.alm.license.builder.util.VersionUtils.generateFinalName(bundleInfo, mavenVersion);
    }

    /**
     * 修复 .classpath 文件，移除 JUnit 依赖（临时修改，构建后会还原）
     */
    private void fixClasspathFile() throws IOException {
        Path classpathFile = pluginDir.resolve(".classpath");
        if (!Files.exists(classpathFile)) {
            logger.debug(".classpath 文件不存在，跳过修复");
            return;
        }

        // 备份原始文件
        backupClasspathFile(classpathFile);

        logger.info("临时修复 .classpath 文件，移除 JUnit 依赖...");

        List<String> lines = Files.readAllLines(classpathFile);
        StringBuilder newContent = new StringBuilder();
        boolean inJunitEntry = false;
        boolean hasJunitDependencies = false;

        for (String line : lines) {
            // 检测 JUnit 相关的条目
            if (line.contains("org.eclipse.jdt.junit.JUNIT_CONTAINER") ||
                line.contains("src/test/java") ||
                line.contains("mockito") ||
                line.contains("junit")) {

                hasJunitDependencies = true;

                if (line.trim().endsWith("/>")) {
                    // 单行条目，直接跳过
                    logger.debug("临时移除 JUnit 依赖行: {}", line.trim());
                    continue;
                } else {
                    // 多行条目开始
                    inJunitEntry = true;
                    logger.debug("开始跳过 JUnit 依赖块: {}", line.trim());
                    continue;
                }
            }

            // 如果在 JUnit 条目中，检查是否结束
            if (inJunitEntry) {
                if (line.contains("</classpathentry>")) {
                    inJunitEntry = false;
                    logger.debug("结束跳过 JUnit 依赖块");
                }
                continue;
            }

            newContent.append(line).append("\n");
        }

        if (hasJunitDependencies) {
            Files.write(classpathFile, newContent.toString().getBytes());
            classpathModified = true;
            logger.info(".classpath 文件临时修复完成（构建后将自动还原）");
        } else {
            logger.debug(".classpath 文件中未发现 JUnit 依赖，无需修复");
        }
    }

    /**
     * 备份 .classpath 文件
     */
    private void backupClasspathFile(Path classpathFile) throws IOException {
        if (!Files.exists(classpathBackupFile)) {
            Files.copy(classpathFile, classpathBackupFile);
            logger.debug("备份 .classpath 文件: {}", classpathBackupFile);
        } else {
            logger.debug(".classpath 备份文件已存在，跳过备份");
        }
    }

    /**
     * 还原 .classpath 文件到原始状态
     */
    public void restoreClasspathFile() throws IOException {
        if (!classpathModified) {
            logger.debug(".classpath 文件未被修改，无需还原");
            return;
        }

        Path classpathFile = pluginDir.resolve(".classpath");

        if (Files.exists(classpathBackupFile)) {
            Files.copy(classpathBackupFile, classpathFile, java.nio.file.StandardCopyOption.REPLACE_EXISTING);
            Files.delete(classpathBackupFile);
            logger.info("已还原 .classpath 文件到原始状态");
            classpathModified = false;
        } else {
            logger.warn("未找到 .classpath 备份文件，无法还原");
        }
    }

    /**
     * 清理临时文件
     */
    public void cleanup() {
        try {
            // 还原 .classpath 文件
            restoreClasspathFile();

            // 清理可能残留的备份文件
            if (Files.exists(classpathBackupFile)) {
                Files.delete(classpathBackupFile);
                logger.debug("清理备份文件: {}", classpathBackupFile);
            }
        } catch (IOException e) {
            logger.warn("清理临时文件时出错", e);
        }
    }

    /**
     * 生成 pom.xml 文件
     */
    private void generatePomXml(BundleInfo bundleInfo, String mavenVersion) throws IOException {
        logger.info("生成 pom.xml 文件...");

        // 生成最终的 JAR 文件名（包含时间戳）
        String finalName = generateFinalName(bundleInfo, mavenVersion);
        logger.info("设置最终文件名: {}", finalName);

        // 创建模板上下文
        TemplateContext context = createPomTemplateContext(bundleInfo, mavenVersion, finalName);

        // 渲染主模板
        String pomContent = templateEngine.render("pom/complete-pom.xml", context);

        Path pomFile = pluginDir.resolve("pom.xml");
        Files.write(pomFile, pomContent.getBytes());
        logger.info("pom.xml 文件生成完成: {}", pomFile);
    }



    /**
     * 创建POM模板上下文
     */
    private TemplateContext createPomTemplateContext(BundleInfo bundleInfo, String mavenVersion, String finalName) {
        TemplateContext context = TemplateContext.fromBundleInfo(bundleInfo);

        // 基本信息
        context.put("maven_version", mavenVersion);
        context.put("final_name", finalName);
        context.put("tycho_version", TYCHO_VERSION);
        context.put("java_version", JAVA_VERSION);

        // Maven 标准属性 - 这些将保持为 Maven 变量，不需要在模板上下文中定义

        // License Builder 配置
        context.put("license_builder_jar", getLicenseBuilderJarPath());
        context.put("license_builder_config", getLicenseBuilderConfigPath());

        // 混淆配置
        context.put("enable_obfuscation", !skipObfuscation);
        context.put("obfuscation_tool", obfuscationTool);
        context.put("obfuscation_level", obfuscationLevel);

        // 扫描InvocationHandler实现类并生成保护规则
        if (!skipObfuscation && "PROGUARD".equals(obfuscationTool)) {
            logger.info("开始扫描InvocationHandler实现类...");
            List<String> invocationHandlerRules = scanAndGenerateInvocationHandlerRules();
            logger.info("扫描完成，生成了 {} 条保护规则", invocationHandlerRules.size());
            context.put("invocation_handler_rules", invocationHandlerRules);
        } else {
            logger.debug("跳过InvocationHandler扫描 - skipObfuscation: {}, obfuscationTool: {}", skipObfuscation, obfuscationTool);
            context.put("invocation_handler_rules", new ArrayList<>());
        }

        // 许可证配置
        context.put("license_validity", licenseValidity);

        // 构建模式配置 - 将构建脚本模式映射到 Maven 模式
        String mavenMode = mapBuildModeToMavenMode(buildMode);
        context.put("maven_build_mode", mavenMode);

        // 机器绑定配置
        boolean enableMachineBinding = Boolean.parseBoolean(System.getProperty("machine.binding.enabled", "false"));
        context.put("machine_binding_enabled", enableMachineBinding);
        String machineId = System.getProperty("machine.id");
        if (machineId != null && !machineId.isEmpty()) {
            context.put("machine_id", machineId);
        }

        // 导出包字符串（用于Maven属性）
        String exportedPackages = bundleInfo.getExportedPackages().isEmpty() ?
            "" : String.join(",", bundleInfo.getExportedPackages());
        context.put("bundle_exported_packages", exportedPackages);

        // 数字签名配置 - 默认启用
        boolean enableSigning = Boolean.parseBoolean(System.getProperty("signing.enabled", "true"));
        context.put("enable_signing", enableSigning);

        // 签名配置参数 - 提供合理的默认值
        context.put("signing_keystore_path", System.getProperty("signing.keystore.path", "${user.home}/.keystore"));
        context.put("signing_alias", System.getProperty("signing.alias", "mykey"));
        context.put("signing_storepass", System.getProperty("signing.storepass", "changeit"));
        context.put("signing_keypass", System.getProperty("signing.keypass", "changeit"));
        context.put("signing_tsa_url", System.getProperty("signing.tsa.url", "http://timestamp.digicert.com"));
        context.put("signing_verbose", System.getProperty("signing.verbose", "true"));

        logger.debug("签名配置 - 启用: {}, 密钥库: {}, 别名: {}",
                    enableSigning,
                    System.getProperty("signing.keystore.path", ""),
                    System.getProperty("signing.alias", ""));

        return context;
    }

    /**
     * 扫描并生成InvocationHandler保护规则
     */
    private List<String> scanAndGenerateInvocationHandlerRules() {
        SourceCodeScanner sourceScanner = new SourceCodeScanner();
        InvocationHandlerScanner bytecodeScanner = new InvocationHandlerScanner();
        Set<String> invocationHandlerClasses = new HashSet<>();

        try {
            // 1. 扫描源代码（主要方式，适用于配置生成阶段）
            Path sourceDir = pluginDir.resolve("src/main/java");
            if (Files.exists(sourceDir)) {
                logger.info("扫描源代码目录: {}", sourceDir);
                invocationHandlerClasses.addAll(sourceScanner.scanSourceDirectory(sourceDir));
            }

            // 2. 扫描编译后的类文件目录（如果存在，用于验证和补充）
            Path classesDir = pluginDir.resolve("target/classes");
            if (Files.exists(classesDir)) {
                logger.info("扫描类文件目录: {}", classesDir);
                invocationHandlerClasses.addAll(bytecodeScanner.scanDirectory(classesDir));
            }

            // 3. 扫描依赖的JAR文件（扫描第三方依赖）
            Path libDir = pluginDir.resolve("lib");
            if (Files.exists(libDir)) {
                Files.list(libDir)
                    .filter(path -> path.toString().endsWith(".jar"))
                    .forEach(jarPath -> {
                        logger.info("扫描JAR文件: {}", jarPath);
                        invocationHandlerClasses.addAll(bytecodeScanner.scanJarFile(jarPath));
                    });
            }

            if (!invocationHandlerClasses.isEmpty()) {
                logger.info("发现 {} 个InvocationHandler实现类，生成保护规则", invocationHandlerClasses.size());
                return sourceScanner.generateProGuardRules(new ArrayList<>(invocationHandlerClasses));
            } else {
                logger.info("未发现InvocationHandler实现类");
                return new ArrayList<>();
            }

        } catch (Exception e) {
            logger.warn("扫描InvocationHandler实现类时出错: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 将构建脚本模式映射到 Maven 模式
     */
    private String mapBuildModeToMavenMode(String scriptMode) {
        switch (scriptMode.toLowerCase()) {
            case "obfuscate-only":
                return "obfuscate";
            case "full":
                return "all";
            default:
                // 如果是直接的 Maven 模式，直接返回
                return scriptMode;
        }
    }









    /**
     * 生成 build.properties 文件
     */
    private void generateBuildProperties() throws IOException {
        Path buildPropertiesFile = pluginDir.resolve("build.properties");
        if (Files.exists(buildPropertiesFile)) {
            logger.debug("build.properties 文件已存在，跳过生成");
            return;
        }

        logger.info("生成 build.properties 文件...");

        TemplateContext context = new TemplateContext();
        String buildPropertiesContent = templateEngine.render("config/build-properties.txt", context);

        Files.write(buildPropertiesFile, buildPropertiesContent.getBytes());
        logger.info("build.properties 文件生成完成: {}", buildPropertiesFile);
    }


    
    /**
     * 生成目标平台文件
     */
    private void generateTargetPlatform() throws IOException {
        Path targetFile = pluginDir.resolve("polarion-platform.target");
        if (Files.exists(targetFile)) {
            logger.debug("目标平台文件已存在，跳过生成");
            return;
        }

        logger.info("生成目标平台文件...");

        TemplateContext context = new TemplateContext();
        context.put("polarion_path", polarionPath.toString());

        // 检查Polarion路径是否存在
        boolean polarionPathExists = Files.exists(polarionPath) && Files.isDirectory(polarionPath);
        context.put("polarion_path_exists", polarionPathExists);

        if (polarionPathExists) {
            logger.info("检测到Polarion安装路径: {}", polarionPath);
        } else {
            logger.warn("Polarion路径不存在: {}，将使用标准Eclipse依赖", polarionPath);
        }

        String targetContent = templateEngine.render("tycho/target-platform.xml", context);

        Files.write(targetFile, targetContent.getBytes());
        logger.info("目标平台文件生成完成: {}", targetFile);
    }


    


    /**
     * 获取 License Builder JAR 路径
     */
    private String getLicenseBuilderJarPath() {
        // 获取当前工作目录
        String currentDir = System.getProperty("user.dir");

        // 尝试几个可能的路径（优先使用fat jar）
        String[] possiblePaths = {
            "target/license-builder-all.jar",
            "../target/license-builder-all.jar",
            "../../target/license-builder-all.jar",
            "license-builder-all.jar",
            "../license-builder-all.jar",
            "../../license-builder-all.jar",
            "target/license-builder.jar",
            "../target/license-builder.jar",
            "../../target/license-builder.jar"
        };

        for (String path : possiblePaths) {
            Path fullPath = Paths.get(currentDir).resolve(path);
            if (Files.exists(fullPath)) {
                return fullPath.toAbsolutePath().toString();
            }
        }

        // 如果找不到，返回默认的fat jar绝对路径
        return Paths.get(currentDir).resolve("target/license-builder-all.jar").toAbsolutePath().toString();
    }

    /**
     * 获取 License Builder 配置文件路径
     */
    private String getLicenseBuilderConfigPath() {
        // 在构建过程中动态生成的配置文件路径
        return "${project.build.directory}/license-build-config.yaml";
    }










}
