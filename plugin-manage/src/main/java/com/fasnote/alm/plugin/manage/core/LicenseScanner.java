package com.fasnote.alm.plugin.manage.core;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 许可证文件扫描器 负责扫描许可证目录，发现并匹配许可证文件与插件ID
 *
 * 支持两种匹配模式： 1. 文件名匹配：文件名去掉.lic后缀即为插件ID 2.
 * 配置映射：通过license-mapping.properties文件配置映射关系
 */
public class LicenseScanner {

	private static final Logger logger = LoggerFactory.getLogger(LicenseScanner.class);

	// 许可证文件扩展名
	private static final String LICENSE_FILE_EXTENSION = ".lic";

	// 映射配置文件名
	private static final String MAPPING_CONFIG_FILE = "license-mapping.properties";

	private final File licenseDirectory;

	/**
	 * 构造函数
	 *
	 * @param licenseDirectory 许可证目录
	 */
	public LicenseScanner(File licenseDirectory) {
		this.licenseDirectory = licenseDirectory;
	}

	/**
	 * 从文件名提取插件ID 支持以下格式： 1. com.fasnote.alm.auth.feishu.lic ->
	 * com.fasnote.alm.auth.feishu 2.
	 * com.fasnote.alm.auth.feishu-1.0.0.202507271630.lic ->
	 * com.fasnote.alm.auth.feishu
	 *
	 * @param baseFileName 去掉.lic后缀的文件名
	 * @return 插件ID
	 */
	private String extractPluginIdFromFileName(String baseFileName) {
		if (baseFileName == null || baseFileName.trim().isEmpty()) {
			return "";
		}

		// 检查是否包含版本号分隔符（连字符后跟版本号）
		// 版本号格式：-数字.数字.数字.时间戳
		String versionPattern = "-\\d+\\.\\d+\\.\\d+\\.\\d+$";
		if (baseFileName.matches(".*" + versionPattern)) {
			// 找到最后一个连字符的位置，该连字符前面是插件ID
			int lastDashIndex = baseFileName.lastIndexOf('-');
			if (lastDashIndex > 0) {
				String pluginId = baseFileName.substring(0, lastDashIndex);
				logger.debug("从带版本号的文件名提取插件ID: {} -> {}", baseFileName, pluginId);
				return pluginId;
			}
		}

		// 如果没有版本号，直接返回文件名作为插件ID
		logger.debug("从简单文件名提取插件ID: {}", baseFileName);
		return baseFileName;
	}

	/**
	 * 获取许可证目录
	 *
	 * @return 许可证目录
	 */
	public File getLicenseDirectory() {
		return licenseDirectory;
	}

	/**
	 * 验证许可证文件是否可读
	 *
	 * @param licenseFilePath 许可证文件路径
	 * @return 是否可读
	 */
	public boolean isLicenseFileReadable(String licenseFilePath) {
		try {
			File file = new File(licenseFilePath);
			return file.exists() && file.isFile() && file.canRead() && file.length() > 0;
		} catch (Exception e) {
			logger.debug("检查许可证文件可读性失败: {}", licenseFilePath, e);
			return false;
		}
	}

	/**
	 * 加载映射配置文件
	 *
	 * @param licenseDir 许可证目录
	 * @return 插件ID到许可证文件名的映射
	 */
	private Map<String, String> loadMappingConfiguration(File licenseDir) {
		Map<String, String> mappingConfig = new HashMap<>();

		File mappingFile = new File(licenseDir, MAPPING_CONFIG_FILE);
		if (!mappingFile.exists() || !mappingFile.isFile()) {
			logger.debug("映射配置文件不存在: {}", mappingFile.getAbsolutePath());
			return mappingConfig;
		}

		try (FileInputStream fis = new FileInputStream(mappingFile)) {
			Properties properties = new Properties();
			properties.load(fis);

			for (String pluginId : properties.stringPropertyNames()) {
				String licenseFileName = properties.getProperty(pluginId);
				if (licenseFileName != null && !licenseFileName.trim().isEmpty()) {
					mappingConfig.put(pluginId.trim(), licenseFileName.trim());
				}
			}

			logger.info("成功加载映射配置文件: {}, 共 {} 个映射", mappingFile.getAbsolutePath(), mappingConfig.size());

		} catch (IOException e) {
			logger.error("加载映射配置文件失败: {}", mappingFile.getAbsolutePath(), e);
		}

		return mappingConfig;
	}

	/**
	 * 刷新许可证文件扫描 重新扫描许可证目录，返回新发现的许可证文件
	 *
	 * @param existingLicenses 已存在的许可证映射
	 * @return 新发现的许可证文件映射
	 */
	public Map<String, String> refreshScan(Map<String, String> existingLicenses) {
		Map<String, String> allLicenses = scanLicenseFiles();
		Map<String, String> newLicenses = new HashMap<>();

		for (Map.Entry<String, String> entry : allLicenses.entrySet()) {
			String pluginId = entry.getKey();
			String licenseFilePath = entry.getValue();

			// 检查是否为新的许可证文件
			if (!existingLicenses.containsKey(pluginId) || !existingLicenses.get(pluginId).equals(licenseFilePath)) {
				newLicenses.put(pluginId, licenseFilePath);
			}
		}

		logger.info("刷新扫描完成，发现 {} 个新的许可证文件", newLicenses.size());

		return newLicenses;
	}

	/**
	 * 扫描许可证文件
	 *
	 * @return 插件ID到许可证文件路径的映射
	 */
	public Map<String, String> scanLicenseFiles() {
		Map<String, String> licenseFiles = new HashMap<>();

		logger.info("开始扫描许可证目录: {}", licenseDirectory.getAbsolutePath());

		// 检查许可证目录是否存在
		if (!licenseDirectory.exists() || !licenseDirectory.isDirectory()) {
			logger.warn("许可证目录不存在或不是目录: {}", licenseDirectory.getAbsolutePath());
			return licenseFiles;
		}

		// 1. 尝试从配置映射文件加载
		Map<String, String> mappingConfig = loadMappingConfiguration(licenseDirectory);
		if (!mappingConfig.isEmpty()) {
			logger.info("使用配置映射模式扫描许可证文件");
			licenseFiles.putAll(scanWithMappingConfig(licenseDirectory, mappingConfig));
		} else {
			logger.info("使用文件名匹配模式扫描许可证文件");
			// 2. 使用文件名匹配模式
			licenseFiles.putAll(scanWithFileNameMatching(licenseDirectory));
		}

		logger.info("许可证文件扫描完成，发现 {} 个许可证文件", licenseFiles.size());

		return licenseFiles;
	}

	/**
	 * 使用文件名匹配扫描许可证文件
	 *
	 * @param licenseDir 许可证目录
	 * @return 插件ID到许可证文件路径的映射
	 */
	private Map<String, String> scanWithFileNameMatching(File licenseDir) {
		Map<String, String> licenseFiles = new HashMap<>();

		File[] files = licenseDir.listFiles();
		if (files == null) {
			logger.warn("无法读取许可证目录内容: {}", licenseDir.getAbsolutePath());
			return licenseFiles;
		}

		for (File file : files) {
			if (file.isFile() && file.getName().endsWith(LICENSE_FILE_EXTENSION)) {
				// 从文件名提取插件ID（去掉.lic后缀）
				String fileName = file.getName();
				String baseFileName = fileName.substring(0, fileName.length() - LICENSE_FILE_EXTENSION.length());

				// 提取插件ID，支持带版本号的文件名
				String pluginId = extractPluginIdFromFileName(baseFileName);

				if (!pluginId.trim().isEmpty()) {
					licenseFiles.put(pluginId, file.getAbsolutePath());
					logger.debug("发现许可证文件: {} -> {}", pluginId, fileName);
				}
			}
		}

		return licenseFiles;
	}

	/**
	 * 使用配置映射扫描许可证文件
	 *
	 * @param licenseDir    许可证目录
	 * @param mappingConfig 映射配置
	 * @return 插件ID到许可证文件路径的映射
	 */
	private Map<String, String> scanWithMappingConfig(File licenseDir, Map<String, String> mappingConfig) {
		Map<String, String> licenseFiles = new HashMap<>();

		for (Map.Entry<String, String> entry : mappingConfig.entrySet()) {
			String pluginId = entry.getKey();
			String licenseFileName = entry.getValue();

			File licenseFile = new File(licenseDir, licenseFileName);
			if (licenseFile.exists() && licenseFile.isFile()) {
				licenseFiles.put(pluginId, licenseFile.getAbsolutePath());
				logger.debug("发现映射许可证文件: {} -> {}", pluginId, licenseFileName);
			} else {
				logger.warn("映射的许可证文件不存在: {} -> {}", pluginId, licenseFileName);
			}
		}

		return licenseFiles;
	}
}
