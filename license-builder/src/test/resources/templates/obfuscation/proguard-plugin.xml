            <!-- ProGuard Maven Plugin -->
            <plugin>
                <groupId>com.github.wvengen</groupId>
                <artifactId>proguard-maven-plugin</artifactId>
                <version>2.6.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>proguard</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <proguardVersion>7.3.2</proguardVersion>
                    <injar>${project.build.finalName}.jar</injar>
                    <outjar>${project.build.finalName}-obfuscated.jar</outjar>
                    <includeDependency>false</includeDependency>
                    <options>
                        <option>-dontoptimize</option>
                        <option>-dontpreverify</option>
                        <option>-dontshrink</option>
                        <option>-keep class ${bundle_activator} { *; }</option>
                        {{#each exported_packages}}
                        <option>-keep class ${package_name}.** { public *; }</option>
                        {{/each}}
                    </options>
                    <libs>
                        <lib>${java.home}/lib/rt.jar</lib>
                    </libs>
                </configuration>
            </plugin>
