package com.fasnote.alm.plugin.manage.security;

import java.lang.reflect.Proxy;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IInjectionContext;
import com.fasnote.alm.injection.api.IServiceInterceptor;
import com.fasnote.alm.plugin.manage.annotation.FeatureRequired;
import com.fasnote.alm.plugin.manage.annotation.LicenseRequired;
import com.fasnote.alm.plugin.manage.annotation.UserLimitCheck;
import com.fasnote.alm.plugin.manage.api.LicenseAware;
import com.fasnote.alm.plugin.manage.model.PluginLicense;

/**
 * 安全验证拦截器
 *
 * 在服务创建后自动为需要许可证验证的服务创建安全验证代理。 代理会拦截方法调用，根据注解自动进行许可证验证： - @LicenseRequired:
 * 基本许可证验证 - @FeatureRequired: 功能权限验证 - @UserLimitCheck: 用户数量限制验证
 *
 * 这样业务插件无需直接依赖SecurityValidator，安全验证作为横切关注点由框架自动处理。
 */
public class SecurityValidationInterceptor implements IServiceInterceptor {

	private static final Logger logger = LoggerFactory.getLogger(SecurityValidationInterceptor.class);

	private ISecurityValidator securityValidator;

	@Override
	public Object afterCreate(Class serviceClass, Object instance, IInjectionContext context) {
		if (instance == null) {
			return null;
		}

		try {
			// 获取许可证信息
			PluginLicense pluginLicense = getPluginLicense(instance);

			// 如果没有许可证信息，记录警告但不创建代理
			if (pluginLicense == null) {
				logger.debug("服务 {} 没有许可证信息，跳过安全验证代理创建", serviceClass.getName());
				return instance;
			}

			// 确保安全验证器可用
			if (securityValidator == null) {
				logger.warn("SecurityValidator未设置，无法创建安全验证代理");
				return instance;
			}

			// 创建安全验证代理
			Object proxy = createSecurityProxy(instance, pluginLicense);
			if (proxy != null) {
				logger.debug("为服务 {} 创建了安全验证代理", serviceClass.getName());
				return proxy;
			}

		} catch (Exception e) {
			logger.error("为服务 {} 创建安全验证代理失败", serviceClass.getName(), e);
		}

		return instance;
	}

	/**
	 * 创建安全验证代理
	 */
	private Object createSecurityProxy(Object target, PluginLicense pluginLicense) {
		if (target == null) {
			return null;
		}

		Class<?> targetClass = target.getClass();

		// 获取目标对象实现的所有接口
		Class<?>[] interfaces = targetClass.getInterfaces();

		if (interfaces.length == 0) {
			logger.debug("目标对象 {} 没有实现接口，无法创建JDK动态代理", targetClass.getName());
			// TODO: 可以考虑使用CGLIB创建代理
			return target;
		}

		try {
			// 创建安全验证处理器
			SecurityValidationHandler handler = new SecurityValidationHandler(target, securityValidator, pluginLicense);

			// 使用JDK动态代理创建代理对象
			return Proxy.newProxyInstance(targetClass.getClassLoader(), interfaces, handler);

		} catch (Exception e) {
			logger.error("创建安全验证代理失败", e);
			return target;
		}
	}

	/**
	 * 获取插件许可证信息
	 */
	private PluginLicense getPluginLicense(Object instance) {
		if (instance instanceof LicenseAware) {
			return ((LicenseAware) instance).getLicenseInfo();
		}

		// TODO: 可以添加其他获取许可证的方式
		// 例如从上下文、注解或配置中获取

		return null;
	}

	@Override
	public int getPriority() {
		return 15; // 在LicenseServiceInterceptor之后执行
	}

	/**
	 * 检查类是否有安全验证注解
	 */
	private boolean hasSecurityAnnotations(Class<?> clazz) {
		// 检查类级别注解
		if (clazz.isAnnotationPresent(LicenseRequired.class) || clazz.isAnnotationPresent(FeatureRequired.class)
				|| clazz.isAnnotationPresent(UserLimitCheck.class)) {
			return true;
		}

		// 检查方法级别注解
		try {
			for (java.lang.reflect.Method method : clazz.getDeclaredMethods()) {
				if (method.isAnnotationPresent(LicenseRequired.class)
						|| method.isAnnotationPresent(FeatureRequired.class)
						|| method.isAnnotationPresent(UserLimitCheck.class)) {
					return true;
				}
			}
		} catch (Exception e) {
			logger.debug("检查类 {} 的方法注解时发生错误", clazz.getName(), e);
		}

		return false;
	}

	/**
	 * 检查是否为系统类
	 */
	private boolean isSystemClass(Class<?> clazz) {
		if (clazz == null) {
			return true;
		}

		String packageName = clazz.getPackage() != null ? clazz.getPackage().getName() : "";

		return packageName.startsWith("java.") || packageName.startsWith("javax.")
				|| packageName.startsWith("org.osgi.") || packageName.startsWith("org.slf4j.")
				|| packageName.startsWith("org.apache.") || packageName.startsWith("com.sun.")
				|| packageName.startsWith("sun.");
	}

	/**
	 * 设置安全验证器
	 *
	 * @param securityValidator 安全验证器实例
	 */
	public void setSecurityValidator(ISecurityValidator securityValidator) {
		this.securityValidator = securityValidator;
	}

	@Override
	public boolean shouldIntercept(Class serviceClass) {
		// 排除系统类和框架类
		// 排除SecurityValidator本身，避免循环代理
		if (isSystemClass(serviceClass) || ISecurityValidator.class.isAssignableFrom(serviceClass)
				|| SecurityValidator.class.isAssignableFrom(serviceClass)) {
			return false;
		}

		// 检查类或其方法是否有许可证验证注解
		return hasSecurityAnnotations(serviceClass);
	}
}
