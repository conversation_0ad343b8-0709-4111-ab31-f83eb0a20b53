package com.fasnote.alm.license.builder.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 混淆映射文件读取器
 * 用于读取ProGuard生成的mapping文件，建立原始类名到混淆类名的映射
 */
public class ObfuscationMappingReader {
    
    private static final Logger logger = LoggerFactory.getLogger(ObfuscationMappingReader.class);
    
    /**
     * 读取ProGuard映射文件
     * 
     * @param mappingFilePath 映射文件路径
     * @return 原始类名到混淆类名的映射
     */
    public static Map<String, String> readProGuardMapping(Path mappingFilePath) {
        Map<String, String> classMapping = new HashMap<>();
        
        if (!Files.exists(mappingFilePath)) {
            logger.warn("混淆映射文件不存在: {}", mappingFilePath);
            return classMapping;
        }
        
        try (BufferedReader reader = Files.newBufferedReader(mappingFilePath)) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                
                // 跳过空行和注释
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }
                
                // 解析类映射行，格式：original.class.Name -> obfuscated.class.Name:
                if (line.contains(" -> ") && line.endsWith(":")) {
                    String[] parts = line.split(" -> ");
                    if (parts.length == 2) {
                        String originalClass = parts[0].trim();
                        String obfuscatedClass = parts[1].trim();
                        // 移除末尾的冒号
                        if (obfuscatedClass.endsWith(":")) {
                            obfuscatedClass = obfuscatedClass.substring(0, obfuscatedClass.length() - 1);
                        }
                        
                        classMapping.put(originalClass, obfuscatedClass);
                        logger.debug("类映射: {} -> {}", originalClass, obfuscatedClass);
                    }
                }
            }
            
            logger.info("成功读取混淆映射文件: {}, 共 {} 个类映射", mappingFilePath, classMapping.size());
            
        } catch (IOException e) {
            logger.error("读取混淆映射文件失败: " + mappingFilePath, e);
        }
        
        return classMapping;
    }
    
    /**
     * 将原始类名转换为混淆后的类名
     * 
     * @param originalClassName 原始类名
     * @param classMapping 类映射
     * @return 混淆后的类名，如果没有映射则返回原始类名
     */
    public static String getObfuscatedClassName(String originalClassName, Map<String, String> classMapping) {
        return classMapping.getOrDefault(originalClassName, originalClassName);
    }
    
    /**
     * 将原始类名转换为混淆后的JAR条目名
     * 
     * @param originalClassName 原始类名（如：com.example.MyClass）
     * @param classMapping 类映射
     * @return 混淆后的JAR条目名（如：com/example/a.class）
     */
    public static String getObfuscatedEntryName(String originalClassName, Map<String, String> classMapping) {
        String obfuscatedClassName = getObfuscatedClassName(originalClassName, classMapping);
        return obfuscatedClassName.replace('.', '/') + ".class";
    }
    
    /**
     * 批量转换原始类名为混淆后的JAR条目名
     * 
     * @param originalClassNames 原始类名集合
     * @param classMapping 类映射
     * @return 混淆后的JAR条目名集合
     */
    public static Map<String, String> getObfuscatedEntryNames(Iterable<String> originalClassNames, Map<String, String> classMapping) {
        Map<String, String> entryMapping = new HashMap<>();
        
        for (String originalClassName : originalClassNames) {
            String obfuscatedEntryName = getObfuscatedEntryName(originalClassName, classMapping);
            entryMapping.put(originalClassName, obfuscatedEntryName);
        }
        
        return entryMapping;
    }
    
    /**
     * 查找混淆映射文件
     * 按优先级查找可能的映射文件位置
     * 
     * @param workDir 工作目录
     * @return 映射文件路径，如果未找到则返回null
     */
    public static Path findMappingFile(Path workDir) {
        // 可能的映射文件位置
        String[] possiblePaths = {
            "proguard_map.txt",
            "mapping.txt", 
            "target/proguard_map.txt",
            "target/mapping.txt",
            "../target/proguard_map.txt",
            "../target/mapping.txt"
        };
        
        for (String relativePath : possiblePaths) {
            Path mappingPath = workDir.resolve(relativePath);
            if (Files.exists(mappingPath)) {
                logger.info("找到混淆映射文件: {}", mappingPath);
                return mappingPath;
            }
        }
        
        logger.warn("未找到混淆映射文件，将使用原始类名");
        return null;
    }
    
    /**
     * 检查是否有混淆映射
     * 
     * @param classMapping 类映射
     * @return 如果有有效的混淆映射则返回true
     */
    public static boolean hasObfuscationMapping(Map<String, String> classMapping) {
        if (classMapping == null || classMapping.isEmpty()) {
            return false;
        }
        
        // 检查是否真的有混淆（原始类名和混淆类名不同）
        for (Map.Entry<String, String> entry : classMapping.entrySet()) {
            if (!entry.getKey().equals(entry.getValue())) {
                return true;
            }
        }
        
        return false;
    }
}
