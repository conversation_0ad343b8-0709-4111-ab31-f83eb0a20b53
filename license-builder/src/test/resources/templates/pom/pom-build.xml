    <build>
        <finalName>${final_name}</finalName>
        <plugins>
            {{>tycho/tycho-plugins.xml}}

            {{#if enable_obfuscation}}
                {{#if_equals obfuscation_tool "PROGUARD"}}
                    <!-- ProGuard 插件 -->
                    <plugin>
                        <groupId>com.github.wvengen</groupId>
                        <artifactId>proguard-maven-plugin</artifactId>
                        <version>2.6.0</version>
                        <configuration>
                            <proguardVersion>7.3.2</proguardVersion>
                            <injar>${project.build.finalName}.jar</injar>
                            <outjar>${project.build.finalName}-obfuscated.jar</outjar>
                            <options>
                                <option>-dontoptimize</option>
                                <option>-keep class ${bundle_activator} { *; }</option>
                            </options>
                        </configuration>
                    </plugin>
                {{/if_equals}}
                {{#if_equals obfuscation_tool "YGUARD"}}
                    <!-- YGuard 插件 -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>3.1.0</version>
                        <configuration>
                            <target>
                                <taskdef name="yguard" classname="com.yworks.yguard.YGuardTask"/>
                                <yguard>
                                    <inoutpair in="${project.build.directory}/${project.build.finalName}.jar"
                                              out="${project.build.directory}/${project.build.finalName}-obfuscated.jar"/>
                                </yguard>
                            </target>
                        </configuration>
                    </plugin>
                {{/if_equals}}
            {{/if}}

            <!-- License 处理插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
            </plugin>
        </plugins>
    </build>
