package com.fasnote.alm.plugin.manage.test;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

// 测试import是否正常工作
import com.fasnote.alm.injection.facade.DI;

/**
 * 简单的依赖测试类 如果这个文件能正常编译，说明依赖配置正确
 */
public class DependencyTest {

	private static final Logger logger = LoggerFactory.getLogger(DependencyTest.class);

	public void testDependency() {
		// 测试是否能访问新的DI框架
		boolean available = DI.isAvailable();

		if (available) {
			logger.info("DI框架依赖配置正确！");
		} else {
			logger.warn("DI框架未初始化");
		}
	}
}