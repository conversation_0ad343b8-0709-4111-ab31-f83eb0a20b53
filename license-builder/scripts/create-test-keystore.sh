#!/bin/bash

# 创建测试用数字签名密钥库脚本
# 用于生成自签名证书，仅用于开发和测试目的

set -euo pipefail

# 脚本信息
SCRIPT_NAME="create-test-keystore.sh"
SCRIPT_VERSION="1.0.0"

# 默认配置
DEFAULT_KEYSTORE_PATH="$HOME/.keystore"
DEFAULT_ALIAS="mykey"
DEFAULT_VALIDITY_DAYS=365
DEFAULT_KEY_SIZE=2048

# 全局变量
KEYSTORE_PATH="$DEFAULT_KEYSTORE_PATH"
ALIAS="$DEFAULT_ALIAS"
STOREPASS=""
KEYPASS=""
VALIDITY_DAYS="$DEFAULT_VALIDITY_DAYS"
KEY_SIZE="$DEFAULT_KEY_SIZE"
FORCE=false

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
$SCRIPT_NAME v$SCRIPT_VERSION - 创建测试用数字签名密钥库

用法:
    $0 [options]

选项:
    -h, --help         显示此帮助信息
    -k, --keystore     密钥库路径 (默认: $DEFAULT_KEYSTORE_PATH)
    -a, --alias        密钥别名 (默认: $DEFAULT_ALIAS)
    -s, --storepass    密钥库密码 (必需)
    -p, --keypass      密钥密码 (默认: 与密钥库密码相同)
    -v, --validity     证书有效期天数 (默认: $DEFAULT_VALIDITY_DAYS)
    --key-size         密钥长度 (默认: $DEFAULT_KEY_SIZE)
    -f, --force        强制覆盖现有密钥库

示例:
    # 创建基本测试密钥库
    $0 --storepass changeit
    
    # 创建自定义配置的密钥库
    $0 --keystore ./my-keystore.jks --alias testkey --storepass mypassword --validity 730

注意:
    - 此脚本生成的证书仅用于开发和测试目的
    - 生产环境请使用正式的CA签发的证书
    - 密钥库密码和密钥密码将在命令行中可见，请注意安全性

EOF
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -k|--keystore)
                KEYSTORE_PATH="$2"
                shift 2
                ;;
            -a|--alias)
                ALIAS="$2"
                shift 2
                ;;
            -s|--storepass)
                STOREPASS="$2"
                shift 2
                ;;
            -p|--keypass)
                KEYPASS="$2"
                shift 2
                ;;
            -v|--validity)
                VALIDITY_DAYS="$2"
                shift 2
                ;;
            --key-size)
                KEY_SIZE="$2"
                shift 2
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                log_error "过多的参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 验证必需参数
    if [[ -z "$STOREPASS" ]]; then
        log_error "缺少密钥库密码参数 (--storepass)"
        show_help
        exit 1
    fi
    
    # 如果没有指定密钥密码，使用密钥库密码
    if [[ -z "$KEYPASS" ]]; then
        KEYPASS="$STOREPASS"
    fi
    
    # 验证数值参数
    if ! [[ "$VALIDITY_DAYS" =~ ^[0-9]+$ ]] || [[ "$VALIDITY_DAYS" -lt 1 ]]; then
        log_error "无效的有效期天数: $VALIDITY_DAYS"
        exit 1
    fi
    
    if ! [[ "$KEY_SIZE" =~ ^[0-9]+$ ]] || [[ "$KEY_SIZE" -lt 1024 ]]; then
        log_error "无效的密钥长度: $KEY_SIZE (最小1024)"
        exit 1
    fi
}

# 检查密钥库是否已存在
check_existing_keystore() {
    if [[ -f "$KEYSTORE_PATH" ]]; then
        if [[ "$FORCE" != "true" ]]; then
            log_error "密钥库文件已存在: $KEYSTORE_PATH"
            log_error "使用 --force 选项强制覆盖"
            exit 1
        else
            log_warn "将覆盖现有密钥库: $KEYSTORE_PATH"
            rm -f "$KEYSTORE_PATH"
        fi
    fi
}

# 创建密钥库目录
create_keystore_directory() {
    local keystore_dir
    keystore_dir="$(dirname "$KEYSTORE_PATH")"
    
    if [[ ! -d "$keystore_dir" ]]; then
        log_info "创建密钥库目录: $keystore_dir"
        mkdir -p "$keystore_dir"
    fi
}

# 生成密钥库和证书
generate_keystore() {
    log_info "生成密钥库和自签名证书..."
    log_info "  密钥库路径: $KEYSTORE_PATH"
    log_info "  密钥别名: $ALIAS"
    log_info "  有效期: $VALIDITY_DAYS 天"
    log_info "  密钥长度: $KEY_SIZE 位"
    
    # 使用keytool生成密钥库
    keytool -genkeypair \
        -alias "$ALIAS" \
        -keyalg RSA \
        -keysize "$KEY_SIZE" \
        -validity "$VALIDITY_DAYS" \
        -keystore "$KEYSTORE_PATH" \
        -storepass "$STOREPASS" \
        -keypass "$KEYPASS" \
        -dname "CN=Test Certificate, OU=Development, O=Test Organization, L=Test City, ST=Test State, C=US" \
        -storetype JKS
    
    log_success "密钥库创建完成: $KEYSTORE_PATH"
}

# 显示密钥库信息
show_keystore_info() {
    log_info "密钥库信息:"
    keytool -list -keystore "$KEYSTORE_PATH" -storepass "$STOREPASS" -v
}

# 显示使用说明
show_usage_instructions() {
    log_success "密钥库创建完成！"
    echo
    log_info "在构建脚本中使用此密钥库："
    echo "  ./scripts/build-plugin.sh /path/to/plugin \\"
    echo "    --keystore \"$KEYSTORE_PATH\" \\"
    echo "    --alias \"$ALIAS\" \\"
    echo "    --storepass \"$STOREPASS\" \\"
    echo "    --keypass \"$KEYPASS\""
    echo
    log_warn "注意: 此证书仅用于开发和测试目的，不应在生产环境中使用！"
}

# 主函数
main() {
    log_info "$SCRIPT_NAME v$SCRIPT_VERSION"
    log_info "创建测试用数字签名密钥库..."
    
    # 解析参数
    parse_arguments "$@"
    
    # 检查现有密钥库
    check_existing_keystore
    
    # 创建密钥库目录
    create_keystore_directory
    
    # 生成密钥库
    generate_keystore
    
    # 显示密钥库信息
    show_keystore_info
    
    # 显示使用说明
    show_usage_instructions
}

# 错误处理
trap 'log_error "脚本执行失败，退出码: $?"' ERR

# 执行主函数
main "$@"
