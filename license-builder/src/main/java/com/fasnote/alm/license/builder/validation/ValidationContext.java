package com.fasnote.alm.license.builder.validation;

import com.fasnote.alm.license.builder.core.BuildConfiguration;

import java.util.HashMap;
import java.util.Map;

/**
 * 验证上下文
 * 包含验证过程中需要的所有上下文信息
 */
public class ValidationContext {
    
    private final BuildPhase currentPhase;
    private final PluginType pluginType;
    private final String buildMode;
    private final BuildConfiguration configuration;
    private final Map<String, Object> properties;
    
    private ValidationContext(Builder builder) {
        this.currentPhase = builder.currentPhase;
        this.pluginType = builder.pluginType;
        this.buildMode = builder.buildMode;
        this.configuration = builder.configuration;
        this.properties = new HashMap<>(builder.properties);
    }
    
    public BuildPhase getCurrentPhase() {
        return currentPhase;
    }
    
    public PluginType getPluginType() {
        return pluginType;
    }
    
    public String getBuildMode() {
        return buildMode;
    }
    
    public BuildConfiguration getConfiguration() {
        return configuration;
    }
    
    public Map<String, Object> getProperties() {
        return new HashMap<>(properties);
    }
    
    public boolean hasProperty(String key) {
        return properties.containsKey(key);
    }
    
    @SuppressWarnings("unchecked")
    public <T> T getProperty(String key, Class<T> type) {
        Object value = properties.get(key);
        if (value == null) {
            return null;
        }
        if (type.isInstance(value)) {
            return (T) value;
        }
        throw new ClassCastException("属性 " + key + " 的类型不匹配，期望: " + type.getName() + ", 实际: " + value.getClass().getName());
    }
    
    public String getProperty(String key, String defaultValue) {
        Object value = properties.get(key);
        return value != null ? value.toString() : defaultValue;
    }
    
    public boolean getBooleanProperty(String key, boolean defaultValue) {
        Object value = properties.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return defaultValue;
    }
    
    /**
     * 创建构建器
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * 构建器类
     */
    public static class Builder {
        private BuildPhase currentPhase;
        private PluginType pluginType = PluginType.ALL;
        private String buildMode = "all";
        private BuildConfiguration configuration;
        private Map<String, Object> properties = new HashMap<>();
        
        public Builder currentPhase(BuildPhase currentPhase) {
            this.currentPhase = currentPhase;
            return this;
        }
        
        public Builder pluginType(PluginType pluginType) {
            this.pluginType = pluginType;
            return this;
        }
        
        public Builder buildMode(String buildMode) {
            this.buildMode = buildMode;
            return this;
        }
        
        public Builder configuration(BuildConfiguration configuration) {
            this.configuration = configuration;
            return this;
        }
        
        public Builder property(String key, Object value) {
            this.properties.put(key, value);
            return this;
        }
        
        public Builder properties(Map<String, Object> properties) {
            this.properties.putAll(properties);
            return this;
        }
        
        public ValidationContext build() {
            if (currentPhase == null) {
                throw new IllegalStateException("当前构建阶段不能为空");
            }
            if (configuration == null) {
                throw new IllegalStateException("构建配置不能为空");
            }
            return new ValidationContext(this);
        }
    }
    
    @Override
    public String toString() {
        return "ValidationContext{" +
                "currentPhase=" + currentPhase +
                ", pluginType=" + pluginType +
                ", buildMode='" + buildMode + '\'' +
                ", properties=" + properties.keySet() +
                '}';
    }
}
