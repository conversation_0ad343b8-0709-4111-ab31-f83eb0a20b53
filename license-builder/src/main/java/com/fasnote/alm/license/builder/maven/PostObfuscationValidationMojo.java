package com.fasnote.alm.license.builder.maven;

import com.fasnote.alm.license.builder.core.BuildConfiguration;
import com.fasnote.alm.license.builder.exception.BuildException;
import com.fasnote.alm.license.builder.model.BundleInfo;
import com.fasnote.alm.license.builder.util.BundleInfoResolver;
import com.fasnote.alm.license.builder.validation.*;
import com.fasnote.alm.license.builder.validation.validators.ClassIntegrityValidator;
import com.fasnote.alm.license.builder.validation.validators.DependencyResolutionValidator;
import org.apache.maven.plugin.AbstractMojo;
import org.apache.maven.plugin.MojoExecutionException;
import org.apache.maven.plugin.MojoFailureException;
import org.apache.maven.plugins.annotations.*;
import org.apache.maven.project.MavenProject;

import java.io.File;
import java.util.List;

/**
 * Maven插件：混淆后验证器
 * 
 * 在代码混淆完成后自动执行验证，确保混淆后的代码完整性
 * 主要验证：
 * - 类文件完整性
 * - 依赖关系解析
 * - Manifest文件正确性
 */
@Mojo(
    name = "post-obfuscation-validate",
    defaultPhase = LifecyclePhase.PROCESS_CLASSES,
    requiresDependencyResolution = ResolutionScope.COMPILE
)
public class PostObfuscationValidationMojo extends AbstractMojo {

    /**
     * Maven项目对象
     */
    @Parameter(defaultValue = "${project}", readonly = true, required = true)
    private MavenProject project;

    /**
     * 输入JAR文件路径（混淆后的JAR）
     */
    @Parameter(property = "post.obfuscation.inputJar", defaultValue = "${project.build.directory}/${project.build.finalName}-obfuscated.jar")
    private File inputJar;

    /**
     * 插件目录
     */
    @Parameter(property = "post.obfuscation.pluginDir", defaultValue = "${basedir}")
    private File pluginDir;

    /**
     * 跳过混淆后验证
     */
    @Parameter(property = "post.obfuscation.validation.skip", defaultValue = "false")
    private boolean skip;

    /**
     * 详细输出
     */
    @Parameter(property = "post.obfuscation.validation.verbose", defaultValue = "false")
    private boolean verbose;

    /**
     * 验证失败时是否中断构建
     */
    @Parameter(property = "post.obfuscation.validation.failOnError", defaultValue = "true")
    private boolean failOnError;

    @Override
    public void execute() throws MojoExecutionException, MojoFailureException {
        if (skip) {
            getLog().info("混淆后验证已跳过");
            return;
        }

        // 如果输入JAR不存在，跳过验证（可能还没有混淆）
        if (!inputJar.exists()) {
            getLog().info("混淆后JAR文件不存在，跳过验证: " + inputJar.getAbsolutePath());
            return;
        }

        try {
            getLog().info("开始执行混淆后验证...");

            // 创建验证器选择器并注册验证器
            ValidatorSelector selector = createValidatorSelector();

            // 创建验证上下文
            ValidationContext context = createValidationContext();

            // 获取Bundle信息
            BundleInfo bundleInfo = getBundleInfo();

            // 选择并执行验证器
            executeValidation(selector, bundleInfo, context);

            getLog().info("混淆后验证完成");

        } catch (BuildException e) {
            handleValidationFailure("混淆后验证失败: " + e.getMessage(), e);
        } catch (Exception e) {
            handleValidationFailure("混淆后验证过程中发生未知错误", e);
        }
    }

    /**
     * 创建验证器选择器并注册验证器
     */
    private ValidatorSelector createValidatorSelector() {
        ValidatorSelector selector = new ValidatorSelector();
        
        // 注册混淆后阶段的验证器
        selector.registerValidator(new ClassIntegrityValidator());
        selector.registerValidator(new DependencyResolutionValidator());
        
        if (verbose) {
            getLog().info("注册了 " + selector.getAllValidators().size() + " 个验证器");
        }
        
        return selector;
    }

    /**
     * 创建验证上下文
     */
    private ValidationContext createValidationContext() {
        BuildConfiguration config = createBuildConfiguration();
        
        return ValidationContext.builder()
                .currentPhase(BuildPhase.POST_OBFUSCATION)
                .pluginType(detectPluginType())
                .buildMode("post-obfuscation")
                .configuration(config)
                .property("inputJar", inputJar.getAbsolutePath())
                .property("pluginDir", pluginDir.getAbsolutePath())
                .property("verbose", verbose)
                .build();
    }

    /**
     * 检测插件类型
     */
    private PluginType detectPluginType() {
        try {
            BundleInfo bundleInfo = getBundleInfo();
            return PluginType.detectFromBundleInfo(bundleInfo);
        } catch (Exception e) {
            getLog().warn("无法检测插件类型，使用默认值: " + e.getMessage());
            return PluginType.ALL;
        }
    }

    /**
     * 获取Bundle信息
     */
    private BundleInfo getBundleInfo() throws BuildException {
        try {
            // 设置插件目录系统属性
            System.setProperty("plugin.dir", pluginDir.getAbsolutePath());
            
            return BundleInfoResolver.getBundleInfo();
        } catch (Exception e) {
            throw new BuildException("获取Bundle信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行验证
     */
    private void executeValidation(ValidatorSelector selector, BundleInfo bundleInfo, 
                                 ValidationContext context) throws BuildException {
        
        List<PhaseAwareValidator> validators = selector.selectValidators(context);
        
        if (validators.isEmpty()) {
            getLog().warn("没有找到适用的验证器");
            return;
        }

        getLog().info("执行 " + validators.size() + " 个验证器...");

        boolean hasFailure = false;
        for (PhaseAwareValidator validator : validators) {
            try {
                if (verbose) {
                    getLog().info("执行验证器: " + validator.getName());
                }

                ValidationResult result = validator.validate(bundleInfo, context);
                
                // 输出验证结果
                logValidationResult(result);
                
                if (!result.isSuccess() && validator.isCritical()) {
                    hasFailure = true;
                }
                
            } catch (Exception e) {
                getLog().error("验证器 " + validator.getName() + " 执行失败", e);
                if (validator.isCritical()) {
                    hasFailure = true;
                }
            }
        }

        if (hasFailure) {
            throw new BuildException("混淆后验证失败，存在关键错误");
        }
    }

    /**
     * 输出验证结果
     */
    private void logValidationResult(ValidationResult result) {
        if (result.isSuccess()) {
            if (verbose || result.hasMessages()) {
                getLog().info("[" + result.getValidatorName() + "] 验证通过");
                for (String message : result.getMessages()) {
                    getLog().info("  " + message);
                }
            }
        } else {
            getLog().error("[" + result.getValidatorName() + "] 验证失败");
            for (String message : result.getMessages()) {
                getLog().error("  " + message);
            }
        }
        
        // 输出警告信息
        for (String warning : result.getWarnings()) {
            getLog().warn("  " + warning);
        }
    }

    /**
     * 处理验证失败
     */
    private void handleValidationFailure(String message, Exception e) throws MojoExecutionException, MojoFailureException {
        if (failOnError) {
            throw new MojoExecutionException(message, e);
        } else {
            getLog().warn(message);
            if (e != null && verbose) {
                getLog().warn("详细错误信息", e);
            }
        }
    }

    /**
     * 创建构建配置
     */
    private BuildConfiguration createBuildConfiguration() {
        BuildConfiguration config = new BuildConfiguration();

        // 输入配置
        BuildConfiguration.InputConfig inputConfig = config.getInput();
        inputConfig.setSourceJar(inputJar.getAbsolutePath());

        // 日志配置
        BuildConfiguration.LoggingConfig loggingConfig = config.getLogging();
        loggingConfig.setLevel(verbose ? "DEBUG" : "INFO");

        return config;
    }
}
