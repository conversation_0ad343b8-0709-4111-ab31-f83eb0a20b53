package com.fasnote.alm.plugin.manage.web.dto;

/**
 * 插件信息DTO
 */
public class PluginInfoDto {
	private String pluginId;
	private String pluginName;
	private String version;
	private String status;
	private boolean licenseValid;
	private String licenseStatus;
	private String description;

	public PluginInfoDto() {
	}

	public PluginInfoDto(String pluginId, String pluginName, String version, String status, boolean licenseValid,
			String licenseStatus, String description) {
		this.pluginId = pluginId;
		this.pluginName = pluginName;
		this.version = version;
		this.status = status;
		this.licenseValid = licenseValid;
		this.licenseStatus = licenseStatus;
		this.description = description;
	}

	public String getDescription() {
		return description;
	}

	public String getLicenseStatus() {
		return licenseStatus;
	}

	// Getters and Setters
	public String getPluginId() {
		return pluginId;
	}

	public String getPluginName() {
		return pluginName;
	}

	public String getStatus() {
		return status;
	}

	public String getVersion() {
		return version;
	}

	public boolean isLicenseValid() {
		return licenseValid;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public void setLicenseStatus(String licenseStatus) {
		this.licenseStatus = licenseStatus;
	}

	public void setLicenseValid(boolean licenseValid) {
		this.licenseValid = licenseValid;
	}

	public void setPluginId(String pluginId) {
		this.pluginId = pluginId;
	}

	public void setPluginName(String pluginName) {
		this.pluginName = pluginName;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public void setVersion(String version) {
		this.version = version;
	}
}
