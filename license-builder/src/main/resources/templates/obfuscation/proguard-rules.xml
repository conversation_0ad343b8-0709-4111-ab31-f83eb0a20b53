                                <!-- 基本选项 - 修复字节码验证问题 -->
                                <option>-dontoptimize</option>
                                <option>-verbose</option>
                                <option>-dontnote</option>
                                <option>-dontwarn</option>
                                <option>-ignorewarnings</option>

                                <!-- 动态设置目标Java版本以确保字节码兼容性 -->
                                <option>-target ${java_target_version}</option>

                                <!-- 保留字节码验证所需的属性 -->
                                <option>-keepattributes Signature,InnerClasses,*Annotation*,StackMapTable,LineNumberTable</option>

                                {{#if exported_packages}}
                                <!-- OSGi Export-Package 公开接口保护 -->
                                {{#each exported_packages}}
                                <option>-keep class ${package_name}.* { public protected *; }</option>
                                {{/each}}
                                {{/if}}

                                {{#if xml_config_classes}}
                                <!-- XML配置文件引用类保护 -->
                                {{#each xml_config_classes}}
                                <option>-keep class ${class_name} { *; }</option>
                                {{/each}}
                                {{/if}}

                                {{#if_equals obfuscation_level "LIGHT"}}
                                <!-- LIGHT 级别：保持大部分公共API -->
                                <option>-keep public class * { public protected *; }</option>
                                <option>-keep class **.*Plugin { *; }</option>
                                <option>-keep class **.*Service { *; }</option>
                                <option>-keep class **.*Manager { *; }</option>
                                {{/if_equals}}

                                {{#if_equals obfuscation_level "AGGRESSIVE"}}
                                <!-- AGGRESSIVE 级别：保守混淆以避免字节码验证错误 -->
                                <option>-keep class com.fasnote.alm.plugin.manage.plugin.ILicensePlugin { *; }</option>
                                <option>-keep class * extends org.osgi.framework.BundleActivator { *; }</option>
                                <option>-keep class * implements org.eclipse.core.runtime.IExecutableExtension { *; }</option>
                                <option>-keep class * implements java.lang.reflect.InvocationHandler {
                                    public java.lang.Object invoke(java.lang.Object, java.lang.reflect.Method, java.lang.Object[]);
                                }</option>
                                <option>-keep class *$* implements java.lang.reflect.InvocationHandler {
                                    public java.lang.Object invoke(java.lang.Object, java.lang.reflect.Method, java.lang.Object[]);
                                }</option>
                                {{#each invocation_handler_rules}}
                                <option>${invocation_handler_rule}</option>
                                {{/each}}
                                <option>-keep public class * { public protected *; }</option>
                                {{/if_equals}}

                                {{#if_equals obfuscation_level "STANDARD"}}
                                <!-- STANDARD 级别：平衡混淆 -->
                                <option>-keep public class * { public protected *; }</option>
                                <option>-keep class **.*Plugin { public protected *; }</option>
                                <option>-keep class **.*Service { public protected *; }</option>
                                <option>-keep class com.fasnote.alm.plugin.manage.plugin.ILicensePlugin { *; }</option>
                                <option>-keep class * extends org.osgi.framework.BundleActivator { *; }</option>
                                <option>-keep class * implements org.eclipse.core.runtime.IExecutableExtension { *; }</option>
                                <option>-keep class * implements java.lang.reflect.InvocationHandler {
                                    public java.lang.Object invoke(java.lang.Object, java.lang.reflect.Method, java.lang.Object[]);
                                }</option>
                                <option>-keep class *$* implements java.lang.reflect.InvocationHandler {
                                    public java.lang.Object invoke(java.lang.Object, java.lang.reflect.Method, java.lang.Object[]);
                                }</option>
                                {{#each invocation_handler_rules}}
                                <option>${invocation_handler_rule}</option>
                                {{/each}}
                                <option>-repackageclasses ''</option>
                                {{/if_equals}}

                                <!-- 通用保持规则 -->
                                <option>-keep class * extends java.lang.Enum { *; }</option>
                                <option>-keep class * implements java.io.Serializable { static final long serialVersionUID; private static final java.io.ObjectStreamField[] serialPersistentFields; private void writeObject(java.io.ObjectOutputStream); private void readObject(java.io.ObjectInputStream); java.lang.Object writeReplace(); java.lang.Object readResolve(); }</option>

                                {{#if exported_packages}}
                                <!-- Export-Package 泛型方法保护 - 确保导出包中的泛型信息不被擦除 -->
                                {{#each exported_packages}}
                                <option>-keep,allowobfuscation class ${package_name}.* { public protected &lt;methods&gt;; }</option>
                                <option>-keep,allowobfuscation interface ${package_name}.* { &lt;methods&gt;; }</option>
                                {{/each}}
                                {{/if}}
