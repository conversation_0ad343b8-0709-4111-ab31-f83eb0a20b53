package com.fasnote.alm.plugin.manage.service;

import java.util.List;
import java.util.Set;

import com.fasnote.alm.plugin.manage.model.LicenseInfo;
import com.fasnote.alm.plugin.manage.model.ValidationResult;

/**
 * 许可证注册服务接口 职责：许可证注册、更新、删除
 */
public interface LicenseRegistryService {

	/**
	 * 激活插件许可证（加载实现类）
	 *
	 * @param pluginId 插件ID
	 * @return 激活结果
	 */
	ValidationResult activateLicense(String pluginId);

	/**
	 * 获取所有插件许可证信息
	 *
	 * @return 许可证信息列表
	 */
	List<LicenseInfo> getAllLicenseInfo();

	/**
	 * 获取插件许可证信息
	 *
	 * @param pluginId 插件ID
	 * @return 许可证信息
	 */
	LicenseInfo getLicenseInfo(String pluginId);

	/**
	 * 获取插件许可证状态
	 *
	 * @param pluginId 插件ID
	 * @return 许可证状态描述
	 */
	String getLicenseStatus(String pluginId);

	/**
	 * 获取所有已注册的插件ID
	 *
	 * @return 插件ID集合
	 */
	Set<String> getRegisteredPluginIds();

	/**
	 * 从文件加载并激活许可证
	 *
	 * @param pluginId 插件ID
	 * @return 激活结果
	 */
	ValidationResult loadAndActivateLicenseFromFile(String pluginId);

	/**
	 * 预加载插件许可证（仅解密验证，不加载实现类）
	 *
	 * @param pluginId    插件ID
	 * @param licenseData 许可证数据（加密）
	 * @return 预加载结果
	 */
	ValidationResult preloadLicense(String pluginId, byte[] licenseData);

	/**
	 * 注册插件许可证
	 *
	 * @param pluginId    插件ID
	 * @param licenseData 许可证数据（加密）
	 * @return 注册结果
	 */
	ValidationResult registerLicense(String pluginId, byte[] licenseData);

	/**
	 * 移除插件许可证
	 *
	 * @param pluginId 插件ID
	 */
	void removeLicense(String pluginId);

	/**
	 * 更新插件许可证
	 *
	 * @param pluginId       插件ID
	 * @param licenseContent 许可证内容
	 * @return 更新是否成功
	 */
	boolean updateLicense(String pluginId, String licenseContent);
}