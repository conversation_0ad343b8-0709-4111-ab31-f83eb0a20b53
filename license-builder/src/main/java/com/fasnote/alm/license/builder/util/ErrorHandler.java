package com.fasnote.alm.license.builder.util;

import com.fasnote.alm.license.builder.exception.BuildException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Callable;
import java.util.function.Supplier;

/**
 * 错误处理工具类
 * 提供统一的异常处理和操作封装
 */
public class ErrorHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(ErrorHandler.class);
    
    /**
     * 处理有返回值的操作
     * @param operation 操作描述
     * @param supplier 操作执行器
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws BuildException 构建异常
     */
    public static <T> T handleOperation(String operation, Supplier<T> supplier) throws BuildException {
        try {
            logger.debug("开始执行: {}", operation);
            T result = supplier.get();
            logger.debug("完成执行: {}", operation);
            return result;
        } catch (Exception e) {
            logger.error("操作失败: {} - {}", operation, e.getMessage());
            if (e instanceof BuildException) {
                throw (BuildException) e;
            }
            throw new BuildException("操作失败: " + operation, e);
        }
    }
    
    /**
     * 处理无返回值的操作
     * @param operation 操作描述
     * @param runnable 操作执行器
     * @throws BuildException 构建异常
     */
    public static void handleOperation(String operation, Runnable runnable) throws BuildException {
        try {
            logger.debug("开始执行: {}", operation);
            runnable.run();
            logger.debug("完成执行: {}", operation);
        } catch (Exception e) {
            logger.error("操作失败: {} - {}", operation, e.getMessage());
            if (e instanceof BuildException) {
                throw (BuildException) e;
            }
            throw new BuildException("操作失败: " + operation, e);
        }
    }
    
    /**
     * 处理可能抛出受检异常的操作
     * @param operation 操作描述
     * @param callable 操作执行器
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws BuildException 构建异常
     */
    public static <T> T handleCheckedOperation(String operation, Callable<T> callable) throws BuildException {
        try {
            logger.debug("开始执行: {}", operation);
            T result = callable.call();
            logger.debug("完成执行: {}", operation);
            return result;
        } catch (Exception e) {
            logger.error("操作失败: {} - {}", operation, e.getMessage());
            if (e instanceof BuildException) {
                throw (BuildException) e;
            }
            throw new BuildException("操作失败: " + operation, e);
        }
    }
    
    /**
     * 安全执行操作，失败时返回默认值
     * @param operation 操作描述
     * @param supplier 操作执行器
     * @param defaultValue 默认值
     * @param <T> 返回值类型
     * @return 操作结果或默认值
     */
    public static <T> T safeExecute(String operation, Supplier<T> supplier, T defaultValue) {
        try {
            logger.debug("安全执行: {}", operation);
            T result = supplier.get();
            logger.debug("安全执行完成: {}", operation);
            return result;
        } catch (Exception e) {
            logger.warn("安全执行失败，返回默认值: {} - {}", operation, e.getMessage());
            return defaultValue;
        }
    }
    
    /**
     * 安全执行操作，失败时忽略
     * @param operation 操作描述
     * @param runnable 操作执行器
     */
    public static void safeExecute(String operation, Runnable runnable) {
        try {
            logger.debug("安全执行: {}", operation);
            runnable.run();
            logger.debug("安全执行完成: {}", operation);
        } catch (Exception e) {
            logger.warn("安全执行失败，已忽略: {} - {}", operation, e.getMessage());
        }
    }
    
    /**
     * 重试执行操作
     * @param operation 操作描述
     * @param supplier 操作执行器
     * @param maxRetries 最大重试次数
     * @param retryDelay 重试延迟（毫秒）
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws BuildException 构建异常
     */
    public static <T> T retryOperation(String operation, Supplier<T> supplier, int maxRetries, long retryDelay) throws BuildException {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= maxRetries + 1; attempt++) {
            try {
                if (attempt > 1) {
                    logger.debug("重试执行 ({}/{}): {}", attempt - 1, maxRetries, operation);
                } else {
                    logger.debug("开始执行: {}", operation);
                }
                
                T result = supplier.get();
                
                if (attempt > 1) {
                    logger.info("重试成功: {}", operation);
                } else {
                    logger.debug("完成执行: {}", operation);
                }
                
                return result;
                
            } catch (Exception e) {
                lastException = e;
                
                if (attempt <= maxRetries) {
                    logger.warn("执行失败，将在{}ms后重试 ({}/{}): {} - {}", 
                              retryDelay, attempt, maxRetries, operation, e.getMessage());
                    
                    if (retryDelay > 0) {
                        try {
                            Thread.sleep(retryDelay);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new BuildException("操作被中断: " + operation, ie);
                        }
                    }
                } else {
                    logger.error("所有重试都失败了: {}", operation);
                }
            }
        }
        
        if (lastException instanceof BuildException) {
            throw (BuildException) lastException;
        }
        throw new BuildException("操作失败（已重试" + maxRetries + "次）: " + operation, lastException);
    }
    
    /**
     * 创建带上下文的构建异常
     * @param operation 操作描述
     * @param cause 原因异常
     * @return 构建异常
     */
    public static BuildException createBuildException(String operation, Throwable cause) {
        BuildException exception = new BuildException("操作失败: " + operation, cause);
        return exception.addContext("operation", operation);
    }
    
    /**
     * 创建带上下文的构建异常
     * @param operation 操作描述
     * @param message 详细消息
     * @param cause 原因异常
     * @return 构建异常
     */
    public static BuildException createBuildException(String operation, String message, Throwable cause) {
        BuildException exception = new BuildException(operation + ": " + message, cause);
        return exception.addContext("operation", operation).addContext("detail", message);
    }
}