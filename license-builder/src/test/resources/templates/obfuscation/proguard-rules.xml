# ProGuard Rules for ${obfuscation_level} level obfuscation

# Keep main classes
-keep class ${bundle_activator} { *; }

# Keep exported packages
{{#each exported_packages}}
-keep class ${package_name}.** { public *; }
{{/each}}

# Obfuscation level: ${obfuscation_level}
{{#if_equals obfuscation_level "LIGHT"}}
-dontoptimize
-dontpreverify
-dontshrink
{{/if_equals}}

{{#if_equals obfuscation_level "STANDARD"}}
-optimizationpasses 3
-dontpreverify
{{/if_equals}}

{{#if_equals obfuscation_level "AGGRESSIVE"}}
-optimizationpasses 5
-allowaccessmodification
-mergeinterfacesaggressively
{{/if_equals}}

# Keep annotations
-keepattributes *Annotation*

# Keep line numbers for debugging
-keepattributes SourceFile,LineNumberTable
