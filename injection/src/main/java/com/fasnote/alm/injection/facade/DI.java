package com.fasnote.alm.injection.facade;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IDependencyInjector;
import com.fasnote.alm.injection.osgi.InjectionActivator;

/**
 * 依赖注入门面类
 * 为最终用户提供简单、直观的API
 * 
 * 使用示例：
 * <pre>
 * // 获取服务
 * MyService service = DI.get(MyService.class);
 * 
 * // 获取命名服务
 * MyService namedService = DI.get(MyService.class, "premium");
 * 
 * // 创建并注入依赖
 * MyObject obj = DI.create(MyObject.class);
 * </pre>
 */
public final class DI {
    
    private static final Logger logger = LoggerFactory.getLogger(DI.class);
    
    private DI() {
        // 工具类，禁止实例化
    }
    
    /**
     * 获取服务实例
     * 
     * @param serviceClass 服务类型
     * @return 服务实例，如果未找到返回null
     */
    public static <T> T get(Class<T> serviceClass) {
        IDependencyInjector injector = getInjector();
        if (injector != null) {
            return injector.getService(serviceClass);
        }
        return null;
    }
    
    /**
     * 获取命名服务实例
     *
     * @param serviceClass 服务类型
     * @param name 服务名称
     * @return 服务实例，如果未找到返回null
     */
    public static <T> T get(Class<T> serviceClass, String name) {
        IDependencyInjector injector = getInjector();
        if (injector != null) {
            return injector.getService(serviceClass, name);
        }
        return null;
    }
    
    /**
     * 创建对象并注入依赖
     * 
     * @param clazz 类型
     * @return 注入依赖后的对象实例
     */
    public static <T> T create(Class<T> clazz) {
        IDependencyInjector injector = getInjector();
        if (injector != null) {
            try {
                return injector.createInstance(clazz);
            } catch (Exception e) {
                logger.error("创建实例失败: {}", clazz.getName(), e);
                return null;
            }
        }
        return null;
    }
    
    /**
     * 基于字符串接口名称获取服务实例
     * 支持动态服务查找，主要用于许可证验证系统
     *
     * @param interfaceName 接口全限定名称
     * @return 服务实例，如果未找到返回null
     */
    public static Object get(String interfaceName) {
        IDependencyInjector injector = getInjector();
        if (injector != null) {
            return injector.getServiceByName(interfaceName);
        }
        return null;
    }

    /**
     * 基于字符串接口名称获取服务实例（类型安全版本）
     * 支持动态服务查找，同时提供类型安全的转换
     *
     * @param interfaceName 接口全限定名称
     * @param expectedType 期望的返回类型
     * @return 服务实例，如果未找到或类型不匹配返回null
     */
    public static <T> T get(String interfaceName, Class<T> expectedType) {
        Object service = get(interfaceName);
        if (service != null && expectedType.isInstance(service)) {
            return expectedType.cast(service);
        }
        return null;
    }

    /**
     * 基于字符串接口名称获取命名服务实例
     *
     * @param interfaceName 接口全限定名称
     * @param serviceName 服务名称
     * @return 服务实例，如果未找到返回null
     */
    public static Object get(String interfaceName, String serviceName) {
        IDependencyInjector injector = getInjector();
        if (injector != null) {
            return injector.getServiceByName(interfaceName, serviceName);
        }
        return null;
    }

    /**
     * 检查依赖注入框架是否可用
     *
     * @return true表示可用，false表示不可用
     */
    public static boolean isAvailable() {
        return getInjector() != null;
    }

    /**
     * 获取依赖注入器实例
     *
     * 注意：此方法主要用于框架内部或高级用例，一般业务代码应使用get()和create()方法
     *
     * 迁移说明：替代已废弃的InjectionUtils.getDependencyInjector()方法
     *
     * @return 依赖注入器实例，如果未初始化返回null
     */
    public static IDependencyInjector getInjector() {
        return InjectionActivator.getDependencyInjector();
    }

}