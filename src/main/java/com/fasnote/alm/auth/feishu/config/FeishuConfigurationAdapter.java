package com.fasnote.alm.auth.feishu.config;

import org.jetbrains.annotations.NotNull;

import com.polarion.platform.security.auth.impl.oauth2.OAuth2Configuration;

/**
 * 飞书配置适配器
 *
 * 基于OAuth2配置ID前缀识别飞书认证，直接使用标准OAuth2Configuration
 */
public class FeishuConfigurationAdapter {

	/**
	 * 检查配置ID是否为飞书认证
	 *
	 * @param configId 配置ID
	 * @return 是否为飞书认证
	 */
	public static boolean isFeishuConfig(@NotNull String configId) {
		return configId.toLowerCase().startsWith("feishu");
	}

	/**
	 * 从配置ID创建OAuth2配置
	 *
	 * @param configId 配置ID
	 * @return OAuth2配置
	 */
	@NotNull
	public static OAuth2Configuration createOAuth2Configuration(@NotNull String configId) {
		// 直接使用标准OAuth2Configuration获取配置
		return OAuth2Configuration.getDefault(configId);
	}
}
