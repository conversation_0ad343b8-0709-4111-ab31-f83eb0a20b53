# 飞书插件配置指南（OSGi 服务机制）

## OSGi 服务机制

飞书插件现在使用 OSGi 服务机制来声明需要扫描的包路径，无需手动配置系统属性。

### 工作原理

1. **服务注册**：飞书插件启动时自动注册 `IPackageScanProvider` 服务
2. **服务发现**：`PackageScanManager` 通过 OSGi 服务跟踪器发现所有包扫描提供者
3. **自动扫描**：收集所有插件的扫描包路径，进行统一扫描

### 兼容性支持

- **OSGi 环境**：使用服务跟踪器动态发现包扫描提供者
- **非 OSGi 环境**：使用 ServiceLoader 机制加载包扫描提供者

## 验证配置

可以通过以下方式验证配置是否生效：

1. **查看启动日志**：
   - 应该看到 "FeishuPackageScanProvider OSGi 服务注册成功" 的日志
   - 应该看到 "飞书插件实现类将通过 OSGi 服务自动扫描注册" 的日志
   - 应该看到扫描包路径信息

2. **运行验证器**：
   ```java
   FeishuRegistrationVerifier.verifyFeishuRegistration();
   ```

## 注解实现类

飞书插件包含以下注解实现类：

### 回退实现
```java
@FallbackImplementation(
    value = IFeishuAuthenticatorEnhancer.class,
    description = "飞书认证增强默认实现",
    priority = 100
)
public class FeishuDefaultAuthenticatorEnhancer implements IFeishuAuthenticatorEnhancer
```

### 许可证实现
```java
@LicenseImplementation(
    level = "PREMIUM", 
    description = "飞书认证增强功能", 
    premium = true
)
public class FeishuLicensedAuthenticatorEnhancer implements IFeishuAuthenticatorEnhancer
```

## 工作原理

1. **插件启动**：`Activator.start()` 注册 `IPackageScanProvider` 服务
2. **服务发现**：`PackageScanManager` 发现所有包扫描提供者
3. **收集包路径**：收集所有插件声明的扫描包路径
4. **自动扫描**：`LicenseModule` 扫描收集到的包路径
5. **注解发现**：发现带有 `@FallbackImplementation` 和 `@LicenseImplementation` 注解的类
6. **自动注册**：将实现类注册到 DI 容器
7. **运行时选择**：根据许可证状态选择合适的实现

## 故障排除

如果自动扫描没有生效，检查：

1. **OSGi 服务**：确认 `IPackageScanProvider` 服务已注册
2. **注解**：确认实现类有正确的注解
3. **包路径**：确认实现类在正确的包路径下
4. **ServiceLoader 配置**：确认 `META-INF/services` 文件存在
5. **日志**：查看扫描和注册相关的日志信息

## 核心组件

### IPackageScanProvider 服务
```java
public class FeishuPackageScanProvider implements IPackageScanProvider {
    @Override
    public String[] getScanPackages() {
        return new String[] { "com.fasnote.alm.auth.feishu" };
    }

    @Override
    public String getPluginId() {
        return "feishu-auth-plugin";
    }
}
```

### ServiceLoader 配置
文件：`META-INF/services/com.fasnote.alm.plugin.manage.api.IPackageScanProvider`
内容：`com.fasnote.alm.auth.feishu.FeishuPackageScanProvider`
