package com.fasnote.alm.injection.api;

/**
 * 服务拦截上下文类
 * 
 * 从 IServiceValidationInterceptor 内部类提取为独立类，避免内部类在混淆和依赖解析时的复杂性
 * 包含拦截过程中需要的上下文信息
 */
public class InterceptContext {
    private final String pluginId;
    private final String requestId;
    private final long timestamp;
    
    public InterceptContext(String pluginId) {
        this(pluginId, generateRequestId(), System.currentTimeMillis());
    }
    
    public InterceptContext(String pluginId, String requestId, long timestamp) {
        this.pluginId = pluginId;
        this.requestId = requestId;
        this.timestamp = timestamp;
    }
    
    public String getPluginId() {
        return pluginId;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    private static String generateRequestId() {
        return "req-" + System.nanoTime();
    }
    
    @Override
    public String toString() {
        return String.format("InterceptContext{pluginId='%s', requestId='%s', timestamp=%d}", 
                           pluginId, requestId, timestamp);
    }
}
