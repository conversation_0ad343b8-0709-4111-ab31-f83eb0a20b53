package com.fasnote.alm.license.builder.util;

import com.fasnote.alm.license.builder.exception.BuildException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;


/**
 * Tycho依赖解析器
 * 利用Tycho构建过程中生成的依赖信息，而不是重新解析MANIFEST.MF
 */
public class TychoDependencyResolver {
    
    private static final Logger logger = LoggerFactory.getLogger(TychoDependencyResolver.class);
    
    /**
     * 从Tycho构建结果中获取完整的依赖classpath
     * 
     * @param projectDir 项目目录
     * @return 完整的依赖JAR文件路径列表
     * @throws BuildException 解析失败
     */
    public static List<String> getResolvedDependencies(Path projectDir) throws BuildException {
        Path classpathFile = projectDir.resolve("target/classpath.txt");
        
        if (!Files.exists(classpathFile)) {
            logger.warn("Tycho classpath文件不存在: {}", classpathFile);
            return new ArrayList<>();
        }
        
        try {
            String classpath = Files.readString(classpathFile).trim();
            if (classpath.isEmpty()) {
                logger.warn("Tycho classpath为空");
                return new ArrayList<>();
            }
            
            // 按路径分隔符分割classpath
            String[] paths = classpath.split(System.getProperty("path.separator"));
            List<String> dependencies = new ArrayList<>();
            
            for (String path : paths) {
                String trimmedPath = path.trim();
                if (!trimmedPath.isEmpty() && Files.exists(Paths.get(trimmedPath))) {
                    dependencies.add(trimmedPath);
                    logger.debug("找到依赖: {}", trimmedPath);
                }
            }
            
            logger.info("从Tycho解析到 {} 个依赖", dependencies.size());
            return dependencies;
            
        } catch (IOException e) {
            throw new BuildException("读取Tycho classpath文件失败: " + classpathFile, e);
        }
    }
    
    /**
     * 从Tycho构建结果中获取依赖信息列表
     * 
     * @param projectDir 项目目录
     * @return 依赖信息列表
     * @throws BuildException 解析失败
     */
    public static List<TychoDependency> getDependencyList(Path projectDir) throws BuildException {
        Path dependenciesFile = projectDir.resolve("target/dependencies.txt");
        
        if (!Files.exists(dependenciesFile)) {
            logger.warn("Tycho dependencies文件不存在: {}", dependenciesFile);
            return new ArrayList<>();
        }
        
        try {
            List<String> lines = Files.readAllLines(dependenciesFile);
            List<TychoDependency> dependencies = new ArrayList<>();
            
            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("The following files") || line.startsWith("---")) {
                    continue;
                }
                
                TychoDependency dependency = parseDependencyLine(line);
                if (dependency != null) {
                    dependencies.add(dependency);
                    logger.debug("解析依赖: {}", dependency);
                }
            }
            
            logger.info("从Tycho解析到 {} 个依赖信息", dependencies.size());
            return dependencies;
            
        } catch (IOException e) {
            throw new BuildException("读取Tycho dependencies文件失败: " + dependenciesFile, e);
        }
    }
    
    /**
     * 解析依赖行
     * 格式: p2.eclipse.plugin:org.eclipse.osgi:eclipse-plugin:3.13.0.v20180409-1500:system
     */
    private static TychoDependency parseDependencyLine(String line) {
        if (!line.contains(":")) {
            return null;
        }
        
        // 移除注释部分
        int commentIndex = line.indexOf(" -- ");
        if (commentIndex > 0) {
            line = line.substring(0, commentIndex).trim();
        }
        
        String[] parts = line.split(":");
        if (parts.length >= 4) {
            String groupId = parts[0].trim();
            String artifactId = parts[1].trim();
            String type = parts[2].trim();
            String version = parts[3].trim();
            String scope = parts.length > 4 ? parts[4].trim() : "compile";
            
            return new TychoDependency(groupId, artifactId, type, version, scope);
        }
        
        return null;
    }
    
    /**
     * Tycho依赖信息
     */
    public static class TychoDependency {
        private final String groupId;
        private final String artifactId;
        private final String type;
        private final String version;
        private final String scope;
        
        public TychoDependency(String groupId, String artifactId, String type, String version, String scope) {
            this.groupId = groupId;
            this.artifactId = artifactId;
            this.type = type;
            this.version = version;
            this.scope = scope;
        }
        
        public String getGroupId() { return groupId; }
        public String getArtifactId() { return artifactId; }
        public String getType() { return type; }
        public String getVersion() { return version; }
        public String getScope() { return scope; }
        
        /**
         * 获取Bundle ID（用于ProGuard配置）
         */
        public String getBundleId() {
            return artifactId;
        }
        
        /**
         * 是否是Eclipse插件
         */
        public boolean isEclipsePlugin() {
            return "p2.eclipse.plugin".equals(groupId) && "eclipse-plugin".equals(type);
        }
        
        @Override
        public String toString() {
            return String.format("%s:%s:%s:%s:%s", groupId, artifactId, type, version, scope);
        }
    }
}
