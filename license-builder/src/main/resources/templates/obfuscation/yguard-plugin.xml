            <!-- YGuard 混淆插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>yguard-obfuscate</id>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <taskdef name="yguard" classname="com.yworks.yguard.YGuardTask" classpath="${yguard.jar}"/>
                                <yguard>
                                    <inoutpair in="${project.build.directory}/${project.build.finalName}.jar"
                                              out="${project.build.directory}/${project.build.finalName}-obfuscated.jar"/>
                                    <shrink logfile="${project.build.directory}/yguard-shrink.log">
                                        <entrypointjar name="${project.build.directory}/${project.build.finalName}.jar"/>
                                    </shrink>
                                    <rename logfile="${project.build.directory}/yguard-rename.log"
                                           replaceClassNameStrings="true"
                                           conserveManifest="true">
                                        {{>obfuscation/yguard-rules.xml}}
                                    </rename>
                                </yguard>

                                <!-- 混淆后处理：清理重复的library.jar -->
                                <echo message="开始清理混淆后JAR中的重复library.jar文件"/>

                                <!-- 创建临时目录 -->
                                <mkdir dir="${project.build.directory}/temp-cleanup"/>

                                <!-- 备份原始MANIFEST.MF -->
                                <unzip src="${project.build.directory}/${project.build.finalName}-obfuscated.jar"
                                       dest="${project.build.directory}/temp-cleanup">
                                    <patternset>
                                        <include name="META-INF/MANIFEST.MF"/>
                                    </patternset>
                                </unzip>
                                <copy file="${project.build.directory}/temp-cleanup/META-INF/MANIFEST.MF"
                                      tofile="${project.build.directory}/original-manifest.mf"/>

                                <!-- 解压混淆后的JAR -->
                                <unzip src="${project.build.directory}/${project.build.finalName}-obfuscated.jar"
                                       dest="${project.build.directory}/temp-cleanup"/>

                                <!-- 删除重复的library.jar文件 -->
                                <delete file="${project.build.directory}/temp-cleanup/library.jar"
                                        failonerror="false"/>

                                <!-- 恢复原始MANIFEST.MF -->
                                <copy file="${project.build.directory}/original-manifest.mf"
                                      tofile="${project.build.directory}/temp-cleanup/META-INF/MANIFEST.MF"
                                      overwrite="true"/>

                                <!-- 重新打包JAR，保持原始MANIFEST.MF -->
                                <jar destfile="${project.build.directory}/${project.build.finalName}-obfuscated.jar"
                                     basedir="${project.build.directory}/temp-cleanup"
                                     manifest="${project.build.directory}/original-manifest.mf"/>

                                <!-- 清理临时文件 -->
                                <delete dir="${project.build.directory}/temp-cleanup"/>
                                <delete file="${project.build.directory}/original-manifest.mf"/>

                                <echo message="已清理混淆后JAR中的重复library.jar文件，保留原始MANIFEST.MF"/>
                            </target>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.yworks</groupId>
                        <artifactId>yguard</artifactId>
                        <version>4.0.0</version>
                    </dependency>
                </dependencies>
            </plugin>
