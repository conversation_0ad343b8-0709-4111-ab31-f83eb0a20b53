package com.fasnote.alm.license.builder.validation.validators;

import com.fasnote.alm.license.builder.model.BundleInfo;
import com.fasnote.alm.license.builder.validation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.Set;
import java.util.jar.JarFile;
import java.util.jar.Manifest;

/**
 * 类完整性验证器
 * 验证混淆后的类文件完整性，确保所有必要的类都存在且可访问
 */
public class ClassIntegrityValidator implements PhaseAwareValidator {
    
    private static final Logger logger = LoggerFactory.getLogger(ClassIntegrityValidator.class);
    
    @Override
    public String getName() {
        return "CLASS_INTEGRITY";
    }
    
    @Override
    public Set<BuildPhase> getSupportedPhases() {
        return Set.of(BuildPhase.POST_OBFUSCATION);
    }
    
    @Override
    public Set<PluginType> getSupportedPluginTypes() {
        return Set.of(PluginType.ALL);
    }
    
    @Override
    public int getPriority() {
        return 100; // 高优先级，在其他验证之前执行
    }
    
    @Override
    public boolean isApplicable(ValidationContext context) {
        // 对所有混淆后的构建都适用
        return true;
    }
    
    @Override
    public String getDescription() {
        return "类完整性验证器 - 验证混淆后的类文件完整性";
    }
    
    @Override
    public ValidationResult validate(BundleInfo bundleInfo, ValidationContext context) {
        logger.info("开始执行类完整性验证...");
        
        try {
            ValidationResult.Builder resultBuilder = ValidationResult.builder()
                    .validatorName(getName());
            
            // 验证Bundle信息
            if (bundleInfo == null) {
                return resultBuilder
                        .success(false)
                        .message("Bundle信息为空")
                        .build();
            }
            
            // 验证JAR文件存在性
            String jarPath = getJarPath(context);
            if (jarPath == null || jarPath.trim().isEmpty()) {
                return resultBuilder
                        .success(false)
                        .message("未指定JAR文件路径")
                        .build();
            }
            
            File jarFile = new File(jarPath);
            if (!jarFile.exists()) {
                return resultBuilder
                        .success(false)
                        .message("JAR文件不存在: " + jarPath)
                        .build();
            }
            
            // 验证JAR文件完整性
            ValidationResult jarValidation = validateJarFile(jarFile, resultBuilder);
            if (!jarValidation.isSuccess()) {
                return jarValidation;
            }
            
            // 验证类文件完整性
            ValidationResult classValidation = validateClassFiles(jarFile, bundleInfo, resultBuilder);
            if (!classValidation.isSuccess()) {
                return classValidation;
            }
            
            logger.info("类完整性验证通过");
            return resultBuilder
                    .success(true)
                    .message("类完整性验证通过")
                    .build();
            
        } catch (Exception e) {
            logger.error("类完整性验证失败", e);
            return ValidationResult.failure(getName(), "类完整性验证过程中发生异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取JAR文件路径
     */
    private String getJarPath(ValidationContext context) {
        // 优先从属性中获取
        String jarPath = context.getProperty("inputJar", String.class);
        if (jarPath != null) {
            return jarPath;
        }
        
        // 从配置中获取
        if (context.getConfiguration() != null && 
            context.getConfiguration().getInput() != null) {
            return context.getConfiguration().getInput().getSourceJar();
        }
        
        return null;
    }
    
    /**
     * 验证JAR文件
     */
    private ValidationResult validateJarFile(File jarFile, ValidationResult.Builder resultBuilder) {
        try (JarFile jar = new JarFile(jarFile)) {
            // 检查JAR文件是否可读
            if (jar.entries().hasMoreElements()) {
                resultBuilder.message("JAR文件可正常读取: " + jarFile.getName());
            } else {
                return resultBuilder
                        .success(false)
                        .message("JAR文件为空或损坏: " + jarFile.getAbsolutePath())
                        .build();
            }
            
            // 检查Manifest文件
            Manifest manifest = jar.getManifest();
            if (manifest == null) {
                resultBuilder.warning("JAR文件缺少Manifest文件");
            } else {
                resultBuilder.message("Manifest文件存在");
            }
            
            return resultBuilder.success(true).build();
            
        } catch (IOException e) {
            return resultBuilder
                    .success(false)
                    .message("无法读取JAR文件: " + e.getMessage())
                    .exception(e)
                    .build();
        }
    }
    
    /**
     * 验证类文件完整性
     */
    private ValidationResult validateClassFiles(File jarFile, BundleInfo bundleInfo, 
                                              ValidationResult.Builder resultBuilder) {
        try (JarFile jar = new JarFile(jarFile)) {
            
            // 统计类文件数量
            long classCount = jar.stream()
                    .filter(entry -> entry.getName().endsWith(".class"))
                    .count();
            
            if (classCount == 0) {
                return resultBuilder
                        .success(false)
                        .message("JAR文件中没有找到任何类文件")
                        .build();
            }
            
            resultBuilder.message("找到 " + classCount + " 个类文件");
            
            // 检查关键类文件
            boolean hasActivator = jar.stream()
                    .anyMatch(entry -> entry.getName().contains("Activator.class"));
            
            if (!hasActivator) {
                resultBuilder.warning("未找到Activator类，可能影响插件启动");
            } else {
                resultBuilder.message("找到Activator类");
            }
            
            return resultBuilder.success(true).build();
            
        } catch (IOException e) {
            return resultBuilder
                    .success(false)
                    .message("验证类文件时发生错误: " + e.getMessage())
                    .exception(e)
                    .build();
        }
    }
}
