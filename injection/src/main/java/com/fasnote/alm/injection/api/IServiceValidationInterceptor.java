package com.fasnote.alm.injection.api;

/**
 * 服务验证拦截器接口
 * 
 * 在服务获取时进行拦截，根据许可证验证结果决定返回正常实现还是降级实现
 * 这是实现许可证感知服务获取的核心接口
 */
public interface IServiceValidationInterceptor {
    
    /**
     * 拦截服务获取请求
     * 
     * @param serviceClass 服务接口类
     * @param serviceName 服务名称（可能为null）
     * @param normalService 正常的服务实例
     * @param context 拦截上下文
     * @return 最终返回的服务实例（可能是正常实现或降级实现）
     */
    <T> T interceptServiceRequest(Class<T> serviceClass, String serviceName, T normalService, InterceptContext context);
    
    /**
     * 检查是否应该拦截此服务请求
     * 
     * @param serviceClass 服务接口类
     * @param serviceName 服务名称
     * @return 是否需要拦截
     */
    boolean shouldIntercept(Class<?> serviceClass, String serviceName);
    
    /**
     * 获取拦截器优先级
     * 数字越小优先级越高
     * 
     * @return 优先级
     */
    int getPriority();
    

}
