package com.fasnote.alm.plugin.manage.repository;

import java.util.List;
import java.util.Optional;

import com.fasnote.alm.plugin.manage.model.PluginLicense;

/**
 * 许可证数据访问接口
 */
public interface LicenseRepository {

	/**
	 * 删除许可证
	 *
	 * @param pluginId 插件ID
	 */
	void deleteByPluginId(String pluginId);

	/**
	 * 检查许可证是否存在
	 *
	 * @param pluginId 插件ID
	 * @return 是否存在
	 */
	boolean existsByPluginId(String pluginId);

	/**
	 * 获取所有许可证
	 *
	 * @return 许可证列表
	 */
	List<PluginLicense> findAll();

	/**
	 * 获取所有插件ID
	 *
	 * @return 插件ID列表
	 */
	List<String> findAllPluginIds();

	/**
	 * 根据功能名称查找许可证
	 *
	 * @param featureName 功能名称
	 * @return 许可证对象的Optional包装
	 */
	Optional<PluginLicense> findByFeature(String featureName);

	/**
	 * 根据插件ID查找许可证
	 *
	 * @param pluginId 插件ID
	 * @return 许可证对象的Optional包装
	 */
	Optional<PluginLicense> findByPluginId(String pluginId);

	/**
	 * 保存许可证
	 *
	 * @param license 许可证对象
	 */
	void save(PluginLicense license);
}