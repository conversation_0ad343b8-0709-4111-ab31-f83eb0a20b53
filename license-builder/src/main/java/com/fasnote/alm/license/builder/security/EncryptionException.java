package com.fasnote.alm.license.builder.security;

/**
 * 加密异常类
 * 用于处理加密和解密过程中的异常情况
 */
public class EncryptionException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    public EncryptionException(String message) {
        super(message);
    }
    
    public EncryptionException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public EncryptionException(Throwable cause) {
        super(cause);
    }
}
