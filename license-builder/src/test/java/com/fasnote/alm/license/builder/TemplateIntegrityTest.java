package com.fasnote.alm.license.builder;

import com.fasnote.alm.license.builder.template.TemplateEngine;
import com.fasnote.alm.license.builder.template.TemplateContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 模板完整性测试 - 验证所有模板文件存在且可以正确渲染
 */
class TemplateIntegrityTest {

    private TemplateEngine templateEngine;
    private TemplateContext context;

    @BeforeEach
    void setUp() {
        // 使用 classpath 中的 templates 文件夹
        templateEngine = TemplateEngine.createClasspathEngine("templates/");
        context = createTestContext();
    }

    private TemplateContext createTestContext() {
        TemplateContext ctx = new TemplateContext();
        
        // Bundle 信息
        ctx.put("bundle_symbolic_name", "com.example.test.plugin");
        ctx.put("bundle_version", "1.0.0.qualifier");
        ctx.put("bundle_name", "Test Plugin");
        ctx.put("bundle_vendor", "Test Vendor");
        ctx.put("bundle_activator", "com.example.test.plugin.Activator");
        ctx.put("bundle_exported_packages", "com.example.test.plugin.api");
        
        // Maven 信息
        ctx.put("maven_version", "1.0.0-SNAPSHOT");
        ctx.put("final_name", "test-plugin_1.0.0.${buildQualifier}");
        
        // 构建配置
        ctx.put("tycho_version", "2.7.5");
        ctx.put("java_version", "21");
        
        // License Builder 配置
        ctx.put("license_builder_jar", "/path/to/license-builder.jar");
        ctx.put("license_builder_config", "${project.build.directory}/license-build-config.yaml");
        
        // 混淆配置
        ctx.put("enable_obfuscation", true);
        ctx.put("obfuscation_tool", "YGUARD");
        ctx.put("obfuscation_level", "STANDARD");
        
        // 许可证配置
        ctx.put("license_validity", "DAYS_90");
        ctx.put("machine_binding_enabled", false);
        
        // Polarion 路径
        ctx.put("polarion_path", "/opt/polarion/polarion");
        
        // 导出包列表（用于混淆规则）
        ctx.put("exported_packages", Arrays.asList(
            createPackageInfo("com.example.test.plugin.api"),
            createPackageInfo("com.example.test.plugin.util")
        ));
        
        return ctx;
    }
    
    private TemplateContext createPackageInfo(String packageName) {
        TemplateContext pkg = new TemplateContext();
        pkg.put("package_name", packageName);
        return pkg;
    }

    @Test
    void testTemplateDirectoryExists() {
        // 检查 classpath 中的模板资源是否存在
        assertNotNull(getClass().getClassLoader().getResource("templates/"),
                     "Templates directory should exist in classpath");
        assertNotNull(getClass().getClassLoader().getResource("templates/config/build-properties.txt"),
                     "Template files should exist in classpath");
    }

    @Test
    void testPomTemplateRendering() throws IOException {
        String result = templateEngine.render("pom/complete-pom.xml", context);
        
        assertNotNull(result);
        assertFalse(result.trim().isEmpty());
        
        // 验证基本 XML 结构
        assertTrue(result.contains("<?xml version=\"1.0\" encoding=\"UTF-8\"?>"));
        assertTrue(result.contains("<project"));
        assertTrue(result.contains("</project>"));
        
        // 验证变量替换
        assertTrue(result.contains("com.example.test.plugin"));
        assertTrue(result.contains("1.0.0-SNAPSHOT"));
        assertTrue(result.contains("2.7.5")); // tycho version
        
        // 验证没有未替换的变量
        assertFalse(result.contains("${bundle_symbolic_name}"));
        assertFalse(result.contains("${maven_version}"));
    }

    @Test
    void testBuildPropertiesTemplateRendering() throws IOException {
        String result = templateEngine.render("config/build-properties.txt", context);
        
        assertNotNull(result);
        assertFalse(result.trim().isEmpty());
        
        // 验证基本内容
        assertTrue(result.contains("source.. = src/"));
        assertTrue(result.contains("output.. = bin/"));
        assertTrue(result.contains("bin.includes = META-INF/"));
    }

    @Test
    void testTargetPlatformTemplateRendering() throws IOException {
        String result = templateEngine.render("tycho/target-platform.xml", context);
        
        assertNotNull(result);
        assertFalse(result.trim().isEmpty());
        
        // 验证 XML 结构
        assertTrue(result.contains("<?xml version=\"1.0\" encoding=\"UTF-8\""));
        assertTrue(result.contains("<target"));
        assertTrue(result.contains("</target>"));
        
        // 验证 Polarion 路径替换
        assertTrue(result.contains("/opt/polarion/polarion"));
        assertFalse(result.contains("${polarion_path}"));
    }

    @Test
    void testTychoPluginsTemplateRendering() throws IOException {
        String result = templateEngine.render("tycho/tycho-plugins.xml", context);
        
        assertNotNull(result);
        assertFalse(result.trim().isEmpty());
        
        // 验证 Tycho 插件配置
        assertTrue(result.contains("tycho-maven-plugin"));
        assertTrue(result.contains("tycho-packaging-plugin"));
        assertTrue(result.contains("target-platform-configuration"));
        
        // 验证版本替换
        assertTrue(result.contains("2.7.5"));
        assertFalse(result.contains("${tycho_version}"));
    }

    @Test
    void testObfuscationTemplatesRendering() throws IOException {
        // 测试 YGuard 模板
        context.put("obfuscation_tool", "YGUARD");
        String yguardResult = templateEngine.render("obfuscation/yguard-plugin.xml", context);
        
        assertNotNull(yguardResult);
        assertTrue(yguardResult.contains("yguard"));
        assertTrue(yguardResult.contains("maven-antrun-plugin"));
        
        // 测试 ProGuard 模板
        context.put("obfuscation_tool", "PROGUARD");
        String proguardResult = templateEngine.render("obfuscation/proguard-plugin.xml", context);
        
        assertNotNull(proguardResult);
        assertTrue(proguardResult.contains("proguard"));
        assertTrue(proguardResult.contains("proguard-maven-plugin"));
    }

    @Test
    void testLicenseTemplatesRendering() throws IOException {
        String licenseResult = templateEngine.render("license/license-processing.xml", context);

        assertNotNull(licenseResult);

        // 调试输出
        System.out.println("=== license-processing.xml 渲染结果 ===");
        System.out.println(licenseResult);
        System.out.println("=== 内容结束 ===");

        // 检查实际包含的内容
        boolean hasLicensePlugin = licenseResult.contains("license-builder") ||
                                 licenseResult.contains("com.fasnote.alm");
        assertTrue(hasLicensePlugin, "应该包含license-builder插件");

        assertTrue(licenseResult.contains("obfuscated.jar")); // 因为 enable_obfuscation = true

        String copyResult = templateEngine.render("license/file-copy-plugin.xml", context);

        assertNotNull(copyResult);
        assertTrue(copyResult.contains("maven-resources-plugin"));
        assertTrue(copyResult.contains("copy-resources"));
    }

    @Test
    void testBundleInfoTemplateRendering() throws IOException {
        String result = templateEngine.render("config/bundle-info.yaml", context);
        
        assertNotNull(result);
        assertFalse(result.trim().isEmpty());
        
        // 验证 YAML 结构
        assertTrue(result.contains("bundle:"));
        assertTrue(result.contains("symbolicName: com.example.test.plugin"));
        assertTrue(result.contains("version: 1.0.0.qualifier"));
        
        // 验证没有未替换的变量
        assertFalse(result.contains("${bundle_symbolic_name}"));
        assertFalse(result.contains("${bundle_version}"));
    }

    @Test
    void testConditionalRenderingWithDifferentObfuscationTools() throws IOException {
        // 测试 PROGUARD 条件渲染
        context.put("obfuscation_tool", "PROGUARD");
        context.put("enable_obfuscation", true);
        
        String pomWithProguard = templateEngine.render("pom/complete-pom.xml", context);
        assertTrue(pomWithProguard.contains("proguard"));
        assertFalse(pomWithProguard.contains("yguard"));
        
        // 测试 YGUARD 条件渲染
        context.put("obfuscation_tool", "YGUARD");
        
        String pomWithYguard = templateEngine.render("pom/complete-pom.xml", context);
        assertTrue(pomWithYguard.contains("yguard"));
        assertFalse(pomWithYguard.contains("proguard"));
        
        // 测试禁用混淆
        context.put("enable_obfuscation", false);
        
        String pomWithoutObfuscation = templateEngine.render("pom/complete-pom.xml", context);
        assertFalse(pomWithoutObfuscation.contains("yguard"));
        assertFalse(pomWithoutObfuscation.contains("proguard"));
    }

    @Test
    void testObfuscationLevelRendering() throws IOException {
        // 测试不同混淆级别的规则渲染
        String[] levels = {"LIGHT", "STANDARD", "AGGRESSIVE"};
        
        for (String level : levels) {
            context.put("obfuscation_level", level);
            
            String proguardRules = templateEngine.render("obfuscation/proguard-rules.xml", context);
            assertNotNull(proguardRules);
            assertTrue(proguardRules.contains(level), "Should contain obfuscation level: " + level);
            
            // 验证没有未替换的变量
            assertFalse(proguardRules.contains("${obfuscation_level}"));
        }
    }

    @Test
    void testTemplateInclusionChain() throws IOException {
        // 测试模板包含链：complete-pom.xml -> pom-build.xml -> tycho-plugins.xml
        String result = templateEngine.render("pom/complete-pom.xml", context);

        // 调试输出
        System.out.println("=== complete-pom.xml 渲染结果 ===");
        System.out.println(result);
        System.out.println("=== 内容结束 ===");

        // 验证包含的内容都被正确渲染
        assertTrue(result.contains("tycho-maven-plugin")); // 来自 tycho-plugins.xml

        // 由于条件渲染，这些插件可能不会出现，检查基本结构
        assertTrue(result.contains("<project")); // 基本XML结构
        assertTrue(result.contains("</project>"));

        // 验证没有包含标记残留
        assertFalse(result.contains("{{>"));
        assertFalse(result.contains("}}"));
    }

    @Test
    void testAllRequiredTemplatesExist() {
        String[] requiredTemplates = {
            "pom/complete-pom.xml",
            "pom/pom-properties.xml", 
            "pom/pom-build.xml",
            "config/build-properties.txt",
            "config/bundle-info.yaml",
            "tycho/target-platform.xml",
            "tycho/tycho-plugins.xml",
            "obfuscation/proguard-plugin.xml",
            "obfuscation/proguard-rules.xml",
            "obfuscation/yguard-plugin.xml",
            "obfuscation/yguard-rules.xml",
            "license/license-processing.xml",
            "license/file-copy-plugin.xml"
        };
        
        for (String template : requiredTemplates) {
            // 检查 classpath 中的模板资源是否存在
            String resourcePath = "templates/" + template;
            assertNotNull(getClass().getClassLoader().getResource(resourcePath),
                         "Required template should exist in classpath: " + template);

            // 验证模板可以被渲染（不抛出异常）
            assertDoesNotThrow(() -> {
                templateEngine.render(template, context);
            }, "Template should render without errors: " + template);
        }
    }
}
