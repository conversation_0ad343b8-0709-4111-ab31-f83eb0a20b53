# 外部依赖库

这个目录包含plugin-manage项目需要的外部jar依赖。

## license-crypto.jar

共享的许可证加解密模块，包含：
- RSAKeyManager: RSA密钥管理
- RSALicenseEncryption: 许可证加解密

### 使用方法

1. 编译license-crypto模块：
   ```bash
   cd ../license-crypto
   mvn clean package
   ```

2. 复制生成的jar到lib目录：
   ```bash
   cp target/license-crypto-1.0.0.jar ../plugin-manage/lib/
   ```

3. 在plugin-manage项目中引用这个jar文件

### 更新步骤

当license-crypto模块有更新时：
1. 重新编译license-crypto模块
2. 替换lib目录中的jar文件
3. 重新编译plugin-manage项目
