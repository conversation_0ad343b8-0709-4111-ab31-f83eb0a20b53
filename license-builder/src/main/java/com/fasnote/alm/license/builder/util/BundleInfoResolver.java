package com.fasnote.alm.license.builder.util;

import com.fasnote.alm.license.builder.exception.BuildException;
import com.fasnote.alm.license.builder.model.BundleInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.jar.JarFile;
import java.util.jar.Manifest;

/**
 * Bundle 信息解析器
 * 从多种数据源智能获取 Bundle 信息
 */
public class BundleInfoResolver {

    private static final Logger logger = LoggerFactory.getLogger(BundleInfoResolver.class);
    
    /**
     * 智能获取 Bundle 信息（统一入口点）
     * 按优先级从多个数据源尝试获取
     */
    public static BundleInfo getBundleInfo() throws BuildException {
        // 1. 优先从 Maven 属性读取
        BundleInfo bundleInfo = tryLoadFromMavenProperties();
        if (bundleInfo != null) {
            logger.debug("从 Maven 属性获取Bundle信息");
            return bundleInfo;
        }

        // 2. 尝试从 bundle-metadata.properties 读取
        bundleInfo = tryLoadFromPropertiesFile();
        if (bundleInfo != null) {
            logger.debug("从属性文件获取Bundle信息");
            return bundleInfo;
        }

        // 3. 尝试从当前目录的 MANIFEST.MF 读取
        bundleInfo = tryLoadFromManifest();
        if (bundleInfo != null) {
            logger.debug("从MANIFEST.MF解析Bundle信息");
            return bundleInfo;
        }

        // 4. 尝试从 target 目录的 JAR 文件读取
        bundleInfo = tryLoadFromTargetJar();
        if (bundleInfo != null) {
            logger.debug("从target目录JAR文件解析Bundle信息");
            return bundleInfo;
        }

        throw new BuildException("无法获取Bundle信息：未找到任何有效的数据源");
    }

    /**
     * 从指定的 JAR 文件获取 Bundle 信息
     */
    public static BundleInfo getBundleInfo(Path jarPath) throws BuildException {
        if (!Files.exists(jarPath)) {
            throw new BuildException("JAR 文件不存在: " + jarPath);
        }

        try (JarFile jarFile = new JarFile(jarPath.toFile())) {
            Manifest manifest = jarFile.getManifest();
            if (manifest == null) {
                throw new BuildException("JAR 文件中没有找到 MANIFEST.MF: " + jarPath);
            }

            BundleInfo bundleInfo = ManifestParser.parseManifest(manifest);
            if (bundleInfo == null || bundleInfo.getSymbolicName() == null) {
                throw new BuildException("无法解析 Bundle 信息或缺少 Bundle-SymbolicName: " + jarPath);
            }

            logger.debug("从 JAR 文件解析Bundle信息: {}", bundleInfo.getSymbolicName());
            return bundleInfo;

        } catch (IOException e) {
            throw new BuildException("读取 JAR 文件失败: " + jarPath, e);
        }
    }

    /**
     * 从指定目录获取 Bundle 信息
     */
    public static BundleInfo getBundleInfo(Path pluginDir, boolean isDirectory) throws BuildException {
        if (isDirectory) {
            return getBundleInfoFromDirectory(pluginDir);
        } else {
            return getBundleInfo(pluginDir);
        }
    }
    
    /**
     * 获取 Tycho 解析的依赖信息
     */
    public static List<String> getDependencies() throws BuildException {
        try {
            List<String> dependencies = TychoDependencyResolver.getResolvedDependencies(Paths.get("."));
            logger.debug("从Tycho解析获取 {} 个依赖", dependencies.size());
            return dependencies;
        } catch (Exception e) {
            throw new BuildException("获取Tycho依赖信息失败", e);
        }
    }

    /**
     * 从指定目录获取 Bundle 信息
     */
    public static BundleInfo getBundleInfoFromDirectory(Path pluginDir) throws BuildException {
        Path manifestPath = null;

        // 首先尝试标准的 META-INF/MANIFEST.MF 路径
        Path standardManifestPath = pluginDir.resolve("META-INF/MANIFEST.MF");
        if (Files.exists(standardManifestPath)) {
            manifestPath = standardManifestPath;
            logger.debug("找到标准MANIFEST.MF文件: {}", manifestPath);
        } else {
            // 如果标准路径不存在，尝试直接在目录下查找 MANIFEST.MF（适用于target目录）
            Path directManifestPath = pluginDir.resolve("MANIFEST.MF");
            if (Files.exists(directManifestPath)) {
                manifestPath = directManifestPath;
                logger.debug("找到直接MANIFEST.MF文件: {}", manifestPath);
            }
        }

        if (manifestPath == null) {
            throw new BuildException("未找到 MANIFEST.MF 文件，尝试了以下路径: " +
                standardManifestPath + ", " + pluginDir.resolve("MANIFEST.MF"));
        }

        try (InputStream is = Files.newInputStream(manifestPath)) {
            Manifest manifest = new Manifest(is);
            BundleInfo bundleInfo = ManifestParser.parseManifest(manifest);

            if (bundleInfo == null || bundleInfo.getSymbolicName() == null) {
                throw new BuildException("无法解析 Bundle 信息或缺少 Bundle-SymbolicName");
            }

            // 解析XML配置文件（只有在标准目录结构下才解析）
            if (Files.exists(pluginDir.resolve("META-INF"))) {
                XmlConfigParser.XmlConfigInfo xmlConfigInfo = XmlConfigParser.scanXmlConfigs(pluginDir);
                bundleInfo.setXmlConfigInfo(xmlConfigInfo);
            } else {
                logger.debug("跳过XML配置解析，因为不是标准的插件目录结构");
            }

            logger.debug("从目录解析Bundle信息: {}", bundleInfo.getSymbolicName());
            return bundleInfo;

        } catch (IOException e) {
            throw new BuildException("解析 MANIFEST.MF 失败: " + manifestPath, e);
        }
    }

    /**
     * 从 Maven 属性读取 Bundle 信息
     */
    private static BundleInfo tryLoadFromMavenProperties() {
        try {
            java.util.Properties systemProps = System.getProperties();
            if (!systemProps.containsKey("bundle.symbolicName")) {
                return null;
            }

            BundleInfo bundleInfo = new BundleInfo();
            bundleInfo.setSymbolicName(systemProps.getProperty("bundle.symbolicName"));
            bundleInfo.setVersion(systemProps.getProperty("bundle.version"));
            bundleInfo.setActivator(systemProps.getProperty("bundle.activator"));

            // 解析导出包列表
            String exportedPackages = systemProps.getProperty("bundle.exportedPackages");
            if (exportedPackages != null && !exportedPackages.trim().isEmpty()) {
                String[] packages = exportedPackages.split(",");
                for (String pkg : packages) {
                    String trimmed = pkg.trim();
                    if (!trimmed.isEmpty()) {
                        bundleInfo.getExportedPackages().add(trimmed);
                    }
                }
            }

            return bundleInfo;
        } catch (Exception e) {
            logger.debug("从 Maven 属性读取失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从属性文件读取 Bundle 信息
     */
    private static BundleInfo tryLoadFromPropertiesFile() {
        try {
            Path propertiesFile = Paths.get("target/bundle-metadata.properties");
            if (!Files.exists(propertiesFile)) {
                return null;
            }

            java.util.Properties properties = new java.util.Properties();
            try (InputStream is = Files.newInputStream(propertiesFile)) {
                properties.load(is);
            }

            BundleInfo bundleInfo = new BundleInfo();
            bundleInfo.setSymbolicName(properties.getProperty("bundle.symbolicName"));
            bundleInfo.setVersion(properties.getProperty("bundle.version"));
            bundleInfo.setActivator(properties.getProperty("bundle.activator"));

            // 解析导出包列表
            String exportedPackages = properties.getProperty("bundle.exportedPackages");
            if (exportedPackages != null && !exportedPackages.trim().isEmpty()) {
                String[] packages = exportedPackages.split(",");
                for (String pkg : packages) {
                    String trimmed = pkg.trim();
                    if (!trimmed.isEmpty()) {
                        bundleInfo.getExportedPackages().add(trimmed);
                    }
                }
            }

            return bundleInfo;
        } catch (Exception e) {
            logger.debug("从属性文件读取失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从 MANIFEST.MF 读取 Bundle 信息
     */
    private static BundleInfo tryLoadFromManifest() {
        try {
            Path manifestPath = Paths.get("META-INF/MANIFEST.MF");
            if (!Files.exists(manifestPath)) {
                return null;
            }

            try (InputStream is = Files.newInputStream(manifestPath)) {
                Manifest manifest = new Manifest(is);
                return ManifestParser.parseManifest(manifest);
            }
        } catch (Exception e) {
            logger.debug("从MANIFEST.MF读取失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从 target 目录的 JAR 文件读取 Bundle 信息
     */
    private static BundleInfo tryLoadFromTargetJar() {
        try {
            Path targetDir = Paths.get("target");
            if (!Files.exists(targetDir)) {
                return null;
            }

            Path[] jarFiles = Files.list(targetDir)
                .filter(p -> p.getFileName().toString().endsWith(".jar"))
                .filter(p -> !p.getFileName().toString().contains("sources"))
                .filter(p -> !p.getFileName().toString().contains("javadoc"))
                .toArray(Path[]::new);

            if (jarFiles.length == 0) {
                return null;
            }

            return getBundleInfo(jarFiles[0]);
        } catch (Exception e) {
            logger.debug("从target目录JAR文件读取失败: {}", e.getMessage());
            return null;
        }
    }


}
