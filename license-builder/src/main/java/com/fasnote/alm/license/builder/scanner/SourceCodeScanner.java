package com.fasnote.alm.license.builder.scanner;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 源代码扫描器，用于检测实现了 InvocationHandler 接口的类
 */
public class SourceCodeScanner {
    private static final Logger logger = LoggerFactory.getLogger(SourceCodeScanner.class);
    
    // 匹配实现InvocationHandler的类的正则表达式
    private static final Pattern INVOCATION_HANDLER_PATTERN = Pattern.compile(
        "(?:public|private|protected|static|final|abstract|\\s)*class\\s+(\\w+)\\s+[^{]*implements\\s+[^{]*InvocationHandler[^{]*\\{",
        Pattern.MULTILINE | Pattern.DOTALL
    );
    
    // 匹配包声明的正则表达式
    private static final Pattern PACKAGE_PATTERN = Pattern.compile(
        "package\\s+([\\w\\.]+)\\s*;", 
        Pattern.MULTILINE
    );
    
    /**
     * 扫描源代码目录中实现了InvocationHandler的类
     * 
     * @param sourceDir 源代码目录
     * @return 实现了InvocationHandler的类名列表
     */
    public List<String> scanSourceDirectory(Path sourceDir) {
        List<String> invocationHandlerClasses = new ArrayList<>();
        
        if (!Files.exists(sourceDir) || !Files.isDirectory(sourceDir)) {
            logger.warn("源代码目录不存在: {}", sourceDir);
            return invocationHandlerClasses;
        }
        
        try {
            Files.walk(sourceDir)
                .filter(path -> path.toString().endsWith(".java"))
                .forEach(javaFile -> {
                    try {
                        List<String> classNames = analyzeJavaFile(javaFile);
                        invocationHandlerClasses.addAll(classNames);
                    } catch (Exception e) {
                        logger.debug("分析Java文件失败: {} - {}", javaFile, e.getMessage());
                    }
                });
        } catch (IOException e) {
            logger.error("扫描源代码目录失败: {} - {}", sourceDir, e.getMessage());
        }
        
        return invocationHandlerClasses;
    }
    
    /**
     * 分析单个Java文件，检查是否包含实现了InvocationHandler的类
     *
     * @param javaFile Java文件路径
     * @return 如果包含InvocationHandler实现类则返回类名列表，否则返回空列表
     */
    private List<String> analyzeJavaFile(Path javaFile) throws IOException {
        List<String> classNames = new ArrayList<>();
        String content = Files.readString(javaFile);

        // 提取包名
        String packageName = extractPackageName(content);

        // 提取外部类名（从文件名）
        String fileName = javaFile.getFileName().toString();
        String outerClassName = fileName.substring(0, fileName.lastIndexOf('.'));

        // 查找实现InvocationHandler的类
        Matcher matcher = INVOCATION_HANDLER_PATTERN.matcher(content);
        while (matcher.find()) {
            String className = matcher.group(1);
            String fullClassName;

            // 如果类名与文件名相同，说明是外部类
            if (className.equals(outerClassName)) {
                fullClassName = packageName.isEmpty() ? className : packageName + "." + className;
            } else {
                // 否则是内部类，使用$分隔符
                fullClassName = packageName.isEmpty() ?
                    outerClassName + "$" + className :
                    packageName + "." + outerClassName + "$" + className;
            }

            classNames.add(fullClassName);
            logger.info("发现InvocationHandler实现类: {} (文件: {})", fullClassName, javaFile.getFileName());
        }

        return classNames;
    }
    
    /**
     * 从Java源代码中提取包名
     * 
     * @param content Java源代码内容
     * @return 包名，如果没有包声明则返回空字符串
     */
    private String extractPackageName(String content) {
        Matcher matcher = PACKAGE_PATTERN.matcher(content);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }
    
    /**
     * 生成ProGuard保护规则
     * 
     * @param invocationHandlerClasses InvocationHandler实现类列表
     * @return ProGuard规则列表
     */
    public List<String> generateProGuardRules(List<String> invocationHandlerClasses) {
        List<String> rules = new ArrayList<>();
        
        for (String className : invocationHandlerClasses) {
            String rule = String.format(
                "-keep class %s { public java.lang.Object invoke(java.lang.Object, java.lang.reflect.Method, java.lang.Object[]); }",
                className
            );
            rules.add(rule);
        }
        
        return rules;
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        if (args.length != 1) {
            System.out.println("Usage: SourceCodeScanner <source-directory>");
            System.exit(1);
        }

        SourceCodeScanner scanner = new SourceCodeScanner();
        Path sourceDir = Path.of(args[0]);
        List<String> classes = scanner.scanSourceDirectory(sourceDir);

        System.out.println("Found InvocationHandler classes:");
        for (String className : classes) {
            System.out.println("  " + className);
        }

        List<String> rules = scanner.generateProGuardRules(classes);
        System.out.println("\nGenerated ProGuard rules:");
        for (String rule : rules) {
            System.out.println("  " + rule);
        }
    }
}
