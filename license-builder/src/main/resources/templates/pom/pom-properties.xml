    <properties>
        <tycho.version>${tycho_version}</tycho.version>
        <maven.compiler.source>${java_version}</maven.compiler.source>
        <maven.compiler.target>${java_version}</maven.compiler.target>
        <!-- 跳过所有测试 -->
        <maven.test.skip>true</maven.test.skip>
        <skipTests>true</skipTests>
        <!-- License Builder 配置 -->
        <license.builder.jar>${license_builder_jar}</license.builder.jar>
        <license.builder.config>${license_builder_config}</license.builder.config>
        <!-- 混淆配置 -->
        <obfuscation.tool>${obfuscation_tool}</obfuscation.tool>
        <obfuscation.level>${obfuscation_level}</obfuscation.level>
        <!-- Bundle 信息（用于传递给混淆和许可证插件） -->
        <bundle.symbolicName>${bundle_symbolic_name}</bundle.symbolicName>
        <bundle.version>${bundle_version}</bundle.version>
        <bundle.activator>${bundle_activator}</bundle.activator>
        <bundle.exportedPackages>${bundle_exported_packages}</bundle.exportedPackages>
    </properties>
