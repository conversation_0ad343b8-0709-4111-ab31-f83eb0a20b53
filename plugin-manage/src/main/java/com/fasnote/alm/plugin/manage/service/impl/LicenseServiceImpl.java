package com.fasnote.alm.plugin.manage.service.impl;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.repository.LicenseRepository;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;
import com.fasnote.alm.plugin.manage.service.LicenseService;

/**
 * 许可证核心服务实现
 */
public class LicenseServiceImpl implements LicenseService {

	private static final Logger logger = LoggerFactory.getLogger(LicenseServiceImpl.class);

	private final LicenseRepository licenseRepository;
	private final SecurityValidator securityValidator;

	public LicenseServiceImpl(LicenseRepository licenseRepository, SecurityValidator securityValidator) {
		this.licenseRepository = licenseRepository;
		this.securityValidator = securityValidator;
	}

	@Override
	public Optional<PluginLicense> getLicense(String pluginId) {
		if (pluginId == null || pluginId.trim().isEmpty()) {
			logger.warn("插件ID为空，无法获取许可证");
			return Optional.empty();
		}

		try {
			return licenseRepository.findByPluginId(pluginId);
		} catch (Exception e) {
			logger.error("获取许可证失败: {}", pluginId, e);
			return Optional.empty();
		}
	}

	@Override
	public boolean hasValidLicense(String pluginId) {
		Optional<PluginLicense> licenseOpt = getLicense(pluginId);
		if (!licenseOpt.isPresent()) {
			logger.debug("插件许可证不存在: {}", pluginId);
			return false;
		}

		ValidationResult result = validateLicense(licenseOpt.get());
		return result.isValid();
	}

	@Override
	public boolean isFeatureEnabled(String featureName) {
		if (featureName == null || featureName.trim().isEmpty()) {
			return false;
		}

		try {
			// 查找包含该功能的许可证
			Optional<PluginLicense> licenseOpt = licenseRepository.findByFeature(featureName);
			if (!licenseOpt.isPresent()) {
				logger.debug("未找到包含功能的许可证: {}", featureName);
				return false;
			}

			// 验证许可证是否有效
			ValidationResult result = validateLicense(licenseOpt.get());
			return result.isValid();

		} catch (Exception e) {
			logger.error("检查功能可用性失败: {}", featureName, e);
			return false;
		}
	}

	@Override
	public void refreshLicenseStatus() {
		logger.info("刷新许可证状态");

		try {
			// 获取所有许可证并重新验证
			licenseRepository.findAll().forEach(license -> {
				ValidationResult result = validateLicense(license);
				logger.info("许可证状态: {} -> {}", license.getPluginId(), result.isValid() ? "有效" : "无效");
			});

		} catch (Exception e) {
			logger.error("刷新许可证状态失败", e);
		}
	}

	@Override
	public ValidationResult validateLicense(PluginLicense license) {
		if (license == null) {
			return ValidationResult.failure("许可证为空");
		}

		logger.info("验证许可证: {}", license.getPluginId());

		try {
			ValidationResult result = securityValidator.validateLicense(license);

			if (result.isValid()) {
				logger.info("许可证验证成功: {}", license.getPluginId());
			} else {
				logger.warn("许可证验证失败: {}, 原因: {}", license.getPluginId(), result.getMessage());
			}

			return result;

		} catch (Exception e) {
			String errorMessage = "许可证验证异常: " + e.getMessage();
			logger.error(errorMessage, e);
			return ValidationResult.failure(errorMessage);
		}
	}

	@Override
	public ValidationResult validatePluginLicense(String pluginId) {
		Optional<PluginLicense> licenseOpt = getLicense(pluginId);
		if (!licenseOpt.isPresent()) {
			return ValidationResult.failure("插件许可证不存在: " + pluginId);
		}

		return validateLicense(licenseOpt.get());
	}
}