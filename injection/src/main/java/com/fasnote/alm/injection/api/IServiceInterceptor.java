package com.fasnote.alm.injection.api;

/**
 * 服务拦截器接口
 * 用于在服务创建前后进行拦截处理
 */
public interface IServiceInterceptor {
    
    /**
     * 是否应该拦截指定的服务类
     * 
     * @param serviceClass 服务类
     * @return true表示需要拦截
     */
    boolean shouldIntercept(Class<?> serviceClass);
    
    /**
     * 服务创建前拦截
     * 
     * @param serviceClass 服务类
     * @param context 注入上下文
     * @return 如果返回非null，则使用返回值作为服务实例，跳过正常创建流程
     */
    default Object beforeCreate(Class<?> serviceClass, IInjectionContext context) {
        return null;
    }
    
    /**
     * 服务创建后拦截
     * 
     * @param serviceClass 服务类
     * @param instance 创建的实例
     * @param context 注入上下文
     * @return 处理后的实例（可以是原实例或代理实例）
     */
    default Object afterCreate(Class<?> serviceClass, Object instance, IInjectionContext context) {
        return instance;
    }
    
    /**
     * 拦截器优先级（数字越小优先级越高）
     * 
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
}