package com.fasnote.alm.license.builder.tools;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.security.*;
import java.util.Base64;
import javax.crypto.Cipher;

/**
 * RSA密钥生成工具
 * 用于生成许可证系统所需的RSA密钥对
 * 
 * 功能：
 * 1. 生成RSA-2048密钥对
 * 2. 保存密钥到PEM格式文件
 * 3. 输出配置信息
 * 4. 生成密钥指纹
 */
public class RSAKeyGeneratorTool {
    
    private static final String RSA_ALGORITHM = "RSA";
    private static final int RSA_KEY_SIZE = 2048;
    
    public static void main(String[] args) {
        try {
            System.out.println("=== ALM许可证系统 RSA密钥生成工具 ===");
            System.out.println();
            
            // 解析命令行参数
            String outputDir = parseOutputDir(args);
            
            // 生成密钥对
            System.out.println("正在生成RSA-" + RSA_KEY_SIZE + "密钥对...");
            KeyPair keyPair = generateKeyPair();
            
            // 保存密钥文件
            saveKeyPairToFiles(keyPair, outputDir);
            
            // 输出配置信息
            outputConfigurationInfo(keyPair);
            
            System.out.println();
            System.out.println("密钥生成完成！");
            System.out.println("请妥善保管私钥文件，并将公钥配置到Manage模块中。");
            
        } catch (Exception e) {
            System.err.println("密钥生成失败: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    /**
     * 解析输出目录参数
     */
    private static String parseOutputDir(String[] args) {
        if (args.length > 0) {
            return args[0];
        }
        
        // 默认输出目录
        String defaultDir = "/opt/license";
        System.out.println("使用默认输出目录: " + defaultDir);
        return defaultDir;
    }
    
    /**
     * 生成RSA密钥对
     */
    private static KeyPair generateKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(RSA_ALGORITHM);
        keyPairGenerator.initialize(RSA_KEY_SIZE, new SecureRandom());
        return keyPairGenerator.generateKeyPair();
    }
    
    /**
     * 保存密钥对到文件
     */
    private static void saveKeyPairToFiles(KeyPair keyPair, String outputDir) throws IOException {
        // 创建输出目录
        File dir = new File(outputDir);
        if (!dir.exists()) {
            if (!dir.mkdirs()) {
                throw new IOException("无法创建输出目录: " + outputDir);
            }
        }
        
        // 保存私钥
        String privateKeyPath = outputDir + File.separator + "license-private.key";
        savePrivateKeyToPemFile(keyPair.getPrivate(), privateKeyPath);
        System.out.println("私钥已保存到: " + privateKeyPath);
        
        // 保存公钥
        String publicKeyPath = outputDir + File.separator + "license-public.key";
        savePublicKeyToPemFile(keyPair.getPublic(), publicKeyPath);
        System.out.println("公钥已保存到: " + publicKeyPath);
        
        // 设置文件权限（仅限Unix系统）
        try {
            File privateKeyFile = new File(privateKeyPath);
            privateKeyFile.setReadable(false, false);
            privateKeyFile.setReadable(true, true);
            privateKeyFile.setWritable(false, false);
            privateKeyFile.setWritable(true, true);
            privateKeyFile.setExecutable(false);
            System.out.println("已设置私钥文件权限（仅所有者可读写）");
        } catch (Exception e) {
            System.out.println("警告：无法设置文件权限，请手动设置私钥文件权限");
        }
    }
    
    /**
     * 保存私钥到PEM格式文件
     */
    private static void savePrivateKeyToPemFile(PrivateKey privateKey, String filePath) throws IOException {
        String base64PrivateKey = Base64.getEncoder().encodeToString(privateKey.getEncoded());
        
        try (FileWriter writer = new FileWriter(filePath)) {
            writer.write("-----BEGIN PRIVATE KEY-----\n");
            
            // 每64个字符换行
            for (int i = 0; i < base64PrivateKey.length(); i += 64) {
                int endIndex = Math.min(i + 64, base64PrivateKey.length());
                writer.write(base64PrivateKey.substring(i, endIndex) + "\n");
            }
            
            writer.write("-----END PRIVATE KEY-----\n");
        }
    }
    
    /**
     * 保存公钥到PEM格式文件
     */
    private static void savePublicKeyToPemFile(PublicKey publicKey, String filePath) throws IOException {
        String base64PublicKey = Base64.getEncoder().encodeToString(publicKey.getEncoded());
        
        try (FileWriter writer = new FileWriter(filePath)) {
            writer.write("-----BEGIN PUBLIC KEY-----\n");
            
            // 每64个字符换行
            for (int i = 0; i < base64PublicKey.length(); i += 64) {
                int endIndex = Math.min(i + 64, base64PublicKey.length());
                writer.write(base64PublicKey.substring(i, endIndex) + "\n");
            }
            
            writer.write("-----END PUBLIC KEY-----\n");
        }
    }
    
    /**
     * 输出配置信息
     */
    private static void outputConfigurationInfo(KeyPair keyPair) throws Exception {
        System.out.println();
        System.out.println("=== 配置信息 ===");
        
        // 公钥Base64编码（用于代码中硬编码）
        String publicKeyBase64 = Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded());
        System.out.println();
        System.out.println("公钥 (Base64格式，用于Manage模块RSAKeyManager类):");
        System.out.println("将以下内容替换到RSAKeyManager.java中的PUBLIC_KEY_V1常量:");
        System.out.println();
        printWrappedString(publicKeyBase64, 80);
        
        // 私钥Base64编码（用于调试）
        String privateKeyBase64 = Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded());
        System.out.println();
        System.out.println("私钥 (Base64格式，仅用于调试，请勿在代码中硬编码):");
        printWrappedString(privateKeyBase64, 80);
        
        // 密钥指纹
        String fingerprint = generateKeyFingerprint(keyPair.getPublic());
        System.out.println();
        System.out.println("公钥指纹: " + fingerprint);
        
        // 配置说明
        System.out.println();
        System.out.println("=== 配置说明 ===");
        System.out.println("Builder模块将自动从 /opt/license/license-private.key 加载私钥");
        System.out.println("Manage模块将使用硬编码的公钥进行验证");
        System.out.println();
        System.out.println("如需自定义密钥路径，可在Builder配置中设置:");
        System.out.println("packaging.license.metadata.keyPath=/custom/path");
        System.out.println();
        System.out.println("请确保密钥文件权限设置正确（私钥仅所有者可读写）");
    }
    
    /**
     * 生成密钥指纹
     */
    private static String generateKeyFingerprint(PublicKey publicKey) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] digest = md.digest(publicKey.getEncoded());
        
        StringBuilder fingerprint = new StringBuilder();
        for (int i = 0; i < Math.min(digest.length, 8); i++) { // 取前8字节
            if (i > 0) fingerprint.append(":");
            fingerprint.append(String.format("%02x", digest[i] & 0xff));
        }
        
        return fingerprint.toString();
    }
    
    /**
     * 按指定宽度打印字符串
     */
    private static void printWrappedString(String str, int width) {
        for (int i = 0; i < str.length(); i += width) {
            int endIndex = Math.min(i + width, str.length());
            System.out.println("\"" + str.substring(i, endIndex) + "\" +");
        }
        // 移除最后一个 " +"
        System.out.println("(移除最后一行的 \" +\")");
    }
    
    /**
     * 验证生成的密钥对
     */
    private static boolean validateKeyPair(KeyPair keyPair) {
        try {
            // 测试加密解密
            String testData = "RSA密钥对验证测试";
            
            // 使用公钥加密
            Cipher encryptCipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            encryptCipher.init(Cipher.ENCRYPT_MODE, keyPair.getPublic());
            byte[] encryptedData = encryptCipher.doFinal(testData.getBytes("UTF-8"));
            
            // 使用私钥解密
            Cipher decryptCipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            decryptCipher.init(Cipher.DECRYPT_MODE, keyPair.getPrivate());
            byte[] decryptedData = decryptCipher.doFinal(encryptedData);
            
            String decryptedText = new String(decryptedData, "UTF-8");
            
            boolean isValid = testData.equals(decryptedText);
            System.out.println("密钥对验证: " + (isValid ? "通过" : "失败"));
            
            return isValid;
            
        } catch (Exception e) {
            System.err.println("密钥对验证失败: " + e.getMessage());
            return false;
        }
    }
}
