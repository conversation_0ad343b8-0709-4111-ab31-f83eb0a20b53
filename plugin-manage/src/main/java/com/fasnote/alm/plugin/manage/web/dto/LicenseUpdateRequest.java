package com.fasnote.alm.plugin.manage.web.dto;

/**
 * 许可证更新请求DTO
 */
public class LicenseUpdateRequest {
	private String licenseContent;
	private String fileName;
	private String updateReason;

	public LicenseUpdateRequest() {
	}

	public LicenseUpdateRequest(String licenseContent, String fileName, String updateReason) {
		this.licenseContent = licenseContent;
		this.fileName = fileName;
		this.updateReason = updateReason;
	}

	public String getFileName() {
		return fileName;
	}

	// Getters and Setters
	public String getLicenseContent() {
		return licenseContent;
	}

	public String getUpdateReason() {
		return updateReason;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public void setLicenseContent(String licenseContent) {
		this.licenseContent = licenseContent;
	}

	public void setUpdateReason(String updateReason) {
		this.updateReason = updateReason;
	}
}
