package com.fasnote.alm.plugin.manage.repository.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.repository.LicenseRepository;

/**
 * 内存许可证存储库实现
 */
public class InMemoryLicenseRepository implements LicenseRepository {

	private static final Logger logger = LoggerFactory.getLogger(InMemoryLicenseRepository.class);

	private final ConcurrentHashMap<String, PluginLicense> licenseStore = new ConcurrentHashMap<>();

	/**
	 * 清空所有许可证（仅用于测试）
	 */
	public void clear() {
		licenseStore.clear();
		logger.debug("清空所有许可证");
	}

	@Override
	public void deleteByPluginId(String pluginId) {
		if (pluginId == null || pluginId.trim().isEmpty()) {
			return;
		}

		PluginLicense removed = licenseStore.remove(pluginId);
		if (removed != null) {
			logger.debug("删除许可证: {}", pluginId);
		}
	}

	@Override
	public boolean existsByPluginId(String pluginId) {
		if (pluginId == null || pluginId.trim().isEmpty()) {
			return false;
		}

		return licenseStore.containsKey(pluginId);
	}

	@Override
	public List<PluginLicense> findAll() {
		return new ArrayList<>(licenseStore.values());
	}

	@Override
	public List<String> findAllPluginIds() {
		return licenseStore.keySet().stream().collect(Collectors.toList());
	}

	@Override
	public Optional<PluginLicense> findByFeature(String featureName) {
		if (featureName == null || featureName.trim().isEmpty()) {
			return Optional.empty();
		}

		return licenseStore.values().stream().filter(license -> {
			try {
				return license.hasFeature(featureName);
			} catch (Exception e) {
				logger.warn("检查许可证功能时发生异常: " + license.getPluginId(), e);
				return false;
			}
		}).findFirst();
	}

	@Override
	public Optional<PluginLicense> findByPluginId(String pluginId) {
		if (pluginId == null || pluginId.trim().isEmpty()) {
			return Optional.empty();
		}

		PluginLicense license = licenseStore.get(pluginId);
		return Optional.ofNullable(license);
	}

	@Override
	public void save(PluginLicense license) {
		if (license == null || license.getPluginId() == null) {
			throw new IllegalArgumentException("许可证或插件ID不能为空");
		}

		licenseStore.put(license.getPluginId(), license);
		logger.debug("保存许可证: {}", license.getPluginId());
	}

	/**
	 * 获取许可证数量
	 *
	 * @return 许可证数量
	 */
	public int size() {
		return licenseStore.size();
	}
}