package com.fasnote.alm.injection.api;

/**
 * 许可证验证结果类
 * 
 * 从 ILicenseValidator 内部类提取为独立类，避免内部类在混淆和依赖解析时的复杂性
 */
public class ValidationResult {
    private final boolean valid;
    private final String message;
    private final String errorCode;
    
    public ValidationResult(boolean valid, String message) {
        this(valid, message, null);
    }
    
    public ValidationResult(boolean valid, String message, String errorCode) {
        this.valid = valid;
        this.message = message;
        this.errorCode = errorCode;
    }
    
    public boolean isValid() {
        return valid;
    }
    
    public String getMessage() {
        return message;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public static ValidationResult success(String message) {
        return new ValidationResult(true, message);
    }
    
    public static ValidationResult failure(String message) {
        return new ValidationResult(false, message);
    }
    
    public static ValidationResult failure(String message, String errorCode) {
        return new ValidationResult(false, message, errorCode);
    }
    
    @Override
    public String toString() {
        return String.format("ValidationResult{valid=%s, message='%s', errorCode='%s'}", 
                           valid, message, errorCode);
    }
}
