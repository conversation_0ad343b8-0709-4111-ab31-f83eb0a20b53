<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee 
         http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         version="3.0">

    <display-name>Feishu OAuth2 Plugin</display-name>
    <description>Feishu OAuth2 authentication plugin for Polarion</description>

    <!-- 飞书用户信息获取Servlet -->
    <servlet>
        <servlet-name>FeishuUserInfoServlet</servlet-name>
        <servlet-class>com.fasnote.alm.auth.feishu.FeishuUserInfoServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>FeishuUserInfoServlet</servlet-name>
        <url-pattern>/userinfo</url-pattern>
    </servlet-mapping>

    <!-- 安全约束（可选） -->
    <security-constraint>
        <web-resource-collection>
            <web-resource-name>Feishu User Info</web-resource-name>
            <url-pattern>/userinfo</url-pattern>
            <http-method>GET</http-method>
            <http-method>POST</http-method>
        </web-resource-collection>
        <!-- 不设置auth-constraint，允许匿名访问（因为使用Bearer Token认证） -->
    </security-constraint>

</web-app>
