<!-- 飞书OAuth2配置示例 -->
<!--
使用说明：
1. 将此配置添加到Polarion的认证配置文件中
2. 认证器ID必须以"feishu"开头（如：feishu、feishu-prod、feishu-test等）
3. 配置正确的飞书应用客户端信息
4. userUrl必须配置为完整的URL路径，包含域名和端口
5. 插件的userUrl端点使用标准的Bearer Token认证方式，符合Polarion OAuth2规范
-->

<oauth2 id="feishu">
  <authorizeUrl>https://passport.feishu.cn/suite/passport/oauth/authorize</authorizeUrl>
  <tokenUrl>https://passport.feishu.cn/suite/passport/oauth/token</tokenUrl>
  <!-- userUrl必需：必须配置为完整的URL路径，包含域名和端口 -->
  <userUrl>https://your-polarion-domain/polarion/oauth-feishu/userinfo</userUrl>
  <clientId>YOUR_FEISHU_CLIENT_ID</clientId>
  <clientSecret>YOUR_FEISHU_CLIENT_SECRET</clientSecret>
  <nonce />
  <view>
    <text>飞书认证登录</text>
  </view>
  <responseParser>jsonpath</responseParser>
  <mapping>
    <id>$.id</id>
    <name>$.name</name>
    <email>$.email</email>
  </mapping>
</oauth2>

<!-- 多环境配置示例 -->
<oauth2 id="feishu-prod">
  <authorizeUrl>https://passport.feishu.cn/suite/passport/oauth/authorize</authorizeUrl>
  <tokenUrl>https://passport.feishu.cn/suite/passport/oauth/token</tokenUrl>
  <clientId>PROD_CLIENT_ID</clientId>
  <clientSecret>PROD_CLIENT_SECRET</clientSecret>
  <view>
    <text>飞书认证登录（生产环境）</text>
  </view>
  <responseParser>jsonpath</responseParser>
  <mapping>
    <id>$.id</id>
    <name>$.name</name>
    <email>$.email</email>
  </mapping>
</oauth2>

<oauth2 id="feishu-test">
  <authorizeUrl>https://passport.feishu.cn/suite/passport/oauth/authorize</authorizeUrl>
  <tokenUrl>https://passport.feishu.cn/suite/passport/oauth/token</tokenUrl>
  <userUrl>https://your-test-polarion-domain/polarion/oauth-feishu/userinfo</userUrl>
  <clientId>TEST_CLIENT_ID</clientId>
  <clientSecret>TEST_CLIENT_SECRET</clientSecret>
  <view>
    <text>飞书认证登录（测试环境）</text>
  </view>
  <responseParser>jsonpath</responseParser>
  <mapping>
    <id>$.id</id>
    <name>$.name</name>
    <email>$.email</email>
  </mapping>
</oauth2>
