package com.fasnote.alm.license.builder.validation;

/**
 * 构建阶段枚举
 * 定义许可证构建过程中的不同阶段，用于阶段感知的验证器选择
 */
public enum BuildPhase {
    
    /**
     * 混淆后阶段
     * 在代码混淆完成后，许可证构建前执行的验证
     * 主要验证：类完整性、依赖解析、Manifest文件等
     */
    POST_OBFUSCATION("post-obfuscation", "混淆后验证"),
    
    /**
     * 许可证构建后阶段  
     * 在许可证构建完成后执行的验证
     * 主要验证：许可证完整性、加密结果、插件完整性等
     */
    POST_LICENSE_BUILD("post-license-build", "许可证构建后验证"),
    
    /**
     * 所有阶段
     * 适用于所有构建阶段的通用验证器
     */
    ALL("all", "所有阶段");
    
    private final String code;
    private final String description;
    
    BuildPhase(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取构建阶段
     */
    public static BuildPhase fromCode(String code) {
        for (BuildPhase phase : values()) {
            if (phase.code.equals(code)) {
                return phase;
            }
        }
        throw new IllegalArgumentException("未知的构建阶段代码: " + code);
    }
    
    @Override
    public String toString() {
        return description + "(" + code + ")";
    }
}
