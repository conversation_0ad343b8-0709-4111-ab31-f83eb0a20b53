package com.fasnote.alm.license.crypto;

import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * RSAKeyManager 测试类
 */
public class RSAKeyManagerTest {

    private RSAKeyManager keyManager;

    @Before
    public void setUp() {
        // 使用硬编码公钥创建实例
        keyManager = new RSAKeyManager();
    }

    @Test
    public void shouldCreateInstance() {
        assertNotNull(keyManager);
        assertTrue(keyManager.hasPublicKey());
        assertFalse(keyManager.hasPrivateKey()); // 硬编码模式下没有私钥
    }

    @Test
    public void shouldReturnCorrectKeyVersion() {
        assertEquals("v1", keyManager.getKeyVersion());
    }

    @Test
    public void shouldGetPublicKeyBase64() {
        String publicKeyBase64 = keyManager.getPublicKeyBase64();
        assertNotNull(publicKeyBase64);
        assertFalse(publicKeyBase64.isEmpty());
        assertTrue(publicKeyBase64.startsWith("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A"));
    }

    @Test
    public void shouldHandleInvalidBase64Signature() {
        String testData = "test data for signature";
        String invalidSignature = "invalid-base64!@#";

        boolean result = keyManager.verifySignature(testData, invalidSignature, "v1");
        assertFalse("无效的Base64签名应该验证失败", result);
    }

    @Test
    public void shouldHandleEmptySignature() {
        String testData = "test data for signature";
        String emptySignature = "";

        boolean result = keyManager.verifySignature(testData, emptySignature, "v1");
        assertFalse("空签名应该验证失败", result);
    }

    @Test
    public void shouldHandleNullSignature() {
        String testData = "test data for signature";

        boolean result = keyManager.verifySignature(testData, null, "v1");
        assertFalse("null签名应该验证失败", result);
    }

    @Test
    public void shouldHandleUnsupportedKeyVersion() {
        String testData = "test data for signature";
        String testSignature = "dGVzdCBzaWduYXR1cmU="; // base64 encoded "test signature"

        boolean result = keyManager.verifySignature(testData, testSignature, "v999");
        assertFalse("不支持的密钥版本应该验证失败", result);
    }

    @Test(expected = IllegalStateException.class)
    public void shouldThrowExceptionWhenGeneratingSignatureWithoutPrivateKey() throws Exception {
        String testData = "test data for signature";
        keyManager.generateSignature(testData);
    }
}
