package com.fasnote.alm.license.builder.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;

/**
 * 文件系统操作工具类
 * 提供常用的文件和目录操作
 */
public class FileUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(FileUtils.class);
    
    /**
     * 确保目录存在，如果不存在则创建
     * @param dirPath 目录路径
     * @throws IOException 创建失败
     */
    public static void ensureDirectoryExists(Path dirPath) throws IOException {
        if (!Files.exists(dirPath)) {
            Files.createDirectories(dirPath);
            logger.debug("创建目录: {}", dirPath);
        }
    }
    
    /**
     * 安全删除目录及其所有内容
     * @param dirPath 目录路径
     * @throws IOException 删除失败
     */
    public static void deleteDirectoryRecursively(Path dirPath) throws IOException {
        if (!Files.exists(dirPath)) {
            return;
        }
        
        Files.walkFileTree(dirPath, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                Files.delete(file);
                return FileVisitResult.CONTINUE;
            }
            
            @Override
            public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                Files.delete(dir);
                return FileVisitResult.CONTINUE;
            }
        });
        
        logger.debug("删除目录: {}", dirPath);
    }
    
    /**
     * 清理目录内容（保留目录本身）
     * @param dirPath 目录路径
     * @throws IOException 清理失败
     */
    public static void cleanDirectory(Path dirPath) throws IOException {
        if (!Files.exists(dirPath)) {
            return;
        }
        
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(dirPath)) {
            for (Path entry : stream) {
                if (Files.isDirectory(entry)) {
                    deleteDirectoryRecursively(entry);
                } else {
                    Files.delete(entry);
                }
            }
        }
        
        logger.debug("清理目录内容: {}", dirPath);
    }
    
    /**
     * 复制文件，如果目标目录不存在则创建
     * @param source 源文件
     * @param target 目标文件
     * @param options 复制选项
     * @throws IOException 复制失败
     */
    public static void copyFileEnsureDir(Path source, Path target, CopyOption... options) throws IOException {
        Path parentDir = target.getParent();
        if (parentDir != null) {
            ensureDirectoryExists(parentDir);
        }
        
        Files.copy(source, target, options);
        logger.debug("复制文件: {} -> {}", source, target);
    }
    
    /**
     * 移动文件，如果目标目录不存在则创建
     * @param source 源文件
     * @param target 目标文件
     * @param options 移动选项
     * @throws IOException 移动失败
     */
    public static void moveFileEnsureDir(Path source, Path target, CopyOption... options) throws IOException {
        Path parentDir = target.getParent();
        if (parentDir != null) {
            ensureDirectoryExists(parentDir);
        }
        
        Files.move(source, target, options);
        logger.debug("移动文件: {} -> {}", source, target);
    }
    
    /**
     * 获取目录大小（字节）
     * @param dirPath 目录路径
     * @return 目录大小
     * @throws IOException 计算失败
     */
    public static long getDirectorySize(Path dirPath) throws IOException {
        if (!Files.exists(dirPath) || !Files.isDirectory(dirPath)) {
            return 0;
        }
        
        final long[] size = {0};
        
        Files.walkFileTree(dirPath, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                size[0] += attrs.size();
                return FileVisitResult.CONTINUE;
            }
        });
        
        return size[0];
    }
    
    /**
     * 获取文件扩展名
     * @param filePath 文件路径
     * @return 扩展名（不包含点号）
     */
    public static String getFileExtension(Path filePath) {
        String fileName = filePath.getFileName().toString();
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : "";
    }
    
    /**
     * 获取不带扩展名的文件名
     * @param filePath 文件路径
     * @return 不带扩展名的文件名
     */
    public static String getFileNameWithoutExtension(Path filePath) {
        String fileName = filePath.getFileName().toString();
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
    }
    
    /**
     * 检查文件是否为空
     * @param filePath 文件路径
     * @return 如果文件为空或不存在返回true
     */
    public static boolean isFileEmpty(Path filePath) {
        try {
            return !Files.exists(filePath) || Files.size(filePath) == 0;
        } catch (IOException e) {
            logger.warn("检查文件大小失败: {}", filePath, e);
            return true;
        }
    }
    
    /**
     * 创建临时目录
     * @param prefix 目录名前缀
     * @return 临时目录路径
     * @throws IOException 创建失败
     */
    public static Path createTempDirectory(String prefix) throws IOException {
        Path tempDir = Files.createTempDirectory(prefix);
        logger.debug("创建临时目录: {}", tempDir);
        return tempDir;
    }
    
    /**
     * 创建临时文件
     * @param prefix 文件名前缀
     * @param suffix 文件名后缀
     * @return 临时文件路径
     * @throws IOException 创建失败
     */
    public static Path createTempFile(String prefix, String suffix) throws IOException {
        Path tempFile = Files.createTempFile(prefix, suffix);
        logger.debug("创建临时文件: {}", tempFile);
        return tempFile;
    }
    
    /**
     * 格式化文件大小为人类可读形式
     * @param bytes 字节数
     * @return 格式化后的大小字符串
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * 生成唯一的文件名（如果文件已存在）
     * @param basePath 基础路径
     * @param fileName 文件名
     * @return 唯一的文件路径
     */
    public static Path generateUniqueFileName(Path basePath, String fileName) {
        Path filePath = basePath.resolve(fileName);
        
        if (!Files.exists(filePath)) {
            return filePath;
        }
        
        String nameWithoutExt = getFileNameWithoutExtension(filePath);
        String extension = getFileExtension(filePath);
        
        int counter = 1;
        do {
            String newFileName = nameWithoutExt + "_" + counter;
            if (!extension.isEmpty()) {
                newFileName += "." + extension;
            }
            filePath = basePath.resolve(newFileName);
            counter++;
        } while (Files.exists(filePath));
        
        return filePath;
    }
}