package com.fasnote.alm.auth.feishu;

import java.util.Map;

import com.polarion.platform.security.auth.IAuthenticator;

/**
 * 飞书认证器增强接口
 * 
 * 根据许可证状态决定是否对认证器进行增强处理。
 * 实现类将通过manage的DI框架进行自动注入和降级管理。
 */
public interface IFeishuAuthenticatorEnhancer {
    
    /**
     * 创建增强版认证器映射
     * 
     * @param originalAuthenticators 原始认证器映射
     * @return 处理后的认证器映射
     */
    Map<String, IAuthenticator> createEnhancedAuthenticatorMap(Map<String, IAuthenticator> originalAuthenticators);
    
    /**
     * 获取增强器类型描述
     * 
     * @return 增强器类型（用于日志记录）
     */
    String getEnhancerType();
}