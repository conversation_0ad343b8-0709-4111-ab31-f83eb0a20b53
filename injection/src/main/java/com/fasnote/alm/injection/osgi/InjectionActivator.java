package com.fasnote.alm.injection.osgi;

import org.osgi.framework.BundleActivator;
import org.osgi.framework.BundleContext;
import org.osgi.framework.ServiceRegistration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IDependencyInjector;
import com.fasnote.alm.injection.impl.DependencyInjector;

/**
 * OSGi Bundle激活器
 * 负责启动和停止依赖注入框架
 */
public class InjectionActivator implements BundleActivator {
    
    private static final Logger logger = LoggerFactory.getLogger(InjectionActivator.class);
    
    private static IDependencyInjector dependencyInjector;
    private ServiceRegistration<IDependencyInjector> serviceRegistration;
    
    @Override
    public void start(BundleContext context) throws Exception {
        logger.info("启动ALM依赖注入框架");
        
        try {
            // 1. 创建依赖注入器实例
            dependencyInjector = new DependencyInjector(context);
            
            // 2. 注册为OSGi服务
            serviceRegistration = context.registerService(
                IDependencyInjector.class, 
                dependencyInjector, 
                null
            );
            
            // 3. 扫描并安装模块（支持从其他bundle发现模块）
            scanAndInstallModules();
            
            logger.info("ALM依赖注入框架启动成功");
            
        } catch (Exception e) {
            logger.error("启动ALM依赖注入框架失败", e);
            throw e;
        }
    }
    
    @Override
    public void stop(BundleContext context) throws Exception {
        logger.info("停止ALM依赖注入框架");
        
        try {
            // 1. 注销OSGi服务
            if (serviceRegistration != null) {
                serviceRegistration.unregister();
                serviceRegistration = null;
            }
            
            // 2. 清理依赖注入器
            if (dependencyInjector != null) {
                dependencyInjector.clear();
                dependencyInjector = null;
            }
            
            logger.info("ALM依赖注入框架停止成功");
            
        } catch (Exception e) {
            logger.error("停止ALM依赖注入框架失败", e);
            throw e;
        }
    }
    
    /**
     * 获取依赖注入器实例
     */
    public static IDependencyInjector getDependencyInjector() {
        return dependencyInjector;
    }
    
    /**
     * 扫描并安装模块
     */
    private void scanAndInstallModules() {
        logger.debug("开始扫描模块");
        
        try {
            // 扫描常见的模块包路径
            String[] modulePackages = {
                "com.fasnote.alm"
            };
            
            for (String packageName : modulePackages) {
                try {
                    logger.debug("扫描模块包: {}", packageName);
                    dependencyInjector.scanAndInstallModules(packageName);
                } catch (Exception e) {
                    // 某个包扫描失败不影响其他包
                    logger.debug("扫描模块包失败: {} - {}", packageName, e.getMessage());
                }
            }
            
            logger.info("模块扫描完成，已安装模块数量: {}", dependencyInjector.getInstalledModules().size());
            
        } catch (Exception e) {
            logger.error("模块扫描失败", e);
        }
    }
}