package com.fasnote.alm.plugin.manage.security;

import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.license.crypto.RSAKeyManager;
import com.fasnote.alm.license.crypto.RSALicenseEncryption;
import com.fasnote.alm.plugin.manage.exception.SecurityException;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;

/**
 * 安全验证器实现类 负责许可证的安全验证，包括： 1. 机器码绑定验证 2. 数字签名验证 3. 时间限制验证 4. 完整性检查 5. 功能权限验证 6.
 * 用户数量限制验证
 *
 * 此实现类将被混淆加密，通过许可证文件动态加载
 */
public class SecurityValidator implements ISecurityValidator {

	private static final Logger logger = LoggerFactory.getLogger(SecurityValidator.class);
	private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
	private static final String HASH_ALGORITHM = "SHA-256";
	private String machineCode;
	private PublicKey publicKey;
	private MachineCodeProvider machineCodeProvider;
	private RSALicenseEncryption rsaEncryption;

	public SecurityValidator() {
		this.machineCodeProvider = new PolarionMachineCodeProvider();
	}

	/**
	 * 构造函数，允许注入自定义的机器码提供者（主要用于测试）
	 */
	public SecurityValidator(MachineCodeProvider machineCodeProvider) {
		this.machineCodeProvider = machineCodeProvider;
	}

	/**
	 * 从许可证构建RSA加密数据包
	 *
	 * @param license 许可证对象
	 * @return RSA加密数据包
	 */
	private RSALicenseEncryption.EncryptedLicensePackage buildRSAEncryptedPackageFromLicense(PluginLicense license) {
		logger.debug("开始构建RSA加密包，插件ID: {}", license.getPluginId());

		RSALicenseEncryption.EncryptedLicensePackage encryptedPackage = new RSALicenseEncryption.EncryptedLicensePackage();

		// 从许可证的自定义属性中提取RSA加密信息
		Map<String, Object> customProperties = license.getCustomProperties();
		if (customProperties != null) {
			logger.debug("customProperties字段数: {}", customProperties.size());

			// 提取加密版本
			String encryptionVersion = (String) customProperties.get("encryptionVersion");
			if (encryptionVersion != null) {
				encryptedPackage.setEncryptionVersion(encryptionVersion);
				logger.debug("encryptionVersion: {}", encryptionVersion);
			}

			// 提取密钥版本
			String keyVersion = (String) customProperties.get("keyVersion");
			if (keyVersion != null) {
				encryptedPackage.setKeyVersion(keyVersion);
				logger.debug("keyVersion: {}", keyVersion);
			}

			// 提取数字签名
			String digitalSignature = (String) customProperties.get("digitalSignature");
			if (digitalSignature != null) {
				encryptedPackage.setDigitalSignature(digitalSignature);
				logger.debug("digitalSignature长度: {}", digitalSignature.length());
			}

			// 提取签名算法
			String signatureAlgorithm = (String) customProperties.get("signatureAlgorithm");
			if (signatureAlgorithm != null) {
				encryptedPackage.setSignatureAlgorithm(signatureAlgorithm);
				logger.debug("signatureAlgorithm: {}", signatureAlgorithm);
			}
		}

		// 设置加密的类数据（从许可证的encryptedClassData字段获取）
		byte[] encryptedClassDataBytes = license.getEncryptedClassData();
		if (encryptedClassDataBytes != null) {
			String encryptedClassData = java.util.Base64.getEncoder().encodeToString(encryptedClassDataBytes);
			encryptedPackage.setEncryptedClassData(encryptedClassData);
		}

		// 构建元数据映射
		Map<String, Object> metadata = new java.util.HashMap<>();
		metadata.put("pluginId", license.getPluginId());
		metadata.put("productName", license.getProductName());
		metadata.put("version", license.getVersion());
		metadata.put("licenseType", license.getLicenseType());
		metadata.put("issuer", license.getIssuer());

		// 日期字段必须使用与 license-builder 相同的格式
		java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter
				.ofPattern("yyyy-MM-dd HH:mm:ss");
		if (license.getIssueDate() != null) {
			metadata.put("issueDate", license.getIssueDate().format(formatter));
		}
		if (license.getEffectiveDate() != null) {
			metadata.put("effectiveDate", license.getEffectiveDate().format(formatter));
		}
		if (license.getExpiryDate() != null) {
			metadata.put("expiryDate", license.getExpiryDate().format(formatter));
		}

		metadata.put("licensedTo", license.getLicensedTo());
		metadata.put("organization", license.getOrganization());
		metadata.put("maxUsers", license.getMaxUsers());

		encryptedPackage.setLicenseMetadata(metadata);

		return encryptedPackage;
	}

	/**
	 * 计算字符串的哈希值 使用 Base64 编码，与 license-builder 保持一致
	 *
	 * @param content 内容
	 * @return 哈希值
	 */
	private String calculateHash(String content) throws Exception {
		MessageDigest digest = MessageDigest.getInstance(HASH_ALGORITHM);
		byte[] hashBytes = digest.digest(content.getBytes("UTF-8"));
		return java.util.Base64.getEncoder().encodeToString(hashBytes);
	}

	/**
	 * 获取机器码
	 *
	 * @return 机器码
	 */
	private String generateMachineCode() throws Exception {
		try {
			String machineCode = machineCodeProvider.getMachineCode();
			logger.debug("成功获取机器码");
			return machineCode;
		} catch (Exception e) {
			logger.error("无法获取机器码: {}", e.getMessage());
			throw new SecurityException("无法获取机器码", e);
		}
	}

	@Override
	public String getCurrentMachineCode() {
		return this.machineCode;
	}

	/**
	 * 获取内置的公钥字符串 实际应用中应该从安全的地方加载
	 *
	 * @return 公钥字符串（纯净的Base64编码数据，不包含PEM头尾）
	 */
	private String getEmbeddedPublicKey() {
		return "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgE9FA70aPd9if7/6BBD2xkMZghPtgOpVJ1sgpk6VypvL6O49TXwJt0HFpZoI28L69CPm5cHRBHExxzZ0KMAFRjQ9EJG4/Wd+c5naNtApu/UCzXBj+QnfQuZbPtn2KPgJCxlLSuCX7KXohyl7KdlIr38u3T4h9MNBocPoFjBiyPlRE4Nyqvjz0GZ5vp6Yr0IPczYcvdctqlHn55Oq3Xwl7DNw1K7OZuWD1L436ckoVm15l6RrIuGn7lbaj0tWPufv/dKJQnELhh5T/7cACyBwJL6R3/oimO4L4PxXHAnoJALRucSPc5EFGG+XGCms1R5bTHQvIq7hbP8JMgm0vLxvFQIDAQAB";
	}

	/**
	 * 获取当前机器码
	 *
	 * @return 机器码
	 */
	public String getMachineCode() {
		return machineCode;
	}

	/**
	 * 初始化安全验证器
	 */
	@Override
	public void initialize() {
		logger.info("初始化安全验证器");

		try {
			// 生成当前机器的机器码
			this.machineCode = generateMachineCode();

			// 加载公钥用于签名验证
			this.publicKey = loadPublicKey();

			// 初始化RSA加密验证器
			RSAKeyManager keyManager = new RSAKeyManager(); // 使用硬编码公钥
			this.rsaEncryption = new RSALicenseEncryption(keyManager);

			logger.info("安全验证器初始化完成，机器码: {}", maskMachineCode(machineCode));

		} catch (Exception e) {
			logger.error("安全验证器初始化失败", e);
			throw new SecurityException("安全验证器初始化失败", e);
		}
	}

	@Override
	public boolean isInitialized() {
		return this.machineCode != null && this.publicKey != null;
	}

	/**
	 * 检查是否为RSA加密许可证
	 *
	 * @param license 许可证对象
	 * @return 是否为RSA加密许可证
	 */
	private boolean isRSAEncryptedLicense(PluginLicense license) {
		try {
			// 检查许可证是否包含RSA加密特有的字段
			return license.getCustomProperties() != null
					&& license.getCustomProperties().containsKey("encryptionVersion")
					&& license.getCustomProperties().containsKey("keyVersion")
					&& license.getCustomProperties().containsKey("digitalSignature")
					&& "1.0".equals(license.getCustomProperties().get("encryptionVersion"));
		} catch (Exception e) {
			logger.debug("检查RSA加密许可证时发生异常", e);
			return false;
		}
	}

	/**
	 * 加载公钥用于签名验证 按优先级顺序从多个安全位置加载公钥
	 *
	 * @return 公钥对象
	 */
	private PublicKey loadPublicKey() throws Exception {
		// 首先尝试从安全位置加载公钥
		String publicKeyString = loadPublicKeyFromSecureLocation();

		if (publicKeyString != null && !publicKeyString.trim().isEmpty()) {
			return parsePublicKey(publicKeyString);
		} else {
			// 如果没有找到外部公钥，使用内置公钥
			logger.warn("未找到外部公钥配置，使用内置公钥");
			publicKeyString = getEmbeddedPublicKey();
			return parsePublicKey(publicKeyString);
		}
	}

	/**
	 * 从类路径加载公钥
	 *
	 * @param resourceName 资源文件名
	 * @return 公钥字符串，如果资源不存在返回null
	 */
	private String loadPublicKeyFromClasspath(String resourceName) {
		try (java.io.InputStream inputStream = getClass().getClassLoader().getResourceAsStream(resourceName)) {
			if (inputStream == null) {
				return null;
			}

			try (java.io.BufferedReader reader = new java.io.BufferedReader(
					new java.io.InputStreamReader(inputStream, java.nio.charset.StandardCharsets.UTF_8))) {

				StringBuilder keyBuilder = new StringBuilder();
				String line;
				boolean inKey = false;

				while ((line = reader.readLine()) != null) {
					line = line.trim();

					if (line.startsWith("-----BEGIN PUBLIC KEY-----")) {
						inKey = true;
						continue;
					} else if (line.startsWith("-----END PUBLIC KEY-----")) {
						break;
					} else if (inKey && !line.isEmpty()) {
						keyBuilder.append(line);
					} else if (!inKey && !line.isEmpty() && !line.startsWith("#")) {
						keyBuilder.append(line);
					}
				}

				String key = keyBuilder.toString().trim();
				return key.isEmpty() ? null : key;
			}

		} catch (Exception e) {
			logger.warn("从类路径加载公钥失败: {}", resourceName, e);
			return null;
		}
	}

	/**
	 * 从文件加载公钥
	 *
	 * @param filePath 公钥文件路径
	 * @return 公钥字符串，如果文件不存在或无法读取返回null
	 */
	private String loadPublicKeyFromFile(String filePath) {
		try {
			java.io.File keyFile = new java.io.File(filePath);
			if (!keyFile.exists() || !keyFile.isFile()) {
				return null;
			}

			// 读取公钥文件内容
			try (java.io.BufferedReader reader = new java.io.BufferedReader(
					new java.io.FileReader(keyFile, java.nio.charset.StandardCharsets.UTF_8))) {

				StringBuilder keyBuilder = new StringBuilder();
				String line;
				boolean inKey = false;

				while ((line = reader.readLine()) != null) {
					line = line.trim();

					// 处理PEM格式的公钥
					if (line.startsWith("-----BEGIN PUBLIC KEY-----")) {
						inKey = true;
						continue;
					} else if (line.startsWith("-----END PUBLIC KEY-----")) {
						break;
					} else if (inKey && !line.isEmpty()) {
						keyBuilder.append(line);
					} else if (!inKey && !line.isEmpty() && !line.startsWith("#")) {
						// 处理纯Base64格式的公钥（无PEM头尾）
						keyBuilder.append(line);
					}
				}

				String key = keyBuilder.toString().trim();
				return key.isEmpty() ? null : key;
			}

		} catch (Exception e) {
			logger.warn("读取公钥文件失败: {}", filePath, e);
			return null;
		}
	}

	/**
	 * 从安全位置加载公钥 按优先级顺序尝试多种加载方式
	 *
	 * @return 公钥字符串，如果未找到返回null
	 */
	private String loadPublicKeyFromSecureLocation() {
		// 1. 优先从系统属性加载
		String publicKey = System.getProperty("license.public.key");
		if (publicKey != null && !publicKey.trim().isEmpty()) {
			logger.info("从系统属性加载公钥");
			return publicKey.trim();
		}

		// 2. 从环境变量加载
		publicKey = System.getenv("LICENSE_PUBLIC_KEY");
		if (publicKey != null && !publicKey.trim().isEmpty()) {
			logger.info("从环境变量加载公钥");
			return publicKey.trim();
		}

		// 3. 从配置对象获取公钥文件路径
		try {
			com.fasnote.alm.plugin.manage.config.LicenseConfiguration config = com.fasnote.alm.plugin.manage.config.LicenseConfiguration
					.getInstance();
			String keyPath = config.getPublicKeyPath();
			if (keyPath != null && !keyPath.trim().isEmpty()) {
				try {
					publicKey = loadPublicKeyFromFile(keyPath.trim());
					if (publicKey != null) {
						logger.info("从配置文件路径加载公钥: {}", keyPath);
						return publicKey;
					}
				} catch (Exception e) {
					logger.warn("从配置文件路径加载公钥失败: {}", keyPath, e);
				}
			}
		} catch (Exception e) {
			logger.debug("获取配置实例失败", e);
		}

		// 4. 尝试从默认位置加载公钥文件
		String[] defaultKeyPaths = { System.getProperty("user.home") + "/.license/public.key",
				"/etc/license/public.key", System.getProperty("user.dir") + "/license-public.key" };

		for (String defaultPath : defaultKeyPaths) {
			try {
				publicKey = loadPublicKeyFromFile(defaultPath);
				if (publicKey != null) {
					logger.info("从默认路径加载公钥: {}", defaultPath);
					return publicKey;
				}
			} catch (Exception e) {
				logger.debug("默认路径公钥文件不存在或无法读取: {}", defaultPath);
			}
		}

		// 5. 尝试从类路径加载公钥
		try {
			publicKey = loadPublicKeyFromClasspath("license-public.key");
			if (publicKey != null) {
				logger.info("从类路径加载公钥");
				return publicKey;
			}
		} catch (Exception e) {
			logger.debug("从类路径加载公钥失败", e);
		}

		logger.debug("未能从任何安全位置加载公钥");
		return null;
	}

	/**
	 * 掩码机器码用于日志记录
	 *
	 * @param machineCode 机器码
	 * @return 掩码后的机器码
	 */
	private String maskMachineCode(String machineCode) {
		if (machineCode == null || machineCode.length() < 8) {
			return "****";
		}

		return machineCode.substring(0, 4) + "****" + machineCode.substring(machineCode.length() - 4);
	}

	/**
	 * 解析公钥字符串为PublicKey对象
	 *
	 * @param publicKeyString Base64编码的公钥字符串
	 * @return PublicKey对象
	 * @throws Exception 解析失败时抛出异常
	 */
	private PublicKey parsePublicKey(String publicKeyString) throws Exception {
		try {
			byte[] keyBytes = Base64.getDecoder().decode(publicKeyString);
			X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
			KeyFactory keyFactory = KeyFactory.getInstance("RSA");
			return keyFactory.generatePublic(keySpec);
		} catch (Exception e) {
			logger.error("解析公钥失败", e);
			throw new SecurityException("解析公钥失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 验证功能权限
	 *
	 * @param license     许可证对象
	 * @param featureName 功能名称
	 * @return 验证结果
	 */
	@Override
	public ValidationResult validateFeaturePermission(PluginLicense license, String featureName) {
		try {
			if (featureName == null || featureName.trim().isEmpty()) {
				return ValidationResult.failure("功能名称不能为空");
			}

			// 检查许可证是否包含该功能
			if (!license.hasFeature(featureName)) {
				logger.warn("功能权限验证失败，许可证不包含功能: {}", featureName);
				return ValidationResult.failure("许可证不包含功能: " + featureName);
			}

			// 检查功能是否启用
			Object featureValue = license.getFeatures().get(featureName);
			if (featureValue instanceof Boolean && !(Boolean) featureValue) {
				return ValidationResult.failure("功能已禁用: " + featureName);
			}

			// 检查功能限制
			Object featureLimit = license.getFeatureLimit(featureName);
			if (featureLimit != null) {
				logger.debug("功能 {} 存在限制: {}", featureName, featureLimit);
			}

			logger.debug("功能权限验证通过: {}", featureName);
			return ValidationResult.success("功能权限验证通过: " + featureName);

		} catch (Exception e) {
			logger.error("功能权限验证异常", e);
			return ValidationResult.failure("功能权限验证异常: " + e.getMessage());
		}
	}

	/**
	 * 验证许可证完整性
	 *
	 * @param license 许可证对象
	 * @return 验证结果
	 */
	@Override
	public ValidationResult validateIntegrity(PluginLicense license) {
		try {
			String expectedHash = license.getContentHash();
			if (expectedHash == null || expectedHash.trim().isEmpty()) {
				return ValidationResult.failure("许可证缺少完整性校验信息");
			}

			// 计算许可证内容的哈希值
			String licenseContent = license.getContentForHash();
			String actualHash = calculateHash(licenseContent);

			if (!expectedHash.equals(actualHash)) {
				logger.warn("完整性验证失败");
				return ValidationResult.failure("许可证完整性验证失败，内容可能被修改");
			}

			logger.debug("完整性验证通过");
			return ValidationResult.success("完整性验证通过");

		} catch (Exception e) {
			logger.error("完整性验证异常", e);
			return ValidationResult.failure("完整性验证异常: " + e.getMessage());
		}
	}

	/**
	 * 验证许可证
	 *
	 * @param license 许可证对象
	 * @return 验证结果
	 */
	@Override
	public ValidationResult validateLicense(PluginLicense license) {
		if (license == null) {
			return ValidationResult.failure("许可证为空");
		}

		try {
			logger.info("开始验证许可证: {}", license.getPluginId());

			// 基本验证：检查许可证是否包含必要信息
			if (license.getPluginId() == null || license.getPluginId().trim().isEmpty()) {
				return ValidationResult.failure("许可证缺少插件ID");
			}

			// 注意：完整性验证和数字签名验证已在crypto模块中完成
			// 这里只进行业务逻辑验证：时间限制、机器码、功能权限等
			logger.info("crypto模块验证已通过，开始业务逻辑验证");

			// 1. 时间限制验证 - 检查许可证是否在有效期内
			ValidationResult timeResult = validateTimeLimit(license);
			if (!timeResult.isSuccess()) {
				return timeResult;
			}

			// 4. 机器码绑定验证 - 检查许可证是否能在当前机器使用
			ValidationResult machineResult = validateMachineBinding(license);
			if (!machineResult.isSuccess()) {
				return machineResult;
			}

			logger.info("许可证验证通过: {}", license.getPluginId());
			return ValidationResult.success("许可证验证通过");

		} catch (Exception e) {
			String errorMessage = "许可证验证失败: " + e.getMessage();
			logger.error(errorMessage, e);
			return ValidationResult.failure(errorMessage);
		}
	}

	/**
	 * 验证许可证（包含功能权限验证）
	 *
	 * @param license     许可证对象
	 * @param featureName 需要验证的功能名称
	 * @return 验证结果
	 */
	@Override
	public ValidationResult validateLicenseWithFeature(PluginLicense license, String featureName) {
		// 首先进行基本的许可证验证
		ValidationResult basicResult = validateLicense(license);
		if (!basicResult.isSuccess()) {
			return basicResult;
		}

		// 然后验证功能权限
		ValidationResult featureResult = validateFeaturePermission(license, featureName);
		if (!featureResult.isSuccess()) {
			return featureResult;
		}

		logger.info("许可证和功能权限验证通过: {}, 功能: {}", license.getPluginId(), featureName);
		return ValidationResult.success("许可证和功能权限验证通过");
	}

	/**
	 * 验证许可证（包含用户数量限制验证）
	 *
	 * @param license          许可证对象
	 * @param currentUserCount 当前用户数量
	 * @return 验证结果
	 */
	@Override
	public ValidationResult validateLicenseWithUserLimit(PluginLicense license, int currentUserCount) {
		// 首先进行基本的许可证验证
		ValidationResult basicResult = validateLicense(license);
		if (!basicResult.isSuccess()) {
			return basicResult;
		}

		// 然后验证用户数量限制
		ValidationResult userLimitResult = validateUserLimit(license, currentUserCount);
		if (!userLimitResult.isSuccess()) {
			return userLimitResult;
		}

		logger.info("许可证和用户数量验证通过: {}, 用户数: {}", license.getPluginId(), currentUserCount);
		return ValidationResult.success("许可证和用户数量验证通过");
	}

	/**
	 * 验证机器码绑定
	 *
	 * @param license 许可证对象
	 * @return 验证结果
	 */
	@Override
	public ValidationResult validateMachineBinding(PluginLicense license) {
		try {
			String licenseMachineCode = license.getMachineCode();

			// 如果许可证没有绑定机器码，则跳过验证
			if (licenseMachineCode == null || licenseMachineCode.trim().isEmpty()) {
				logger.debug("许可证未绑定机器码，跳过机器码验证");
				return ValidationResult.success("未绑定机器码");
			}

			// 验证机器码是否匹配
			if (!machineCode.equals(licenseMachineCode)) {
				logger.warn("机器码验证失败，当前: {}, 许可证: {}", maskMachineCode(machineCode),
						maskMachineCode(licenseMachineCode));
				return ValidationResult.failure("机器码不匹配，许可证无法在当前机器上使用");
			}

			logger.debug("机器码验证通过");
			return ValidationResult.success("机器码验证通过");

		} catch (Exception e) {
			logger.error("机器码验证异常", e);
			return ValidationResult.failure("机器码验证异常: " + e.getMessage());
		}
	}

	/**
	 * 验证RSA加密许可证的数字签名
	 *
	 * @param license 许可证对象
	 * @return 验证结果
	 */
	private ValidationResult validateRSAEncryptedSignature(PluginLicense license) {
		try {
			logger.info("开始验证RSA加密许可证签名");

			// 构建RSA加密数据包
			RSALicenseEncryption.EncryptedLicensePackage encryptedPackage = buildRSAEncryptedPackageFromLicense(
					license);

			// 使用crypto模块验证签名
			try {
				rsaEncryption.decryptAndVerifyLicensePackage(encryptedPackage);
				logger.info("RSA加密许可证签名验证通过");
				return ValidationResult.success("RSA加密许可证签名验证通过");
			} catch (Exception e) {
				logger.warn("RSA加密许可证签名验证失败: {}", e.getMessage());
				return ValidationResult.failure("RSA加密许可证签名验证失败: " + e.getMessage());
			}

		} catch (Exception e) {
			logger.error("RSA加密许可证签名验证异常", e);
			return ValidationResult.failure("RSA加密许可证签名验证异常: " + e.getMessage());
		}
	}

	/**
	 * 验证数字签名
	 *
	 * @param license 许可证对象
	 * @return 验证结果
	 */
	@Override
	public ValidationResult validateSignature(PluginLicense license) {
		try {
			// 检查是否为RSA加密许可证
			if (isRSAEncryptedLicense(license)) {
				return validateRSAEncryptedSignature(license);
			}

			// 传统签名验证（已废弃）
			String licenseSignature = license.getSignature();
			if (licenseSignature == null || licenseSignature.trim().isEmpty()) {
				return ValidationResult.failure("许可证缺少数字签名");
			}

			// 如果公钥为空，跳过验证
			if (publicKey == null) {
				return ValidationResult.failure("未配置公钥，无法验证数字签名");
			}

			// 获取许可证内容（不包括签名部分）
			String licenseContent = license.getContentForSignature();

			// 验证签名
			Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
			signature.initVerify(publicKey);
			signature.update(licenseContent.getBytes("UTF-8"));

			byte[] signatureBytes = Base64.getDecoder().decode(licenseSignature);
			boolean isValid = signature.verify(signatureBytes);

			if (!isValid) {
				logger.warn("数字签名验证失败");
				return ValidationResult.failure("数字签名验证失败，许可证可能被篡改");
			}

			logger.debug("数字签名验证通过");
			return ValidationResult.success("数字签名验证通过");

		} catch (Exception e) {
			logger.error("数字签名验证异常", e);
			return ValidationResult.failure("数字签名验证异常: " + e.getMessage());
		}
	}

	/**
	 * 验证时间限制
	 *
	 * @param license 许可证对象
	 * @return 验证结果
	 */
	@Override
	public ValidationResult validateTimeLimit(PluginLicense license) {
		try {
			LocalDateTime now = LocalDateTime.now();

			// 检查生效时间
			LocalDateTime effectiveDate = license.getEffectiveDate();
			if (effectiveDate != null && now.isBefore(effectiveDate)) {
				return ValidationResult.failure("许可证尚未生效，生效时间: " + effectiveDate);
			}

			// 检查过期时间
			LocalDateTime expiryDate = license.getExpiryDate();
			if (expiryDate != null && now.isAfter(expiryDate)) {
				return ValidationResult.failure("许可证已过期，过期时间: " + expiryDate);
			}

			logger.debug("时间限制验证通过");
			return ValidationResult.success("时间限制验证通过");

		} catch (Exception e) {
			logger.error("时间限制验证异常", e);
			return ValidationResult.failure("时间限制验证异常: " + e.getMessage());
		}
	}

	/**
	 * 验证用户数量限制
	 *
	 * @param license          许可证对象
	 * @param currentUserCount 当前用户数量
	 * @return 验证结果
	 */
	@Override
	public ValidationResult validateUserLimit(PluginLicense license, int currentUserCount) {
		try {
			int maxUsers = license.getMaxUsers();

			// 如果maxUsers为0或负数，表示无限制
			if (maxUsers <= 0) {
				logger.debug("许可证无用户数量限制");
				return ValidationResult.success("无用户数量限制");
			}

			if (currentUserCount > maxUsers) {
				logger.warn("用户数量超出限制，当前: {}, 最大: {}", currentUserCount, maxUsers);
				return ValidationResult.failure("用户数量超出限制，当前: " + currentUserCount + ", 最大允许: " + maxUsers);
			}

			logger.debug("用户数量验证通过，当前: {}, 最大: {}", currentUserCount, maxUsers);
			return ValidationResult.success("用户数量验证通过");

		} catch (Exception e) {
			logger.error("用户数量验证异常", e);
			return ValidationResult.failure("用户数量验证异常: " + e.getMessage());
		}
	}
}
