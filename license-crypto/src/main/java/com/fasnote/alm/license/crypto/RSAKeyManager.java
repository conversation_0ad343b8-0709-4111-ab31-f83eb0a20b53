package com.fasnote.alm.license.crypto;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * RSA密钥管理器 - 独立的加解密模块
 * 负责RSA密钥的加载、管理和签名验证
 */
public class RSAKeyManager {
    
    private static final Logger logger = LoggerFactory.getLogger(RSAKeyManager.class);
    private static final String LOG_PREFIX = "[RSAKeyManager] ";
    
    // 硬编码的公钥（从 /opt/license/license-public.key 获取）
    private static final String PUBLIC_KEY_V1 =
        "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtjoaKez2S8TEiFttvtls" +
        "1qgKmluo5Lem5rnpr3DjKaOdIx3Bl2+ySZcuK8hLOaHKI5QNXTabQz/FJLeNfah0" +
        "VxmbIayf2dYpsbSRjRfPYn67la6+Gmg3rSCVar/pUFWtz8XPiMWVYMRJ4PuVCncy" +
        "wev4EgINuGEfxcIivcvfTcEgljyJM+V5kYV9LH/h+QgurbnCYF1KMx1jhNepylZU" +
        "G/Wyt/4g4v2jjqOQVM0P4eDkGhEc4W1yYkDoXhr7bkfZrIP2x9/fWI8mIoMuH5q6" +
        "inAb6y9cIrPf62nujfuWLGKIuzG9outm1mWL3EGqq9kzibYQQPWFlkQYjNqqlEAR" +
        "QwIDAQAB";
    
    private PrivateKey privateKey;
    private PublicKey publicKey;
    private String keyVersion = "v1";
    
    /**
     * 构造函数 - 从指定路径加载密钥对
     */
    public RSAKeyManager(String keyPath) {
        loadKeyPairFromPath(keyPath);
    }
    
    /**
     * 构造函数 - 使用硬编码公钥（仅用于验证）
     */
    public RSAKeyManager() {
        loadPublicKeyFromConstant();
    }
    
    /**
     * 从路径加载RSA密钥对
     */
    private void loadKeyPairFromPath(String keyPath) {
        try {
            logger.info(LOG_PREFIX + "开始从路径加载RSA密钥对: {}", keyPath);
            
            // 加载私钥
            String privateKeyPath = keyPath + "/license-private.key";
            logger.info(LOG_PREFIX + "从路径加载私钥: {}", privateKeyPath);
            this.privateKey = loadPrivateKeyFromFile(privateKeyPath);
            
            // 加载公钥
            String publicKeyPath = keyPath + "/license-public.key";
            logger.info(LOG_PREFIX + "从路径加载公钥: {}", publicKeyPath);
            this.publicKey = loadPublicKeyFromFile(publicKeyPath);
            
            logger.info(LOG_PREFIX + "成功加载RSA密钥对，版本: {}", keyVersion);
            
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "加载RSA密钥对失败", e);
            throw new RuntimeException("RSA密钥对加载失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从硬编码常量加载公钥
     */
    private void loadPublicKeyFromConstant() {
        try {
            logger.info(LOG_PREFIX + "使用硬编码公钥，版本: {}", keyVersion);
            this.publicKey = loadPublicKeyFromBase64(PUBLIC_KEY_V1);
            logger.info(LOG_PREFIX + "成功加载硬编码公钥");
            
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "加载硬编码公钥失败", e);
            throw new RuntimeException("硬编码公钥加载失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从文件加载私钥
     */
    private PrivateKey loadPrivateKeyFromFile(String filePath) throws Exception {
        byte[] keyBytes = Files.readAllBytes(Paths.get(filePath));
        String keyContent = new String(keyBytes, "UTF-8").trim();
        
        // 移除PEM格式的头尾标记
        String cleanKeyContent = keyContent.replaceAll("-----BEGIN PRIVATE KEY-----", "")
                                          .replaceAll("-----END PRIVATE KEY-----", "")
                                          .replaceAll("\\s", "");
        
        byte[] decodedKey = Base64.getDecoder().decode(cleanKeyContent);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodedKey);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(keySpec);
    }
    
    /**
     * 从文件加载公钥
     */
    private PublicKey loadPublicKeyFromFile(String filePath) throws Exception {
        byte[] keyBytes = Files.readAllBytes(Paths.get(filePath));
        String keyContent = new String(keyBytes, "UTF-8").trim();
        
        // 移除PEM格式的头尾标记
        String cleanKeyContent = keyContent.replaceAll("-----BEGIN PUBLIC KEY-----", "")
                                          .replaceAll("-----END PUBLIC KEY-----", "")
                                          .replaceAll("\\s", "");
        
        return loadPublicKeyFromBase64(cleanKeyContent);
    }
    
    /**
     * 从Base64字符串加载公钥
     */
    private PublicKey loadPublicKeyFromBase64(String base64Key) throws Exception {
        byte[] decodedKey = Base64.getDecoder().decode(base64Key);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decodedKey);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(keySpec);
    }
    
    /**
     * 使用私钥生成数字签名
     */
    public String generateSignature(String data) throws Exception {
        if (privateKey == null) {
            throw new IllegalStateException("私钥未加载，无法生成签名");
        }
        
        logger.debug(LOG_PREFIX + "开始生成数字签名，数据长度: {}", data.length());
        
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(data.getBytes("UTF-8"));
        
        byte[] signatureBytes = signature.sign();
        String signatureBase64 = Base64.getEncoder().encodeToString(signatureBytes);
        
        logger.debug(LOG_PREFIX + "数字签名生成完成，签名长度: {}", signatureBase64.length());
        return signatureBase64;
    }
    
    /**
     * 使用公钥验证数字签名
     */
    public boolean verifySignature(String data, String signatureBase64, String keyVersion) {
        try {
            logger.debug(LOG_PREFIX + "开始验证数字签名，密钥版本: {}", keyVersion);
            logger.debug(LOG_PREFIX + "待验证数据长度: {}", data.length());
            
            byte[] signatureBytes = Base64.getDecoder().decode(signatureBase64);
            logger.debug(LOG_PREFIX + "签名字节长度: {}", signatureBytes.length);
            
            PublicKey keyToUse = getPublicKeyByVersion(keyVersion);
            logger.debug(LOG_PREFIX + "成功加载公钥，版本: {}", keyVersion);
            logger.debug(LOG_PREFIX + "成功获取公钥，算法: {}", keyToUse.getAlgorithm());
            logger.debug(LOG_PREFIX + "公钥格式: {}", keyToUse.getFormat());
            
            Signature signature = Signature.getInstance("SHA256withRSA");
            logger.debug(LOG_PREFIX + "使用签名算法: SHA256withRSA");
            
            signature.initVerify(keyToUse);
            signature.update(data.getBytes("UTF-8"));
            
            logger.debug(LOG_PREFIX + "开始执行签名验证");
            boolean result = signature.verify(signatureBytes);
            logger.debug(LOG_PREFIX + "签名验证完成，结果: {}", result);
            
            if (!result) {
                logger.warn(LOG_PREFIX + "数字签名验证失败，密钥版本: {}", keyVersion);
                logger.debug(LOG_PREFIX + "验证失败的数据前100字符: {}", 
                    data.substring(0, Math.min(100, data.length())));
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "数字签名验证过程中发生异常", e);
            return false;
        }
    }
    
    /**
     * 根据版本获取公钥
     */
    private PublicKey getPublicKeyByVersion(String version) throws Exception {
        if ("v1".equals(version)) {
            return publicKey;
        } else {
            throw new IllegalArgumentException("不支持的密钥版本: " + version);
        }
    }
    
    /**
     * 获取公钥的Base64编码
     */
    public String getPublicKeyBase64() {
        if (publicKey == null) {
            return null;
        }
        return Base64.getEncoder().encodeToString(publicKey.getEncoded());
    }
    
    /**
     * 获取密钥版本
     */
    public String getKeyVersion() {
        return keyVersion;
    }
    
    /**
     * 检查是否有私钥
     */
    public boolean hasPrivateKey() {
        return privateKey != null;
    }
    
    /**
     * 检查是否有公钥
     */
    public boolean hasPublicKey() {
        return publicKey != null;
    }
}
