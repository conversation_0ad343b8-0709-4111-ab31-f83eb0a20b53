package com.fasnote.alm.injection.impl;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.fasnote.alm.injection.api.IDependencyInjector;
import com.fasnote.alm.injection.api.IInjectionContext;

/**
 * 依赖注入上下文实现
 */
public class InjectionContext implements IInjectionContext {
    
    private final IDependencyInjector injector;
    private final Class<?> currentServiceClass;
    private final String serviceName;
    private final Map<String, Object> attributes = new ConcurrentHashMap<>();
    
    public InjectionContext(IDependencyInjector injector, Class<?> currentServiceClass, String serviceName) {
        this.injector = injector;
        this.currentServiceClass = currentServiceClass;
        this.serviceName = serviceName;
    }
    
    @Override
    public IDependencyInjector getInjector() {
        return injector;
    }
    
    @Override
    public Class<?> getCurrentServiceClass() {
        return currentServiceClass;
    }
    
    @Override
    public String getServiceName() {
        return serviceName;
    }
    
    @Override
    public Object getAttribute(String key) {
        return attributes.get(key);
    }
    
    @Override
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }
    
    @Override
    public Map<String, Object> getAttributes() {
        return new ConcurrentHashMap<>(attributes);
    }
    
    @Override
    public <T> T createDependency(Class<T> dependencyClass) throws Exception {
        return injector.createInstance(dependencyClass);
    }
}