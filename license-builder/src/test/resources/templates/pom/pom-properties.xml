    <properties>
        <tycho.version>${tycho_version}</tycho.version>
        <maven.compiler.source>${java_version}</maven.compiler.source>
        <maven.compiler.target>${java_version}</maven.compiler.target>
        <maven.test.skip>true</maven.test.skip>
        <skipTests>true</skipTests>
        <license.builder.jar>${license_builder_jar}</license.builder.jar>
        <license.builder.config>${license_builder_config}</license.builder.config>
        <obfuscation.tool>${obfuscation_tool}</obfuscation.tool>
        <obfuscation.level>${obfuscation_level}</obfuscation.level>
        <bundle.symbolicName>${bundle_symbolic_name}</bundle.symbolicName>
        <bundle.version>${bundle_version}</bundle.version>
        <bundle.activator>${bundle_activator}</bundle.activator>
        <bundle.exportedPackages>${bundle_exported_packages}</bundle.exportedPackages>
    </properties>
