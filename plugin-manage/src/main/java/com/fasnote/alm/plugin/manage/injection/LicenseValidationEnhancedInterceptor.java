package com.fasnote.alm.plugin.manage.injection;

import java.lang.reflect.Method;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IEnhancedServiceInterceptor;
import com.fasnote.alm.injection.api.IInjectionContext;
import com.fasnote.alm.plugin.manage.annotation.FeatureRequired;
import com.fasnote.alm.plugin.manage.annotation.LicenseRequired;
import com.fasnote.alm.plugin.manage.annotation.UserLimitCheck;
import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;

/**
 * 增强的许可证验证拦截器
 *
 * 同时支持服务级别和方法级别的许可证验证 处理@LicenseRequired、@FeatureRequired、@UserLimitCheck注解
 */
public class LicenseValidationEnhancedInterceptor implements IEnhancedServiceInterceptor {

	private static final Logger logger = LoggerFactory.getLogger(LicenseValidationEnhancedInterceptor.class);
	private static final String LOG_PREFIX = "[LicenseValidationEnhancedInterceptor] ";

	private final LicenseManager licenseManager;
	private final SecurityValidator securityValidator;

	public LicenseValidationEnhancedInterceptor(LicenseManager licenseManager, SecurityValidator securityValidator) {
		this.licenseManager = licenseManager;
		this.securityValidator = securityValidator;
	}

	@Override
	public Object afterCreate(Class serviceClass, Object instance, IInjectionContext context) {
		// 服务级别的处理保持不变，主要用于注入许可证信息等
		return instance;
	}

	@Override
	public Object beforeMethodInvocation(Object target, Method method, Object[] args) {
		try {
			// 1. 检查基本许可证验证
			LicenseRequired licenseRequired = getAnnotation(target, method, LicenseRequired.class);
			if (licenseRequired != null && licenseRequired.enabled()) {
				ValidationResult result = validateBasicLicense(target, method);
				if (!result.isSuccess()) {
					return handleValidationFailure(licenseRequired.onFailure(), licenseRequired.message(),
							result.getMessage());
				}
			}

			// 2. 检查功能权限验证
			FeatureRequired featureRequired = getAnnotation(target, method, FeatureRequired.class);
			if (featureRequired != null && featureRequired.enabled()) {
				ValidationResult result = validateFeaturePermission(target, method, featureRequired);
				if (!result.isSuccess()) {
					return handleValidationFailure(featureRequired.onFailure(), featureRequired.message(),
							result.getMessage());
				}
			}

			// 3. 检查用户数量限制
			UserLimitCheck userLimitCheck = getAnnotation(target, method, UserLimitCheck.class);
			if (userLimitCheck != null && userLimitCheck.enabled()) {
				ValidationResult result = validateUserLimit(target, method, userLimitCheck, args);
				if (!result.isSuccess()) {
					return handleValidationFailure(userLimitCheck.onFailure(), userLimitCheck.message(),
							result.getMessage());
				}
			}

			return null; // 继续正常执行

		} catch (Exception e) {
			logger.error(LOG_PREFIX + "许可证验证过程中发生异常", e);
			throw new SecurityException("许可证验证失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 从目标对象和方法中提取插件ID
	 */
	private String extractPluginId(Object target, Method method) {
		// 简单实现：从包名提取插件ID
		String packageName = target.getClass().getPackage().getName();

		// 假设包名格式为: com.fasnote.xxx.yyy，提取com.fasnote.xxx作为插件ID
		String[] parts = packageName.split("\\.");
		if (parts.length >= 3) {
			return String.join(".", parts[0], parts[1], parts[2]);
		}

		return packageName;
	}

	/**
	 * 获取注解（优先从方法获取，然后从类获取）
	 */
	private <T extends java.lang.annotation.Annotation> T getAnnotation(Object target, Method method,
			Class<T> annotationClass) {
		T annotation = method.getAnnotation(annotationClass);
		if (annotation == null) {
			annotation = target.getClass().getAnnotation(annotationClass);
		}
		return annotation;
	}

	/**
	 * 获取当前用户数量
	 */
	private int getCurrentUserCount(UserLimitCheck annotation, Object[] args) {
		switch (annotation.userCountSource()) {
		case AUTO_DETECT:
			// TODO: 从Polarion系统获取当前用户数量
			return 1; // 临时返回1

		case METHOD_PARAMETER:
			// 从方法参数中查找int类型的参数
			for (Object arg : args) {
				if (arg instanceof Integer) {
					return (Integer) arg;
				}
			}
			throw new IllegalArgumentException("未找到int类型的用户数量参数");

		case CUSTOM:
			// TODO: 使用自定义提供者
			return 1; // 临时返回1

		case FIXED_VALUE:
			return 1; // 固定值，主要用于测试

		default:
			return 1;
		}
	}

	@Override
	public String getInterceptorName() {
		return "LicenseValidationEnhancedInterceptor";
	}

	@Override
	public int getPriority() {
		return 10; // 高优先级，确保许可证验证在其他拦截器之前执行
	}

	/**
	 * 处理验证失败
	 */
	private Object handleValidationFailure(LicenseRequired.FailureStrategy strategy, String customMessage,
			String defaultMessage) {
		String message = customMessage.isEmpty() ? defaultMessage : customMessage;

		switch (strategy) {
		case THROW_EXCEPTION:
			throw new SecurityException(message);

		case RETURN_NULL:
			logger.warn(LOG_PREFIX + "许可证验证失败，返回null: " + message);
			return null;

		case DEGRADED_SERVICE:
			logger.warn(LOG_PREFIX + "许可证验证失败，应执行降级服务: " + message);
			// TODO: 实现降级服务逻辑
			return null;

		case LOG_AND_CONTINUE:
			logger.warn(LOG_PREFIX + "许可证验证失败，但继续执行: " + message);
			return null; // 继续正常执行

		default:
			throw new SecurityException(message);
		}
	}

	/**
	 * 检查类是否有许可证验证注解
	 */
	private boolean hasLicenseAnnotations(Class<?> clazz) {
		return clazz.isAnnotationPresent(LicenseRequired.class) || clazz.isAnnotationPresent(FeatureRequired.class)
				|| clazz.isAnnotationPresent(UserLimitCheck.class);
	}

	/**
	 * 检查类的方法是否有许可证验证注解
	 */
	private boolean hasMethodLevelAnnotations(Class<?> clazz) {
		for (Method method : clazz.getMethods()) {
			if (method.isAnnotationPresent(LicenseRequired.class) || method.isAnnotationPresent(FeatureRequired.class)
					|| method.isAnnotationPresent(UserLimitCheck.class)) {
				return true;
			}
		}
		return false;
	}

	@Override
	public boolean needsMethodInterception(Class serviceClass, Object instance) {
		// 检查是否需要方法级别的拦截
		return hasLicenseAnnotations(serviceClass) || hasMethodLevelAnnotations(serviceClass);
	}

	@Override
	public Throwable onMethodException(Object target, Method method, Object[] args, Throwable exception) {
		// 记录许可证验证相关的异常
		if (exception instanceof SecurityException) {
			logger.warn(LOG_PREFIX + "许可证验证异常: {} - {}", method.getName(), exception.getMessage());
		}
		return exception;
	}

	@Override
	public boolean shouldIntercept(Class serviceClass) {
		// 检查类级别是否有许可证验证注解
		return hasLicenseAnnotations(serviceClass) || hasMethodLevelAnnotations(serviceClass);
	}

	@Override
	public boolean shouldInterceptMethod(Object target, Method method, Object[] args) {
		// 检查方法或类是否有许可证验证注解
		return method.isAnnotationPresent(LicenseRequired.class) || method.isAnnotationPresent(FeatureRequired.class)
				|| method.isAnnotationPresent(UserLimitCheck.class)
				|| target.getClass().isAnnotationPresent(LicenseRequired.class)
				|| target.getClass().isAnnotationPresent(FeatureRequired.class)
				|| target.getClass().isAnnotationPresent(UserLimitCheck.class);
	}

	/**
	 * 验证基本许可证
	 */
	private ValidationResult validateBasicLicense(Object target, Method method) {
		String pluginId = extractPluginId(target, method);

		Optional<PluginLicense> licenseOpt = licenseManager.getPluginLicense(pluginId);
		if (licenseOpt.isEmpty()) {
			return ValidationResult.failure("未找到插件许可证: " + pluginId);
		}

		return securityValidator.validateLicense(licenseOpt.get());
	}

	/**
	 * 验证功能权限
	 */
	private ValidationResult validateFeaturePermission(Object target, Method method, FeatureRequired annotation) {
		String pluginId = extractPluginId(target, method);

		Optional<PluginLicense> licenseOpt = licenseManager.getPluginLicense(pluginId);
		if (licenseOpt.isEmpty()) {
			return ValidationResult.failure("未找到插件许可证: " + pluginId);
		}

		PluginLicense license = licenseOpt.get();

		// 如果需要基本验证，先进行基本验证
		if (annotation.requireBasicValidation()) {
			ValidationResult basicResult = securityValidator.validateLicense(license);
			if (!basicResult.isSuccess()) {
				return basicResult;
			}
		}

		// 验证功能权限
		String featureName = annotation.value();
		ValidationResult result = securityValidator.validateFeaturePermission(license, featureName);

		// 如果主功能验证失败，尝试别名
		if (!result.isSuccess() && annotation.aliases().length > 0) {
			for (String alias : annotation.aliases()) {
				result = securityValidator.validateFeaturePermission(license, alias);
				if (result.isSuccess()) {
					break;
				}
			}
		}

		return result;
	}

	/**
	 * 验证用户数量限制
	 */
	private ValidationResult validateUserLimit(Object target, Method method, UserLimitCheck annotation, Object[] args) {
		String pluginId = extractPluginId(target, method);

		Optional<PluginLicense> licenseOpt = licenseManager.getPluginLicense(pluginId);
		if (licenseOpt.isEmpty()) {
			return ValidationResult.failure("未找到插件许可证: " + pluginId);
		}

		PluginLicense license = licenseOpt.get();

		// 如果需要基本验证，先进行基本验证
		if (annotation.requireBasicValidation()) {
			ValidationResult basicResult = securityValidator.validateLicense(license);
			if (!basicResult.isSuccess()) {
				return basicResult;
			}
		}

		// 获取当前用户数量
		int currentUserCount = getCurrentUserCount(annotation, args);

		// 验证用户数量限制
		return securityValidator.validateUserLimit(license, currentUserCount);
	}
}
