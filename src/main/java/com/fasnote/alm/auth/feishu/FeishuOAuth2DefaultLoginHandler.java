package com.fasnote.alm.auth.feishu;

import java.io.IOException;
import java.util.Objects;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.fasnote.alm.auth.feishu.utils.FeishuStateUtils;
import com.fasnote.alm.auth.feishu.utils.OAuth2UrlHandler;
import com.polarion.core.util.logging.Logger;
import com.polarion.platform.internal.security.auth.oauth2.OAuth2DefaultLoginHandler;
import com.polarion.platform.security.auth.IAuthenticationContext;
import com.polarion.platform.security.auth.impl.SSOPageDispatcher;
import com.polarion.platform.security.auth.impl.oauth2.OAuth2Exception;
import com.polarion.platform.security.auth.impl.oauth2.OAuth2LoginHandler;
import com.polarion.platform.security.auth.impl.oauth2.OAuth2Result;
import com.polarion.platform.security.login.OAuth2;

/**
 * 飞书OAuth2默认登录处理器
 *
 * 实现OAuth2LoginHandler接口，添加飞书特定的重定向逻辑： 1. 支持解析问号后面的直接URL格式 2. 处理SPA路由的正确重定向 3.
 * 确保认证成功后重定向到正确的目标页面
 */
@SuppressWarnings("restriction")
public class FeishuOAuth2DefaultLoginHandler implements OAuth2LoginHandler {

	private static final long serialVersionUID = 1L;

	@NotNull
	private static final Logger log = Logger.getLogger(FeishuOAuth2DefaultLoginHandler.class);

	@NotNull
	private final OAuth2DefaultLoginHandler delegate;

	@Nullable
	private final String targetUrl;

	/**
	 * 默认构造函数
	 */
	public FeishuOAuth2DefaultLoginHandler() {
		this.delegate = new OAuth2DefaultLoginHandler();
		this.targetUrl = null;
	}

	/**
	 * 构造函数
	 *
	 * @param targetUrl 目标重定向URL
	 */
	public FeishuOAuth2DefaultLoginHandler(@Nullable String targetUrl) {
		this.delegate = new OAuth2DefaultLoginHandler();
		this.targetUrl = targetUrl;
	}

	/**
	 * 处理登录结果，包含增强的重定向逻辑
	 */
	@Override
	public boolean handleLogin(@NotNull OAuth2Result result, @NotNull HttpServletRequest request,
			@NotNull HttpServletResponse response, @Nullable IAuthenticationContext context) throws IOException {

		if (result.error != null) {
			log.error(
					String.format("Error during Feishu authentication: %s, %s", result.error, result.errorDescription));
			throw new OAuth2Exception(result.error);
		} else if (context == null) {
			log.info("Internal authentication error");
			response.sendError(401, "Authentication failed");
			return false;
		} else if (request.getRequestURI().equals("/polarion/reauth")) {
			// 重新认证请求，委托给原始处理器
			return delegate.handleLogin(result, request, response, context);
		} else {
			try {
				// 执行认证
				context.getRealm().authenticateSSO(
						OAuth2.id(context.getAuthenticatorId()),
						OAuth2.token(Objects.requireNonNull(result.accessToken), result.idToken));
			} catch (Exception ex) {
				log.error("Error during Feishu authentication", ex);
				SSOPageDispatcher dispatcher = new SSOPageDispatcher(request, response, context);
				dispatcher.handleAuthenticationException(ex);
				return false;
			}

			// 处理重定向URL
			String redirectUrl = getFeishuRedirectUrl(request, result.originalRequestedUrl);
			log.info("Feishu login successful, redirecting to: " + redirectUrl);
			response.sendRedirect(response.encodeRedirectURL(redirectUrl));
			return false;
		}
	}

	/**
	 * 获取飞书特定的重定向URL，使用统一的 OAuth2UrlHandler
	 */
	@NotNull
	private String getFeishuRedirectUrl(@NotNull HttpServletRequest request, @Nullable String originalRequestedUrl) {

		// 添加详细的调试信息
		String state = request.getParameter("state");
		log.debug("State parameter: " + state);
		log.debug("Original requested URL: " + originalRequestedUrl);

		// 首先尝试从state参数中提取目标URL
		String stateTargetUrl = extractTargetUrlFromState(request);
		log.debug("Extracted target URL from state: " + stateTargetUrl);
		if (stateTargetUrl != null && !stateTargetUrl.isEmpty()) {
			try {
				// 解码并验证目标URL
				String decodedTargetUrl = java.net.URLDecoder.decode(stateTargetUrl, "UTF-8");
				log.debug("Decoded target URL from state: " + decodedTargetUrl);

				if (OAuth2UrlHandler.isValidPolarionUrl(decodedTargetUrl)) {
					String processedUrl = OAuth2UrlHandler.processSpaRoute(decodedTargetUrl);
					log.info("Using target URL from state: " + stateTargetUrl + " -> " + processedUrl);
					return processedUrl;
				} else {
					log.warn("Invalid target URL from state: " + decodedTargetUrl);
				}
			} catch (Exception e) {
				log.warn("Failed to process target URL from state: " + stateTargetUrl, e);
			}
		} else {
			log.debug("No target URL found in state parameter");
		}

		// 如果state中没有有效的目标URL，尝试其他来源
		String[] urlSources = { originalRequestedUrl, targetUrl, extractTargetUrlFromRequest(request) };
		String[] sourceNames = { "original requested URL", "constructor", "request" };

		for (int i = 0; i < urlSources.length; i++) {
			String url = urlSources[i];
			if (url != null && !url.isEmpty()) {
				try {
					String decodedUrl = java.net.URLDecoder.decode(url, "UTF-8");
					if (OAuth2UrlHandler.isValidPolarionUrl(decodedUrl)) {
						String processedUrl = OAuth2UrlHandler.processSpaRoute(decodedUrl);
						log.debug("Using target URL from " + sourceNames[i] + ": " + url + " -> " + processedUrl);
						return processedUrl;
					} else {
						log.warn("Invalid target URL from " + sourceNames[i] + ": " + decodedUrl);
					}
				} catch (Exception e) {
					log.warn("Failed to process target URL from " + sourceNames[i] + ": " + url, e);
				}
			}
		}

		// 默认重定向到Polarion首页
		log.debug("No valid redirect URL found, using default: /polarion/");
		return "/polarion/";
	}

	/**
	 * 从请求中提取目标URL，使用统一的 OAuth2UrlHandler
	 */
	@Nullable
	private String extractTargetUrlFromRequest(@NotNull HttpServletRequest request) {
		return OAuth2UrlHandler.extractTargetUrlFromSsoRequest(request);
	}

	/**
	 * 从state参数中提取目标URL
	 */
	@Nullable
	private String extractTargetUrlFromState(@NotNull HttpServletRequest request) {
		String state = request.getParameter("state");
		return FeishuStateUtils.extractTargetUrlFromState(state);
	}

}
