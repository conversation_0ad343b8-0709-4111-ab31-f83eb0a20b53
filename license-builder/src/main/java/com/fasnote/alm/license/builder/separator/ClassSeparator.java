package com.fasnote.alm.license.builder.separator;

import com.fasnote.alm.license.builder.analyzer.ClassAnalyzer;
import com.fasnote.alm.license.builder.analyzer.InterfaceAnalyzer;
import com.fasnote.alm.license.builder.config.SeparationConfig;
import com.fasnote.alm.license.builder.core.BuildConfiguration;
import com.fasnote.alm.license.builder.exception.SeparationException;
import com.fasnote.alm.license.builder.model.BundleInfo;
import com.fasnote.alm.license.builder.model.ClassInfo;
import com.fasnote.alm.license.builder.model.SeparationResult;
import com.fasnote.alm.license.builder.util.ObfuscationMappingReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Path;
import java.util.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.util.stream.Collectors;

/**
 * 类文件分离器
 * 负责将插件类分离为接口插件和许可证实现
 */
public class ClassSeparator {
    
    private static final Logger logger = LoggerFactory.getLogger(ClassSeparator.class);
    
    private final BuildConfiguration config;
    private final ClassAnalyzer classAnalyzer;
    private final InterfaceAnalyzer interfaceAnalyzer;
    private BundleInfo bundleInfo; // 添加Bundle信息
    
    public ClassSeparator(BuildConfiguration config) {
        this.config = config;
        this.classAnalyzer = new ClassAnalyzer(config);
        this.interfaceAnalyzer = new InterfaceAnalyzer(config);
    }
    
    /**
     * 设置Bundle信息
     */
    public void setBundleInfo(BundleInfo bundleInfo) {
        this.bundleInfo = bundleInfo;
    }
    
    /**
     * 执行类分离
     *
     * @param sourceJarPath 源JAR文件路径
     * @return 分离结果
     * @throws SeparationException 分离异常
     */
    public SeparationResult separate(Path sourceJarPath) throws SeparationException {
        logger.info("ClassSeparator.separate() 开始执行类分离: {}", sourceJarPath.getFileName());

        try {
            // 1. 读取混淆映射文件（如果存在）
            Path mappingFile = ObfuscationMappingReader.findMappingFile(sourceJarPath.getParent());
            Map<String, String> obfuscationMapping = new HashMap<>();
            if (mappingFile != null) {
                obfuscationMapping = ObfuscationMappingReader.readProGuardMapping(mappingFile);
                logger.info("使用混淆映射文件: {}, 共 {} 个类映射", mappingFile, obfuscationMapping.size());
            } else {
                logger.info("未找到混淆映射文件，将基于原始类名进行分离");
            }

            // 2. 分析JAR文件中的所有类
            Map<String, ClassInfo> allClasses = analyzeAllClasses(sourceJarPath);

            // 3. 根据策略确定需要分离的类（基于原始类名）
            Set<String> licensedClasses = determineLicensedClasses(allClasses);

            // 4. 执行分离（考虑混淆映射）
            SeparationResult result = performSeparation(sourceJarPath, allClasses, licensedClasses, obfuscationMapping);

            logger.info("类分离完成 - 接口类: {}, 许可证类: {}, 资源文件: {}",
                       result.getInterfaceClasses().size(),
                       result.getLicensedClasses().size(),
                   result.getResources().size());

        // 调试：输出资源文件列表
        if (!result.getResources().isEmpty()) {
            logger.debug("资源文件列表:");
            for (String resourceName : result.getResources().keySet()) {
                logger.debug("  - {}", resourceName);
            }
        }

            return result;

        } catch (Exception e) {
            throw new SeparationException("类分离失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 分析JAR文件中的所有类
     */
    private Map<String, ClassInfo> analyzeAllClasses(Path jarPath) throws IOException {
        Map<String, ClassInfo> classInfoMap = new HashMap<>();
        
        try (JarFile jarFile = new JarFile(jarPath.toFile())) {
            Enumeration<JarEntry> entries = jarFile.entries();
            
            while (entries.hasMoreElements()) {
                JarEntry entry = entries.nextElement();
                
                if (entry.getName().endsWith(".class") && !entry.isDirectory()) {
                    String className = entryNameToClassName(entry.getName());
                    
                    try {
                        byte[] classBytes = jarFile.getInputStream(entry).readAllBytes();
                        ClassInfo classInfo = classAnalyzer.analyzeClass(className, classBytes);
                        classInfoMap.put(className, classInfo);
                        
                    } catch (Exception e) {
                        logger.warn("分析类失败: {} - {}", className, e.getMessage());
                    }
                }
            }
        }
        
        logger.debug("分析完成，共发现 {} 个类", classInfoMap.size());
        return classInfoMap;
    }
    
    /**
     * 根据策略确定需要分离到许可证的类
     */
    private Set<String> determineLicensedClasses(Map<String, ClassInfo> allClasses) {
        Set<String> licensedClasses = new HashSet<>();
        SeparationConfig.Strategy strategy = config.getSeparation().getStrategy();

        logger.info("确定许可证类开始，使用分离策略: {}", strategy);
        
        switch (strategy) {
            case AUTO:
                licensedClasses.addAll(autoDetectLicensedClasses(allClasses));
                break;
                
            case MANUAL:
                licensedClasses.addAll(manuallySpecifiedClasses());
                break;
                
            case HYBRID:
                licensedClasses.addAll(autoDetectLicensedClasses(allClasses));
                licensedClasses.addAll(manuallySpecifiedClasses());
                break;
        }
        
        // 移除排除的类
        licensedClasses.removeAll(getExcludedClasses(allClasses.keySet()));
        
        logger.info("确定需要分离的类: {} 个", licensedClasses.size());
        return licensedClasses;
    }
    
    /**
     * 自动检测需要许可证的类
     */
    private Set<String> autoDetectLicensedClasses(Map<String, ClassInfo> allClasses) {
        Set<String> licensedClasses = new HashSet<>();

        logger.info("自动检测许可证类开始，isAutoDetectLicenseImplementations: {}",
            config.getAnalysis().isAutoDetectLicenseImplementations());

        if (!config.getAnalysis().isAutoDetectLicenseImplementations()) {
            logger.info("自动检测被禁用，返回空列表");
            return licensedClasses;
        }
        
        for (ClassInfo classInfo : allClasses.values()) {
            // 检查是否有许可证注解
            if (hasLicenseAnnotation(classInfo)) {
                licensedClasses.add(classInfo.getClassName());
                logger.debug("通过注解检测到许可证类: {}", classInfo.getClassName());
            }
            
            // 检查是否实现了许可证接口
            if (implementsLicenseInterface(classInfo)) {
                licensedClasses.add(classInfo.getClassName());
                logger.debug("通过接口检测到许可证类: {}", classInfo.getClassName());
            }
        }
        
        // 添加依赖的类
        logger.info("查找依赖类，当前许可证类数量: {}", licensedClasses.size());
        Set<String> dependentClasses = findDependentClasses(allClasses, licensedClasses);
        logger.info("找到依赖类数量: {}", dependentClasses.size());
        for (String depClass : dependentClasses) {
            logger.info("依赖类: {}", depClass);
        }
        licensedClasses.addAll(dependentClasses);
        
        return licensedClasses;
    }
    
    /**
     * 手动指定的类
     */
    private Set<String> manuallySpecifiedClasses() {
        Set<String> licensedClasses = new HashSet<>();
        
        // 添加指定的包中的所有类
        licensedClasses.addAll(config.getSeparation().getLicensedPackages().stream()
            .map(this::expandPackagePattern)
            .flatMap(Set::stream)
            .collect(Collectors.toSet()));
        
        // 添加指定的具体类
        licensedClasses.addAll(config.getSeparation().getLicensedClasses());
        
        return licensedClasses;
    }
    
    /**
     * 获取排除的类
     */
    private Set<String> getExcludedClasses(Set<String> allClassNames) {
        Set<String> excludedClasses = new HashSet<>();
        
        // 添加排除的包中的所有类
        for (String excludedPackage : config.getSeparation().getExcludedPackages()) {
            excludedClasses.addAll(allClassNames.stream()
                .filter(className -> className.startsWith(excludedPackage))
                .collect(Collectors.toSet()));
        }
        
        // 添加排除的具体类
        for (String excludedClass : config.getSeparation().getExcludedClasses()) {
            if (excludedClass.contains("*")) {
                // 处理通配符模式
                String pattern = excludedClass.replace("*", ".*");
                excludedClasses.addAll(allClassNames.stream()
                    .filter(className -> className.matches(pattern))
                    .collect(Collectors.toSet()));
            } else {
                excludedClasses.add(excludedClass);
            }
        }
        
        return excludedClasses;
    }
    
    /**
     * 检查类是否有许可证注解
     */
    private boolean hasLicenseAnnotation(ClassInfo classInfo) {
        for (String licenseAnnotation : config.getAnalysis().getLicenseAnnotations()) {
            if (classInfo.getAnnotations().contains(licenseAnnotation)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查类是否实现了许可证接口
     */
    private boolean implementsLicenseInterface(ClassInfo classInfo) {
        for (String licenseInterface : config.getAnalysis().getLicenseInterfaces()) {
            if (classInfo.getInterfaces().contains(licenseInterface) ||
                classInfo.getSuperClasses().contains(licenseInterface)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 查找依赖的类
     */
    private Set<String> findDependentClasses(Map<String, ClassInfo> allClasses, Set<String> licensedClasses) {
        Set<String> dependentClasses = new HashSet<>();

        // 简化的依赖分析 - 实际实现中需要更复杂的字节码分析
        for (String licensedClass : licensedClasses) {
            ClassInfo classInfo = allClasses.get(licensedClass);
            if (classInfo != null) {
                // 添加内部类 - 但只有当外部类也是许可证类时
                for (String innerClass : classInfo.getInnerClasses()) {
                    // 检查内部类的外部类是否也在许可证类列表中
                    String outerClassName = getOuterClassName(innerClass);
                    if (outerClassName != null && licensedClasses.contains(outerClassName)) {
                        dependentClasses.add(innerClass);
                        logger.debug("添加内部类作为依赖: {} (外部类: {})", innerClass, outerClassName);
                    } else {
                        logger.debug("跳过内部类分离: {} (外部类 {} 不是许可证类)", innerClass, outerClassName);
                    }
                }

                // 添加同包的非公共类（可能是帮助类）- 但排除内部类和匿名类
                String packageName = getPackageName(licensedClass);
                dependentClasses.addAll(allClasses.keySet().stream()
                    .filter(className -> getPackageName(className).equals(packageName))
                    .filter(className -> {
                        ClassInfo info = allClasses.get(className);
                        logger.info("检查同包类: {} - isPublic: {}, isInterface: {}, isInnerOrAnonymous: {}",
                            className, info != null ? info.isPublic() : "null",
                            info != null ? info.isInterface() : "null",
                            isInnerOrAnonymousClass(className));

                        // 排除内部类和匿名类
                        if (isInnerOrAnonymousClass(className)) {
                            logger.info("跳过内部类/匿名类: {}", className);
                            return false;
                        }
                        boolean shouldInclude = info != null && !info.isPublic() && !info.isInterface();
                        if (shouldInclude) {
                            logger.info("添加同包非公共类: {}", className);
                        }
                        return shouldInclude;
                    })
                    .collect(Collectors.toSet()));
            }
        }

        return dependentClasses;
    }
    
    /**
     * 执行实际的分离操作
     *
     * 新的分离逻辑：
     * - 接口 JAR = 完整的原始 JAR - 仅许可证注解标记的类
     * - 许可证文件 = 仅包含许可证注解标记的类
     * - 所有资源文件都保留在接口 JAR 中
     * - 支持混淆映射：基于原始类名确定分离策略，但使用混淆后的条目名
     */
    private SeparationResult performSeparation(Path sourceJarPath,
                                             Map<String, ClassInfo> allClasses,
                                             Set<String> licensedClasses,
                                             Map<String, String> obfuscationMapping) throws IOException {

        Map<String, byte[]> interfaceContent = new HashMap<>();  // 接口 JAR 的完整内容
        Map<String, byte[]> licensedClassBytes = new HashMap<>(); // 许可证文件中的类
        Map<String, byte[]> resources = new HashMap<>();         // 资源文件（将全部放入接口 JAR）

        // 构建许可证类的混淆条目名映射
        Set<String> licensedObfuscatedEntries = new HashSet<>();
        for (String originalClassName : licensedClasses) {
            String obfuscatedEntryName = ObfuscationMappingReader.getObfuscatedEntryName(originalClassName, obfuscationMapping);
            licensedObfuscatedEntries.add(obfuscatedEntryName);
            logger.debug("许可证类映射: {} -> {}", originalClassName, obfuscatedEntryName);
        }

        try (JarFile jarFile = new JarFile(sourceJarPath.toFile())) {
            Enumeration<JarEntry> entries = jarFile.entries();

            while (entries.hasMoreElements()) {
                JarEntry entry = entries.nextElement();

                if (entry.isDirectory()) {
                    continue;
                }

                byte[] entryBytes = jarFile.getInputStream(entry).readAllBytes();
                String entryName = entry.getName();

                if (entryName.endsWith(".class")) {
                    // 将条目名转换为类名进行检查
                    String className = entryNameToClassName(entryName);



                    // 检查是否是内部类或匿名类 - 内部类应该和外部类保持在一起
                    if (isInnerOrAnonymousClass(className)) {
                        // 内部类始终保留在接口 JAR 中，不分离
                        interfaceContent.put(entryName, entryBytes);
                        logger.info("内部类保留在接口JAR: {}", entryName);
                    } else if (licensedObfuscatedEntries.contains(entryName)) {
                        // 许可证注解标记的类：放入许可证文件，不放入接口 JAR
                        licensedClassBytes.put(entryName, entryBytes);
                        logger.debug("许可证类分离: {}", entryName);
                    } else {
                        // 所有其他类：放入接口 JAR
                        interfaceContent.put(entryName, entryBytes);
                        logger.debug("接口类保留: {}", entryName);
                    }
                } else {
                    // 所有资源文件都保留在接口 JAR 中
                    interfaceContent.put(entryName, entryBytes);
                    resources.put(entryName, entryBytes); // 同时放入 resources 以保持兼容性
                    logger.debug("资源文件保留在接口 JAR: {}", entryName);
                }
            }
        }

        // 返回结果：interfaceContent 包含完整的接口 JAR 内容
        return new SeparationResult(interfaceContent, licensedClassBytes, resources);
    }
    
    /**
     * 判断资源文件是否应该分离
     */
    private boolean shouldSeparateResource(String resourcePath) {
        if (!config.getSeparation().getResources().isEnabled()) {
            return false;
        }
        
        // 检查排除模式
        for (String excludePattern : config.getSeparation().getResources().getExcludePatterns()) {
            if (matchesPattern(resourcePath, excludePattern)) {
                return false;
            }
        }
        
        // 检查包含模式
        for (String includePattern : config.getSeparation().getResources().getPatterns()) {
            if (matchesPattern(resourcePath, includePattern)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 模式匹配
     */
    private boolean matchesPattern(String path, String pattern) {
        // 简化的模式匹配，实际可以使用更复杂的glob模式
        String regex = pattern.replace("**", ".*").replace("*", "[^/]*");
        return path.matches(regex);
    }
    
    /**
     * 展开包模式
     */
    private Set<String> expandPackagePattern(String packagePattern) {
        // 这里需要实现根据包模式查找所有匹配的类
        // 简化实现，实际需要扫描类路径
        return new HashSet<>();
    }
    
    /**
     * JAR条目名称转换为类名
     */
    private String entryNameToClassName(String entryName) {
        return entryName.replace('/', '.').replace(".class", "");
    }
    
    /**
     * 获取包名
     */
    private String getPackageName(String className) {
        int lastDotIndex = className.lastIndexOf('.');
        return lastDotIndex > 0 ? className.substring(0, lastDotIndex) : "";
    }

    /**
     * 获取内部类的外部类名
     *
     * @param innerClassName 内部类名（如 com.example.Outer$Inner）
     * @return 外部类名（如 com.example.Outer），如果不是内部类则返回null
     */
    private String getOuterClassName(String innerClassName) {
        int dollarIndex = innerClassName.lastIndexOf('$');
        if (dollarIndex > 0) {
            return innerClassName.substring(0, dollarIndex);
        }
        return null;
    }

    /**
     * 检查类是否是内部类或匿名类
     *
     * @param className 类名
     * @return 如果是内部类或匿名类则返回true
     */
    private boolean isInnerOrAnonymousClass(String className) {
        // 包含 $ 符号的类通常是内部类或匿名类
        return className.contains("$");
    }
    
    /**
     * 检查类是否在Export-Package中
     */
    private boolean isInExportedPackage(String className) {
        if (bundleInfo == null || bundleInfo.getExportedPackages() == null) {
            return false;
        }
        
        String packageName = getPackageName(className);
        
        for (String exportedPackage : bundleInfo.getExportedPackages()) {
            // Extract package name from export declaration (remove version info)
            String exportPackageName = extractPackageName(exportedPackage);
            if (packageName.equals(exportPackageName) || packageName.startsWith(exportPackageName + ".")) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 从Export-Package声明中提取包名
     */
    private String extractPackageName(String exportDeclaration) {
        // Export-Package格式: com.example.api;version="1.0.0"
        int semicolonIndex = exportDeclaration.indexOf(';');
        if (semicolonIndex > 0) {
            return exportDeclaration.substring(0, semicolonIndex).trim();
        }
        return exportDeclaration.trim();
    }
}