package com.fasnote.alm.auth.feishu;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.List;
import java.util.Map;

import org.apache.hivemind.InterceptorStack;
import org.apache.hivemind.ServiceInterceptorFactory;
import org.apache.hivemind.internal.Module;
import org.jetbrains.annotations.NotNull;

import com.polarion.core.util.logging.Logger;
import com.polarion.platform.security.auth.IAuthenticator;

/**
 * 飞书认证器管理器拦截器工厂
 *
 * 这个ServiceInterceptorFactory用于拦截IAuthenticatorManager服务，
 * 将OAuth2认证器替换为增强版本，以支持飞书认证。
 *
 * 主要功能： 1. 拦截IAuthenticatorManager.getAuthenticators()方法 2. 将"oauth2"认证器替换为增强版本
 * 3. 增强版本会根据配置ID判断是否使用飞书逻辑 4. 保持其他认证器不变
 *
 * 工作原理： - 通过动态代理拦截getAuthenticators()方法调用 - 获取原始认证器映射 -
 * 将"oauth2"键对应的认证器替换为FeishuEnhancedOAuth2Authenticator - 返回增强后的认证器映射
 */
public class FeishuAuthenticatorManagerInterceptorFactory implements ServiceInterceptorFactory {

	@NotNull
	private static final Logger log = Logger.getLogger(FeishuAuthenticatorManagerInterceptorFactory.class);

	/**
	 * 创建拦截器
	 * 
	 * @param stack          拦截器堆栈
	 * @param invokingModule 调用模块
	 * @param parameters     参数列表
	 */
	@Override
	public void createInterceptor(InterceptorStack stack, Module invokingModule, List parameters) {
		log.info("Creating FeishuAuthenticatorManager interceptor to handle feishu-prefixed authenticators");

		// 获取原始服务实例
		Object originalService = stack.peek();

		// 创建代理拦截器
		Object interceptor = createAuthenticatorManagerProxy(originalService, stack);

		// 将拦截器推入堆栈
		stack.push(interceptor);

		log.info("FeishuAuthenticatorManager interceptor created successfully");
	}

	/**
	 * 创建认证器管理器代理
	 * 
	 * @param originalService 原始服务实例
	 * @param stack           拦截器堆栈
	 * @return 代理对象
	 */
	private Object createAuthenticatorManagerProxy(Object originalService, InterceptorStack stack) {
		Class<?> serviceInterface = stack.getServiceInterface();
		ClassLoader classLoader = originalService.getClass().getClassLoader();

		InvocationHandler handler = new FeishuAuthenticatorManagerInvocationHandler(originalService);

		return Proxy.newProxyInstance(classLoader, new Class<?>[] { serviceInterface }, handler);
	}

	/**
	 * 飞书认证器管理器调用处理器
	 */
	private static class FeishuAuthenticatorManagerInvocationHandler implements InvocationHandler {

		private final Object originalService;

		public FeishuAuthenticatorManagerInvocationHandler(Object originalService) {
			this.originalService = originalService;
		}

		@Override
		public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
			String methodName = method.getName();

			// 拦截getAuthenticators方法
			if ("getAuthenticators".equals(methodName)) {
				log.debug("Intercepting getAuthenticators() method call for Feishu enhancement");

				// 调用原始方法获取认证器映射
				@SuppressWarnings("unchecked")
				Map<String, IAuthenticator> originalAuthenticators = (Map<String, IAuthenticator>) method
						.invoke(originalService, args);

				// 创建增强版认证器映射
				Map<String, IAuthenticator> enhancedAuthenticators = createEnhancedAuthenticatorMap(
						originalAuthenticators);

				log.debug("Feishu authenticators registered successfully in getAuthenticators() call");
				return enhancedAuthenticators;
			}

			// 对于其他方法，直接调用原始服务
			return method.invoke(originalService, args);
		}

		/**
		 * 创建增强版认证器映射
		 *
		 * @param originalAuthenticators 原始认证器映射
		 * @return 增强版认证器映射
		 */
		private Map<String, IAuthenticator> createEnhancedAuthenticatorMap(
				Map<String, IAuthenticator> originalAuthenticators) {
			
			// 使用DI框架获取认证器增强实现
			IFeishuAuthenticatorEnhancer enhancer = getAuthenticatorEnhancer();
			
			log.debug("Using enhancer: " + enhancer.getEnhancerType());
			
			// 委托给增强器处理
			return enhancer.createEnhancedAuthenticatorMap(originalAuthenticators);
		}

		/**
		 * 获取认证器增强实现
		 * 
		 * manage框架会根据许可证状态自动选择合适的实现：
		 * - 许可证验证通过：返回FeishuLicensedAuthenticatorEnhancer
		 * - 许可证验证失败：返回FeishuDefaultAuthenticatorEnhancer
		 * 
		 * @return 认证器增强实现
		 */
		private IFeishuAuthenticatorEnhancer getAuthenticatorEnhancer() {
			try {
				// 使用manage的DI框架获取注入的实现
				Object enhancerObj = com.fasnote.alm.injection.facade.DI.get(IFeishuAuthenticatorEnhancer.class);
				//IFeishuAuthenticatorEnhancer enhancerObj = new FeishuDefaultAuthenticatorEnhancer();
				if (enhancerObj instanceof IFeishuAuthenticatorEnhancer) {
					return (IFeishuAuthenticatorEnhancer) enhancerObj;
				}
				
				log.warn("DI framework returned unexpected type, falling back to default enhancer");
				
			} catch (Exception e) {
				log.warn("Failed to get enhancer from DI framework, falling back to default", e);
			}
			
			// Fallback：如果DI框架不可用，使用默认实现
			return new FeishuDefaultAuthenticatorEnhancer();
		}
	}
}
