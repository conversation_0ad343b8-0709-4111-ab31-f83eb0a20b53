package com.fasnote.alm.license.builder.injection;

import com.fasnote.alm.license.builder.core.BuildConfiguration;
import com.fasnote.alm.license.builder.model.SeparationResult;
import org.objectweb.asm.*;
import org.objectweb.asm.commons.AdviceAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 基于ASM的服务注入代码转换器
 * 将Services.create()和DI.get()调用替换为LicenseManagementFramework.getService()调用
 */
public class ServiceInjectionTransformer {
    
    private static final Logger logger = LoggerFactory.getLogger(ServiceInjectionTransformer.class);
    
    private final BuildConfiguration config;
    
    public ServiceInjectionTransformer(BuildConfiguration config) {
        this.config = config;
    }
    
    /**
     * 转换分离结果中的类，将Services.create()和DI.get()替换为LicenseManagementFramework.getService()
     */
    public void transformServiceCalls(SeparationResult separationResult) {
        logger.info("开始转换依赖注入调用...");
        
        Map<String, byte[]> interfaceClasses = separationResult.getInterfaceClasses();
        int transformedCount = 0;
        
        for (Map.Entry<String, byte[]> entry : interfaceClasses.entrySet()) {
            String className = entry.getKey();
            byte[] classBytes = entry.getValue();
            
            if (className.endsWith(".class")) {
                try {
                    byte[] transformedBytes = transformClass(classBytes, className);
                    if (transformedBytes != null) {
                        interfaceClasses.put(className, transformedBytes);
                        transformedCount++;
                        logger.debug("转换完成: {}", className);
                    }
                } catch (Exception e) {
                    logger.warn("转换失败: {} - {}", className, e.getMessage());
                }
            }
        }
        
        logger.info("依赖注入调用转换完成，共转换 {} 个类", transformedCount);
    }
    
    /**
     * 使用ASM转换单个类文件的字节码
     */
    private byte[] transformClass(byte[] classBytes, String className) throws Exception {
        ClassReader classReader = new ClassReader(classBytes);
        ClassWriter classWriter = new ClassWriter(ClassWriter.COMPUTE_MAXS);
        
        DITransformVisitor transformVisitor = new DITransformVisitor(classWriter);
        classReader.accept(transformVisitor, 0);
        
        if (transformVisitor.isTransformed()) {
            byte[] transformedBytes = classWriter.toByteArray();
            
            logger.debug("类 {} 中发现依赖注入调用，已替换", className);
            
            // 验证转换后的字节码
            if (BytecodeAnalyzer.validateClass(transformedBytes, className)) {
                // 在调试模式下比较转换前后的差异
                if (logger.isDebugEnabled()) {
                    BytecodeAnalyzer.compareClasses(classBytes, transformedBytes, className);
                }
                return transformedBytes;
            } else {
                logger.error("转换后的字节码验证失败，保持原样: {}", className);
                return null;
            }
        }
        
        return null; // 没有需要转换的内容
    }
    
    /**
     * ASM ClassVisitor - 访问类中的所有方法
     */
    private static class DITransformVisitor extends ClassVisitor {
        private boolean transformed = false;
        
        public DITransformVisitor(ClassVisitor cv) {
            super(Opcodes.ASM9, cv);
        }
        
        @Override
        public MethodVisitor visitMethod(int access, String name, String descriptor, String signature, String[] exceptions) {
            MethodVisitor mv = super.visitMethod(access, name, descriptor, signature, exceptions);
            return new DIMethodVisitor(mv, access, name, descriptor);
        }
        
        public boolean isTransformed() {
            return transformed;
        }
        
        /**
         * ASM MethodVisitor - 负责转换方法中的DI调用
         */
        private class DIMethodVisitor extends AdviceAdapter {
            
            public DIMethodVisitor(MethodVisitor mv, int access, String name, String desc) {
                super(Opcodes.ASM9, mv, access, name, desc);
            }
            
            @Override
            public void visitMethodInsn(int opcode, String owner, String name, String descriptor, boolean isInterface) {
                boolean shouldTransform = false;
                
                // 检查是否为需要转换的DI调用
                if (opcode == Opcodes.INVOKESTATIC && !isInterface) {
                    // Services.create() 调用
                    if ("com/fasnote/alm/injection/facade/Services".equals(owner) && "create".equals(name)) {
                        shouldTransform = true;
                    }
                    // DI.get() 调用
                    else if ("com/fasnote/alm/injection/facade/DI".equals(owner) && "get".equals(name)) {
                        shouldTransform = true;
                    }
                }
                
                if (shouldTransform) {
                    if ("(Ljava/lang/Class;)Ljava/lang/Object;".equals(descriptor)) {
                        // 单参数调用：Services.create(Class) 或 DI.get(Class)
                        replaceSingleParamCall();
                    } else if ("(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Object;".equals(descriptor)) {
                        // 双参数调用：Services.create(Class, String) 或 DI.get(Class, String)
                        replaceDoubleParamCall();
                    } else {
                        // 其他形式的调用保持不变
                        super.visitMethodInsn(opcode, owner, name, descriptor, isInterface);
                    }
                } else {
                    // 非DI调用，保持不变
                    super.visitMethodInsn(opcode, owner, name, descriptor, isInterface);
                }
            }
            
            /**
             * 替换单参数DI调用
             * Services.create(MyService.class) | DI.get(MyService.class) 
             * -> LicenseManagementFramework.getInstance().getService(MyService.class)
             */
            private void replaceSingleParamCall() {
                transformed = true;
                
                // 调用 LicenseManagementFramework.getInstance()
                super.visitMethodInsn(
                    Opcodes.INVOKESTATIC,
                    "com/fasnote/alm/plugin/manage/LicenseManagementFramework",
                    "getInstance",
                    "()Lcom/fasnote/alm/plugin/manage/LicenseManagementFramework;",
                    false
                );
                
                // 交换栈顶的两个元素：[Class, Framework] -> [Framework, Class]
                super.visitInsn(Opcodes.SWAP);
                
                // 调用 framework.getService(Class)
                super.visitMethodInsn(
                    Opcodes.INVOKEVIRTUAL,
                    "com/fasnote/alm/plugin/manage/LicenseManagementFramework",
                    "getService",
                    "(Ljava/lang/Class;)Ljava/lang/Object;",
                    false
                );
                
                logger.debug("替换单参数DI调用");
            }
            
            /**
             * 替换双参数DI调用
             * Services.create(MyService.class, "name") | DI.get(MyService.class, "name")
             * -> LicenseManagementFramework.getInstance().getService(MyService.class, "name")
             */
            private void replaceDoubleParamCall() {
                transformed = true;
                
                // 调用 LicenseManagementFramework.getInstance()
                super.visitMethodInsn(
                    Opcodes.INVOKESTATIC,
                    "com/fasnote/alm/plugin/manage/LicenseManagementFramework",
                    "getInstance",
                    "()Lcom/fasnote/alm/plugin/manage/LicenseManagementFramework;",
                    false
                );
                
                // 栈操作：[Class, String, Framework] -> [Framework, Class, String]
                // 使用DUP_X2将Framework复制到栈底，然后POP掉顶部的Framework
                super.visitInsn(Opcodes.DUP_X2);
                super.visitInsn(Opcodes.POP);
                
                // 调用 framework.getService(Class, String)
                super.visitMethodInsn(
                    Opcodes.INVOKEVIRTUAL,
                    "com/fasnote/alm/plugin/manage/LicenseManagementFramework",
                    "getService",
                    "(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Object;",
                    false
                );
                
                logger.debug("替换双参数DI调用");
            }
        }
    }
}