# 模板系统使用指南

## 概述

TychoConfigGenerator 现在支持基于模板的配置文件生成，提供了更好的可维护性和灵活性。

## 模板语法

### 1. 变量替换
```
${variable_name}
```
示例：
```xml
<artifactId>${bundle_symbolic_name}</artifactId>
<version>${maven_version}</version>
```

### 2. 条件渲染
```
{{#if condition}}
  内容
{{/if}}
```

带else分支：
```
{{#if condition}}
  条件为真时的内容
{{else}}
  条件为假时的内容
{{/if}}
```

### 3. 条件等值判断
```
{{#if_equals variable "value"}}
  内容
{{/if_equals}}
```

### 4. 循环渲染
```
{{#each collection_name}}
  ${item_property}
{{/each}}
```

### 5. 模板包含
```
{{>template_path}}
```

## 模板文件结构

```
templates/
├── pom/                    # POM文件相关模板
│   ├── complete-pom.xml   # 完整POM模板
│   ├── pom-properties.xml # POM属性配置
│   └── pom-build.xml      # 构建配置
├── tycho/                 # Tycho相关模板
│   ├── tycho-plugins.xml  # Tycho插件配置
│   └── target-platform.xml # 目标平台配置
├── obfuscation/           # 混淆工具模板
│   ├── proguard-plugin.xml
│   ├── proguard-rules.xml
│   ├── yguard-plugin.xml
│   └── yguard-rules.xml
├── license/               # 许可证处理模板
│   ├── license-processing.xml
│   └── file-copy-plugin.xml
└── config/                # 配置文件模板
    ├── bundle-info.yaml
    └── build-properties.txt
```

## 可用变量

### Bundle信息
- `bundle_symbolic_name` - Bundle符号名称
- `bundle_version` - Bundle版本
- `bundle_name` - Bundle名称
- `bundle_vendor` - Bundle供应商
- `bundle_activator` - Bundle激活器类
- `exported_packages` - 导出包列表（数组）
- `required_bundles` - 必需Bundle列表（数组）

### 构建配置
- `maven_version` - Maven版本
- `final_name` - 最终文件名
- `tycho_version` - Tycho版本
- `java_version` - Java版本

### 混淆配置
- `enable_obfuscation` - 是否启用混淆
- `obfuscation_tool` - 混淆工具（PROGUARD/YGUARD）
- `obfuscation_level` - 混淆级别（LIGHT/STANDARD/AGGRESSIVE）

### 许可证配置
- `license_validity` - 许可证有效期
- `license_builder_jar` - License Builder JAR路径
- `license_builder_config` - License Builder配置路径
- `machine_binding_enabled` - 是否启用机器绑定
- `machine_id` - 机器ID

## 系统属性配置

可以通过系统属性控制构建行为：

```bash
# 混淆配置
-Dskip.obfuscation=false
-Dobfuscation.tool=YGUARD
-Dobfuscation.level=STANDARD

# 许可证配置
-Dlicense.validity=DAYS_90

# 机器绑定配置
-Dmachine.binding.enabled=false
-Dmachine.id=your-machine-id
```

## 模板路径查找顺序

模板引擎会按以下顺序查找模板文件：

1. `./templates/`
2. `../templates/`
3. `../../templates/`
4. `{pluginDir}/../templates/`
5. `../com.fasnote.alm.license.builder/templates/`
6. `{currentDir}/../com.fasnote.alm.license.builder/templates/`
7. `{currentDir}/templates/`



## 自定义模板

您可以通过修改模板文件来自定义生成的配置：

1. 复制现有模板文件
2. 根据需要修改模板内容
3. 确保模板文件位于正确的路径下
4. 重新运行构建过程

## 调试

启用调试日志可以查看模板加载和渲染过程：

```bash
-Dlogging.level.com.fasnote.alm.license.builder.template=DEBUG
```
