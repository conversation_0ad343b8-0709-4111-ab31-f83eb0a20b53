            <!-- 文件复制插件 - 复制最终结果到输出目录 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>copy-license-files</id>
                        <phase>install</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.basedir}/../output</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${project.build.directory}/license-output</directory>
                                    <includes>
                                        <include>*.jar</include>
                                        <include>*.lic</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
