package com.fasnote.alm.license.builder.exception;

/**
 * JAR签名异常
 * 在JAR文件数字签名过程中发生错误时抛出
 */
public class SigningException extends Exception {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     */
    public SigningException(String message) {
        super(message);
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param cause 原因异常
     */
    public SigningException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param cause 原因异常
     */
    public SigningException(Throwable cause) {
        super(cause);
    }
}