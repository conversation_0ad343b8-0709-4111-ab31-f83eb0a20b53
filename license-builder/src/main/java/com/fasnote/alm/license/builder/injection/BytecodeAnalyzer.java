package com.fasnote.alm.license.builder.injection;

import org.objectweb.asm.ClassReader;
import org.objectweb.asm.ClassVisitor;
import org.objectweb.asm.ClassWriter;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.util.ASMifier;
import org.objectweb.asm.util.TraceClassVisitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * ASM字节码分析工具
 * 用于调试和验证字节码转换
 */
public class BytecodeAnalyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(BytecodeAnalyzer.class);
    
    /**
     * 分析字节码并打印ASM代码
     */
    public static void analyzeClass(byte[] classBytes, String className) {
        logger.info("开始分析类: {}", className);
        
        try {
            ClassReader classReader = new ClassReader(classBytes);
            StringWriter stringWriter = new StringWriter();
            PrintWriter printWriter = new PrintWriter(stringWriter);
            
            // 使用ASMifier生成对应的ASM代码
            TraceClassVisitor traceVisitor = new TraceClassVisitor(null, new ASMifier(), printWriter);
            classReader.accept(traceVisitor, 0);
            
            String asmCode = stringWriter.toString();
            logger.debug("类 {} 的ASM代码:\n{}", className, asmCode);
            
            // 检查是否包含ServiceFactory调用
            if (asmCode.contains("ServiceFactory")) {
                logger.info("类 {} 包含ServiceFactory调用", className);
            }
            
            if (asmCode.contains("LicenseManagementFramework")) {
                logger.info("类 {} 包含LicenseManagementFramework调用", className);
            }
            
        } catch (Exception e) {
            logger.error("分析类失败: " + className, e);
        }
    }
    
    /**
     * 比较转换前后的字节码差异
     */
    public static void compareClasses(byte[] originalBytes, byte[] transformedBytes, String className) {
        logger.info("比较类转换前后的差异: {}", className);
        
        try {
            // 分析原始字节码
            logger.debug("=== 转换前 ===");
            analyzeClass(originalBytes, className + "_ORIGINAL");
            
            // 分析转换后字节码
            logger.debug("=== 转换后 ===");
            analyzeClass(transformedBytes, className + "_TRANSFORMED");
            
        } catch (Exception e) {
            logger.error("比较类差异失败: " + className, e);
        }
    }
    
    /**
     * 验证字节码的有效性
     */
    public static boolean validateClass(byte[] classBytes, String className) {
        try {
            ClassReader classReader = new ClassReader(classBytes);
            
            // 使用CheckClassAdapter验证字节码
            ClassWriter classWriter = new ClassWriter(0);
            ClassVisitor checkVisitor = new ClassVisitor(Opcodes.ASM9, classWriter) {};
            
            classReader.accept(checkVisitor, 0);
            
            logger.debug("类 {} 字节码验证通过", className);
            return true;
            
        } catch (Exception e) {
            logger.error("类 {} 字节码验证失败: {}", className, e.getMessage());
            return false;
        }
    }
}