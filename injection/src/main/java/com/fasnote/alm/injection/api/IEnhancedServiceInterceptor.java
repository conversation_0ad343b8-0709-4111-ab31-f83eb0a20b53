package com.fasnote.alm.injection.api;

/**
 * 增强的服务拦截器接口
 *
 * 扩展IServiceInterceptor，同时支持方法级别的拦截
 * 保持与现有OSGi架构的兼容性
 */
public interface IEnhancedServiceInterceptor extends IServiceInterceptor, IMethodInterceptor {

    /**
     * 是否需要为此服务创建方法拦截代理
     *
     * @param serviceClass 服务类
     * @param instance 服务实例
     * @return true表示需要创建代理
     */
    default boolean needsMethodInterception(Class<?> serviceClass, Object instance) {
        return false;
    }

    /**
     * 获取拦截器名称（用于调试和日志）
     *
     * @return 拦截器名称
     */
    default String getInterceptorName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 统一的拦截器优先级
     * 服务级别和方法级别使用相同的优先级
     *
     * @return 拦截器优先级
     */
    @Override
    default int getPriority() {
        return 100;
    }

    /**
     * 方法拦截器优先级（委托给统一优先级）
     *
     * @return 方法拦截优先级
     */
    @Override
    default int getMethodInterceptorPriority() {
        return getPriority();
    }
}
