package com.fasnote.alm.plugin.manage.bootstrap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.config.LicenseConfiguration;
import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;

/**
 * 许可证框架启动器 职责：框架初始化和生命周期管理
 *
 * 功能： - 组件启动顺序控制 - 资源管理和清理 - 单例模式管理 - OSGi集成初始化
 */
public class LicenseFrameworkBootstrap {

	private static final Logger logger = LoggerFactory.getLogger(LicenseFrameworkBootstrap.class);

	// 核心组件
	private final LicenseManager licenseManager;
	private final LicenseConfiguration configuration;
	private final SecurityValidator securityValidator;

	private volatile boolean initialized = false;

	/**
	 * 构造函数（依赖注入）
	 *
	 * @param licenseManager 许可证管理器
	 */
	public LicenseFrameworkBootstrap(LicenseManager licenseManager) {
		if (licenseManager == null) {
			throw new IllegalArgumentException("LicenseManager不能为null");
		}
		this.configuration = LicenseConfiguration.getInstance();
		this.licenseManager = licenseManager;
		this.securityValidator = new SecurityValidator();
	}

	/**
	 * 获取配置管理器
	 */
	public LicenseConfiguration getConfiguration() {
		return configuration;
	}

	/**
	 * 获取许可证管理器
	 */
	public LicenseManager getLicenseManager() {
		return licenseManager;
	}

	/**
	 * 获取安全验证器
	 */
	public SecurityValidator getSecurityValidator() {
		return securityValidator;
	}

	/**
	 * 初始化框架
	 */
	public void initialize() {
		if (initialized) {
			logger.warn("框架已经初始化，跳过重复初始化");
			return;
		}

		logger.info("开始初始化许可证管理框架");

		try {
			// 1. 加载配置
			logger.info("开始加载配置...");
			configuration.loadConfiguration();
			logger.info("✓ 配置加载完成");

			// 2. 初始化安全验证器
			logger.info("开始初始化安全验证器...");
			securityValidator.initialize();
			logger.info("✓ 安全验证器初始化完成");

			// 3. 初始化完成
			logger.info("✓ 核心组件初始化完成");

			initialized = true;
			logger.info("许可证管理框架初始化完成");

		} catch (Exception e) {
			logger.error("许可证管理框架初始化失败", e);
			throw new RuntimeException("许可证管理框架初始化失败", e);
		}
	}

	/**
	 * 检查是否已初始化
	 */
	public boolean isInitialized() {
		return initialized;
	}

	/**
	 * 关闭框架
	 */
	public void shutdown() {
		logger.info("关闭许可证管理框架");

		try {
			// 关闭各组件
			licenseManager.cleanup();

			initialized = false;
			logger.info("许可证管理框架已关闭");

		} catch (Exception e) {
			logger.error("关闭许可证管理框架时发生异常", e);
		}
	}
}
