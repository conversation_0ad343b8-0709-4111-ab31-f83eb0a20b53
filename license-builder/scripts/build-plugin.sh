#!/bin/bash

# ALM License Builder - 统一构建脚本
# 支持三种模式：
# 1. obfuscate-only: 仅打包和混淆（旧模式）
# 2. full: 完整流程（打包、混淆、类分离、许可证处理）（旧模式）
# 3. license: 新Maven插件模式（编译、类分离、加密、校验）

set -euo pipefail

# 脚本信息
SCRIPT_NAME="build-plugin.sh"
SCRIPT_VERSION="2.0.0"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 默认配置
DEFAULT_POLARION_PATH="/opt/polarion/polarion"
DEFAULT_OBFUSCATION_TOOL="PROGUARD"
DEFAULT_OBFUSCATION_LEVEL="AGGRESSIVE"
DEFAULT_MODE="obfuscate-only"

# 全局变量
PLUGIN_DIR=""
POLARION_PATH=""
VERBOSE=false
CLEAN=false
OBFUSCATION_TOOL="$DEFAULT_OBFUSCATION_TOOL"
OBFUSCATION_LEVEL="$DEFAULT_OBFUSCATION_LEVEL"
BUILD_MODE="$DEFAULT_MODE"
AUTO_CLEANUP=true

# 数字签名配置
ENABLE_SIGNING=true
SIGNING_KEYSTORE=""
SIGNING_ALIAS=""
SIGNING_STOREPASS=""
SIGNING_KEYPASS=""
SIGNING_TSA_URL="http://timestamp.sectigo.com"
SIGNING_VERBOSE=false

# RSA密钥相关变量
RSA_KEY_PATH="/opt/license"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
$SCRIPT_NAME v$SCRIPT_VERSION - ALM License Builder 统一构建脚本

支持两种构建模式：
  obfuscate-only 仅执行打包和混淆（默认）
  full           完整流程（打包、混淆、类分离、许可证处理）

用法:
    $0 <plugin-directory> [polarion-path] [options]

参数:
    plugin-directory    插件源代码目录路径
    polarion-path      Polarion安装路径 (可选，默认: $DEFAULT_POLARION_PATH)

选项:
    -h, --help         显示此帮助信息
    -v, --verbose      详细输出
    -c, --clean        清理构建目录
    -m, --mode         构建模式 (obfuscate-only|full，默认: $DEFAULT_MODE)
    --obfuscation-tool 指定混淆工具 (YGUARD|PROGUARD，默认: $DEFAULT_OBFUSCATION_TOOL)
    --obfuscation-level 指定混淆级别 (BASIC|AGGRESSIVE，默认: $DEFAULT_OBFUSCATION_LEVEL)
    --no-cleanup       禁用自动清理临时文件

数字签名选项:
    --enable-signing   启用数字签名 (默认: 启用)
    --disable-signing  禁用数字签名
    --keystore         签名密钥库路径
    --alias            签名别名
    --storepass        密钥库密码
    --keypass          密钥密码
    --tsa-url          时间戳服务器URL (默认: http://timestamp.sectigo.com)

许可证RSA密钥选项:
    --rsa-key-path     RSA密钥文件目录路径 (默认: /opt/license)

环境变量:
    POLARION_HOME      Polarion安装路径

示例:
    # 仅打包和混淆（默认模式）
    $0 /path/to/my-plugin
    
    # 完整流程
    $0 /path/to/my-plugin --mode full
    
    # 自定义配置
    $0 /path/to/my-plugin /opt/polarion/polarion --mode obfuscate-only --obfuscation-tool PROGUARD --verbose

输出:
    obfuscate-only 模式: 混淆后的JAR文件在插件目录的target/目录中
    full 模式: 完整的许可证包在指定的输出目录中
EOF
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -c|--clean)
                CLEAN=true
                shift
                ;;
            -m|--mode)
                BUILD_MODE="$2"
                shift 2
                ;;
            --obfuscation-tool)
                OBFUSCATION_TOOL="$2"
                shift 2
                ;;
            --obfuscation-level)
                OBFUSCATION_LEVEL="$2"
                shift 2
                ;;
            --no-cleanup)
                AUTO_CLEANUP=false
                shift
                ;;
            --enable-signing)
                ENABLE_SIGNING=true
                shift
                ;;
            --disable-signing)
                ENABLE_SIGNING=false
                shift
                ;;
            --keystore)
                SIGNING_KEYSTORE="$2"
                shift 2
                ;;
            --alias)
                SIGNING_ALIAS="$2"
                shift 2
                ;;
            --storepass)
                SIGNING_STOREPASS="$2"
                shift 2
                ;;
            --keypass)
                SIGNING_KEYPASS="$2"
                shift 2
                ;;
            --tsa-url)
                SIGNING_TSA_URL="$2"
                shift 2
                ;;
            --rsa-key-path)
                RSA_KEY_PATH="$2"
                shift 2
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [[ -z "$PLUGIN_DIR" ]]; then
                    PLUGIN_DIR="$1"
                elif [[ -z "$POLARION_PATH" ]]; then
                    POLARION_PATH="$1"
                else
                    log_error "过多的参数: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 验证必需参数
    if [[ -z "$PLUGIN_DIR" ]]; then
        log_error "缺少插件目录参数"
        show_help
        exit 1
    fi

    # 验证构建模式
    case "$BUILD_MODE" in
        obfuscate-only|full)
            log_info "构建模式: $BUILD_MODE"
            ;;
        *)
            log_error "不支持的构建模式: $BUILD_MODE"
            log_error "支持的构建模式: obfuscate-only, full"
            exit 1
            ;;
    esac

    # 验证混淆工具参数
    case "$OBFUSCATION_TOOL" in
        YGUARD|PROGUARD)
            log_info "使用混淆工具: $OBFUSCATION_TOOL"
            ;;
        *)
            log_error "不支持的混淆工具: $OBFUSCATION_TOOL"
            log_error "支持的混淆工具: YGUARD, PROGUARD"
            exit 1
            ;;
    esac

    # 验证混淆级别参数
    case "$OBFUSCATION_LEVEL" in
        BASIC|AGGRESSIVE)
            log_info "使用混淆级别: $OBFUSCATION_LEVEL"
            ;;
        *)
            log_error "不支持的混淆级别: $OBFUSCATION_LEVEL"
            log_error "支持的混淆级别: BASIC, AGGRESSIVE"
            exit 1
            ;;
    esac
    
    # 显示签名配置信息
    if [[ "$ENABLE_SIGNING" == "true" ]]; then
        log_info "数字签名: 启用"
        if [[ -n "$SIGNING_KEYSTORE" ]]; then
            log_info "  密钥库: $SIGNING_KEYSTORE"
        else
            log_info "  密钥库: 使用默认值 (\${user.home}/.keystore)"
        fi
        if [[ -n "$SIGNING_ALIAS" ]]; then
            log_info "  别名: $SIGNING_ALIAS"
        else
            log_info "  别名: 使用默认值 (mykey)"
        fi
        log_info "  时间戳服务器: $SIGNING_TSA_URL"
    else
        log_info "数字签名: 禁用"
    fi

    # 设置Polarion路径
    if [[ -z "$POLARION_PATH" ]]; then
        POLARION_PATH="${POLARION_HOME:-$DEFAULT_POLARION_PATH}"
    fi
}

# 验证插件目录
validate_plugin_directory() {
    if [[ ! -d "$PLUGIN_DIR" ]]; then
        log_error "插件目录不存在: $PLUGIN_DIR"
        exit 1
    fi
    
    local manifest_path="$PLUGIN_DIR/META-INF/MANIFEST.MF"
    if [[ ! -f "$manifest_path" ]]; then
        log_error "未找到MANIFEST.MF文件: $manifest_path"
        exit 1
    fi
    
    log_info "插件目录验证通过: $PLUGIN_DIR"
}

# 验证Polarion路径
validate_polarion_path() {
    if [[ ! -d "$POLARION_PATH" ]]; then
        log_warn "Polarion路径不存在: $POLARION_PATH"
        log_warn "将尝试继续构建，但可能缺少某些依赖"
        return 0
    fi
    
    if [[ ! -d "$POLARION_PATH/plugins" ]]; then
        log_warn "Polarion插件目录不存在: $POLARION_PATH/plugins"
        log_warn "将尝试继续构建，但可能缺少某些依赖"
        return 0
    fi
    
    log_info "Polarion路径验证通过: $POLARION_PATH"
}

# 查找license-builder JAR文件
find_license_builder_jar() {
    local jar_name="license-builder-all.jar"

    # 检查顺序（适合部署到服务器的场景）：
    # 1. 脚本同目录（部署时JAR和脚本放在同一目录）
    # 2. 脚本上级目录
    # 3. 脚本上级目录的target子目录（开发环境）
    # 4. 当前工作目录
    local possible_paths=(
        "$SCRIPT_DIR/$jar_name"
        "$SCRIPT_DIR/license-builder.jar"
        "$(dirname "$SCRIPT_DIR")/$jar_name"
        "$(dirname "$SCRIPT_DIR")/license-builder.jar"
        "$(dirname "$SCRIPT_DIR")/target/$jar_name"
        "$(dirname "$SCRIPT_DIR")/target/license-builder.jar"
        "./$jar_name"
        "./license-builder.jar"
    )

    for jar_path in "${possible_paths[@]}"; do
        if [[ -f "$jar_path" ]]; then
            echo "$jar_path"
            return 0
        fi
    done

    # 如果没找到，且在开发环境中，尝试构建
    if [[ -f "$(dirname "$SCRIPT_DIR")/pom.xml" ]]; then
        log_info "未找到license-builder JAR文件，尝试构建..."
        cd "$(dirname "$SCRIPT_DIR")"
        if mvn clean package -DskipTests -q; then
            cd - > /dev/null
            # 重新查找构建后的文件
            for jar_path in "${possible_paths[@]}"; do
                if [[ -f "$jar_path" ]]; then
                    echo "$jar_path"
                    return 0
                fi
            done
        else
            cd - > /dev/null
        fi
    fi

    log_error "无法找到license-builder JAR文件"
    log_error "请确保以下文件之一存在："
    for path in "${possible_paths[@]}"; do
        log_error "  - $path"
    done
    exit 1
}

# 生成Tycho配置
generate_tycho_configuration() {
    log_info "生成Tycho配置..."

    # 查找license-builder JAR文件
    local jar_file
    jar_file=$(find_license_builder_jar)
    log_debug "使用JAR文件: $jar_file"

    # 设置签名相关的系统属性
    local signing_properties=()
    signing_properties+=("-Dsigning.enabled=$ENABLE_SIGNING")
    if [[ "$SIGNING_VERBOSE" == "true" ]]; then
        signing_properties+=("-Dsigning.verbose=true")
    fi
    if [[ -n "$SIGNING_KEYSTORE" ]]; then
        signing_properties+=("-Dsigning.keystore.path=$SIGNING_KEYSTORE")
    fi
    if [[ -n "$SIGNING_ALIAS" ]]; then
        signing_properties+=("-Dsigning.alias=$SIGNING_ALIAS")
    fi
    if [[ -n "$SIGNING_STOREPASS" ]]; then
        signing_properties+=("-Dsigning.storepass=$SIGNING_STOREPASS")
    fi
    if [[ -n "$SIGNING_KEYPASS" ]]; then
        signing_properties+=("-Dsigning.keypass=$SIGNING_KEYPASS")
    fi
    if [[ -n "$SIGNING_TSA_URL" ]]; then
        signing_properties+=("-Dsigning.tsa.url=$SIGNING_TSA_URL")
    fi
    if [[ -n "$RSA_KEY_PATH" ]]; then
        signing_properties+=("-Dpackaging.license.metadata.keyPath=$RSA_KEY_PATH")
    fi

    # 构建Java命令 - 使用LicenseBuilder
    local java_args=(
        "${signing_properties[@]}"
        "-Dbuild.mode=$BUILD_MODE"
        "-cp" "$jar_file"
        "com.fasnote.alm.license.builder.LicenseBuilderCLI"
        "--tycho-mode"
        "--plugin-dir" "$PLUGIN_DIR"
        "--mode" "tycho-config"
        "--polarion-path" "$POLARION_PATH"
        "--obfuscation-tool" "$OBFUSCATION_TOOL"
        "--obfuscation-level" "$OBFUSCATION_LEVEL"
        "--license-validity" "DAYS_365"
    )

    # 添加详细输出参数
    if [[ "$VERBOSE" == "true" ]]; then
        java_args+=("--verbose")
        SIGNING_VERBOSE=true
    fi

    log_debug "Java命令: java ${java_args[*]}"

    # 执行Java配置生成器
    if java "${java_args[@]}"; then
        log_success "Tycho配置生成完成"
    else
        log_error "Tycho配置生成失败"
        exit 1
    fi
}

# 执行Maven构建
execute_maven_build() {
    log_info "执行Maven构建..."

    cd "$PLUGIN_DIR"

    # 清理构建目录
    if [[ "$CLEAN" == "true" ]]; then
        log_info "清理构建目录..."
        rm -rf target/
    fi

    # 构建Maven参数
    local maven_args=(
        "clean"
        "install"
        "-Dbuild.mode=$BUILD_MODE"
    )

    # 根据构建模式激活对应的profile
    if [[ "$BUILD_MODE" == "obfuscate-only" ]]; then
        maven_args+=("-P" "obfuscate-only")
    elif [[ "$BUILD_MODE" == "full" ]]; then
        maven_args+=("-P" "full")
    fi

    if [[ "$VERBOSE" == "true" ]]; then
        maven_args+=("-X")
    else
        maven_args+=("-q")
    fi

    log_debug "Maven命令: mvn ${maven_args[*]}"

    # 执行Maven构建
    if mvn "${maven_args[@]}"; then
        log_success "Maven构建完成"
    else
        log_error "Maven构建失败"
        cd - > /dev/null
        exit 1
    fi

    cd - > /dev/null
}

# 执行完整流程（包含许可证处理）
execute_full_build() {
    log_info "执行完整构建流程..."

    # 查找license-builder JAR文件
    local jar_file
    jar_file=$(find_license_builder_jar)
    log_debug "使用JAR文件: $jar_file"

    # 设置签名相关的系统属性（与generate_tycho_configuration保持一致）
    local signing_properties=()
    signing_properties+=("-Dsigning.enabled=$ENABLE_SIGNING")
    if [[ "$SIGNING_VERBOSE" == "true" ]]; then
        signing_properties+=("-Dsigning.verbose=true")
    fi
    if [[ -n "$SIGNING_KEYSTORE" ]]; then
        signing_properties+=("-Dsigning.keystore.path=$SIGNING_KEYSTORE")
    fi
    if [[ -n "$SIGNING_ALIAS" ]]; then
        signing_properties+=("-Dsigning.alias=$SIGNING_ALIAS")
    fi
    if [[ -n "$SIGNING_STOREPASS" ]]; then
        signing_properties+=("-Dsigning.storepass=$SIGNING_STOREPASS")
    fi
    if [[ -n "$SIGNING_KEYPASS" ]]; then
        signing_properties+=("-Dsigning.keypass=$SIGNING_KEYPASS")
    fi
    if [[ -n "$SIGNING_TSA_URL" ]]; then
        signing_properties+=("-Dsigning.tsa.url=$SIGNING_TSA_URL")
    fi
    if [[ -n "$RSA_KEY_PATH" ]]; then
        signing_properties+=("-Dpackaging.license.metadata.keyPath=$RSA_KEY_PATH")
    fi

    # 使用LicenseBuilder进行Tycho完整构建
    local java_args=(
        "${signing_properties[@]}"
        "-Dbuild.mode=$BUILD_MODE"
        "-cp" "$jar_file"
        "com.fasnote.alm.license.builder.LicenseBuilderCLI"
        "--tycho-mode"
        "--plugin-dir" "$PLUGIN_DIR"
        "--mode" "tycho-build"
        "--polarion-path" "$POLARION_PATH"
        "--obfuscation-tool" "$OBFUSCATION_TOOL"
        "--obfuscation-level" "$OBFUSCATION_LEVEL"
        "--license-validity" "DAYS_365"
    )

    # 添加详细输出参数
    if [[ "$VERBOSE" == "true" ]]; then
        java_args+=("--verbose")
    fi

    log_debug "Java命令: java ${java_args[*]}"

    # 执行完整构建流程
    if java "${java_args[@]}"; then
        log_success "完整构建流程完成"
    else
        log_error "完整构建流程失败"
        exit 1
    fi
}







# 查找生成的JAR文件
find_generated_jar() {
    log_info "查找生成的JAR文件..."

    # 查找target目录中的JAR文件（优先查找混淆后的JAR）
    local jar_files=()

    # 首先查找混淆后的JAR文件
    while IFS= read -r -d '' file; do
        jar_files+=("$file")
    done < <(find target -name "*-obfuscated.jar" -type f -print0 2>/dev/null || true)

    # 如果没有混淆JAR，查找普通JAR文件
    if [[ ${#jar_files[@]} -eq 0 ]]; then
        while IFS= read -r -d '' file; do
            if [[ "$file" != *"sources"* ]] && [[ "$file" != *"test"* ]]; then
                jar_files+=("$file")
            fi
        done < <(find target -name "*.jar" -type f -print0 2>/dev/null || true)
    fi

    if [[ ${#jar_files[@]} -gt 0 ]]; then
        local source_jar="${jar_files[0]}"
        local plugin_name="$(basename "$PLUGIN_DIR")"
        local target_jar="target/${plugin_name}-test.jar"

        log_info "找到JAR文件: $source_jar"

        # 复制到标准位置
        cp "$source_jar" "$target_jar"
        log_success "JAR文件已复制到: $target_jar"

        # 显示JAR文件大小
        local jar_size=$(stat -f%z "$target_jar" 2>/dev/null || stat -c%s "$target_jar" 2>/dev/null || echo "unknown")
        log_info "JAR文件大小: ${jar_size} bytes"
    else
        log_error "未找到生成的JAR文件"
        return 1
    fi
}



# 清理临时文件
cleanup_temporary_files() {
    if [[ "$AUTO_CLEANUP" != "true" ]]; then
        log_info "跳过自动清理（--no-cleanup 选项已启用）"
        return 0
    fi

    log_info "清理临时文件..."

    cd "$PLUGIN_DIR"

    # 清理生成的配置文件
    local files_to_clean=(
        "pom.xml"
        "polarion-platform.target"
        ".classpath.backup"
    )

    for file in "${files_to_clean[@]}"; do
        if [[ -f "$file" ]]; then
            log_debug "删除文件: $file"
            rm -f "$file"
        fi
    done

    log_success "临时文件清理完成"

    cd - > /dev/null
}

# 显示构建结果
show_build_results() {
    # 获取绝对路径
    local abs_plugin_dir="$(cd "$PLUGIN_DIR" && pwd)"
    local abs_target_dir="$abs_plugin_dir/target"

    log_info "构建结果:"
    log_info "  插件目录: $abs_plugin_dir"
    log_info "  构建模式: $BUILD_MODE"
    log_info "  输出目录: $abs_target_dir"

    if [[ -d "$abs_target_dir" ]]; then
        local jar_files=($(find "$abs_target_dir" -name "*.jar" -type f 2>/dev/null))

        if [[ ${#jar_files[@]} -gt 0 ]]; then
            for jar_file in "${jar_files[@]}"; do
                local size=$(stat -f%z "$jar_file" 2>/dev/null || stat -c%s "$jar_file" 2>/dev/null || echo "unknown")
                log_info "  生成文件: $jar_file (${size} bytes)"
            done
        else
            log_warn "  未找到生成的JAR文件"
        fi

        # 检查混淆映射文件
        if [[ -f "$abs_target_dir/proguard_map.txt" ]]; then
            log_info "  混淆映射文件: $abs_target_dir/proguard_map.txt"
        fi

        # 检查许可证输出（full模式）
        if [[ "$BUILD_MODE" == "full" && -d "$abs_target_dir/license-output" ]]; then
            local abs_license_dir="$abs_target_dir/license-output"
            log_info "  许可证输出目录: $abs_license_dir/"

            local separated_files=($(find "$abs_license_dir" -name "*.jar" -type f 2>/dev/null))
            if [[ ${#separated_files[@]} -gt 0 ]]; then
                for separated_file in "${separated_files[@]}"; do
                    local size=$(stat -f%z "$separated_file" 2>/dev/null || stat -c%s "$separated_file" 2>/dev/null || echo "unknown")
                    log_info "    $separated_file (${size} bytes)"
                done
            fi

            # 检查许可证文件
            local license_files=($(find "$abs_license_dir" -name "*.lic*" -type f 2>/dev/null))
            if [[ ${#license_files[@]} -gt 0 ]]; then
                for license_file in "${license_files[@]}"; do
                    local size=$(stat -f%z "$license_file" 2>/dev/null || stat -c%s "$license_file" 2>/dev/null || echo "unknown")
                    log_info "    $license_file (${size} bytes)"
                done
            fi
        fi
    else
        log_warn "  输出目录不存在: $abs_target_dir"
    fi
}

# 主函数
main() {
    log_info "ALM License Builder v$SCRIPT_VERSION"
    log_info "开始构建流程..."

    # 解析参数
    parse_arguments "$@"

    # 验证插件目录
    validate_plugin_directory

    # 根据模式执行不同的构建流程
    # 验证Polarion路径
    validate_polarion_path

    # 根据构建模式执行不同流程
    if [[ "$BUILD_MODE" == "full" ]]; then
        # full模式：直接执行完整构建流程（包含配置生成、Maven构建和许可证处理）
        execute_full_build
    else
        # obfuscate-only模式：仅生成配置和执行Maven构建
        generate_tycho_configuration
        execute_maven_build
    fi

    # 清理临时文件
    cleanup_temporary_files

    # 显示构建结果
    show_build_results

    # 获取绝对路径用于最终消息
    local abs_plugin_dir="$(cd "$PLUGIN_DIR" && pwd)"

    if [[ "$BUILD_MODE" == "obfuscate-only" ]]; then
        log_success "构建完成! 混淆后的JAR文件已生成在 $abs_plugin_dir/target/ 目录中"
    else
        log_success "构建完成! 完整的许可证包已生成在 $abs_plugin_dir/target/license-output/ 目录中"
    fi
}

# 错误处理
trap 'log_error "脚本执行失败，退出码: $?"' ERR

# 执行主函数
main "$@"
