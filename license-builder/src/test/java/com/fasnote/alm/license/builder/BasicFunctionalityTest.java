package com.fasnote.alm.license.builder;

import com.fasnote.alm.license.builder.exception.BuildException;
import com.fasnote.alm.license.builder.tycho.TychoConfigGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 基本功能测试 - 验证重构后的核心功能
 */
class BasicFunctionalityTest {

    @TempDir
    Path tempDir;
    
    private Path pluginDir;
    private Path polarionPath;
    private TychoConfigGenerator generator;

    @BeforeEach
    void setUp() throws IOException {
        // 创建测试目录结构
        pluginDir = tempDir.resolve("test-plugin");
        polarionPath = tempDir.resolve("polarion");
        
        Files.createDirectories(pluginDir);
        Files.createDirectories(polarionPath.resolve("plugins"));
        
        // 创建基本的 MANIFEST.MF
        Path metaInfDir = pluginDir.resolve("META-INF");
        Files.createDirectories(metaInfDir);
        
        String manifestContent = "Manifest-Version: 1.0\n" +
            "Bundle-ManifestVersion: 2\n" +
            "Bundle-Name: Test Plugin\n" +
            "Bundle-SymbolicName: com.example.test.plugin\n" +
            "Bundle-Version: 1.0.0.qualifier\n" +
            "Bundle-Vendor: Test Vendor\n" +
            "Bundle-RequiredExecutionEnvironment: JavaSE-11\n" +
            "Require-Bundle: org.eclipse.core.runtime,\n" +
            " org.eclipse.ui\n";
        Files.writeString(metaInfDir.resolve("MANIFEST.MF"), manifestContent);
        
        // 创建基本的 .classpath 文件
        String classpathContent = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
            "<classpath>\n" +
            "    <classpathentry kind=\"con\" path=\"org.eclipse.jdt.launching.JRE_CONTAINER\"/>\n" +
            "    <classpathentry kind=\"con\" path=\"org.eclipse.pde.core.requiredPlugins\"/>\n" +
            "    <classpathentry kind=\"src\" path=\"src\"/>\n" +
            "    <classpathentry kind=\"output\" path=\"bin\"/>\n" +
            "</classpath>\n";
        Files.writeString(pluginDir.resolve(".classpath"), classpathContent);
        
        generator = new TychoConfigGenerator(pluginDir, polarionPath);
    }

    @Test
    void testBasicConfigurationGeneration() throws BuildException {
        // 测试基本的配置生成功能
        assertDoesNotThrow(() -> {
            generator.generateTychoConfiguration();
        });
        
        // 验证所有必要文件都被创建
        assertTrue(Files.exists(pluginDir.resolve("pom.xml")));
        assertTrue(Files.exists(pluginDir.resolve("build.properties")));
        assertTrue(Files.exists(pluginDir.resolve("polarion-platform.target")));
    }

    @Test
    void testPomXmlGeneration() throws BuildException, IOException {
        generator.generateTychoConfiguration();
        
        Path pomFile = pluginDir.resolve("pom.xml");
        assertTrue(Files.exists(pomFile));
        
        String pomContent = Files.readString(pomFile);
        
        // 验证基本内容
        assertTrue(pomContent.contains("<?xml version=\"1.0\" encoding=\"UTF-8\"?>"));
        assertTrue(pomContent.contains("<project"));
        assertTrue(pomContent.contains("</project>"));
        assertTrue(pomContent.contains("com.example.test.plugin"));
        assertTrue(pomContent.contains("1.0.0-SNAPSHOT"));
    }

    @Test
    void testBuildPropertiesGeneration() throws BuildException, IOException {
        generator.generateTychoConfiguration();
        
        Path buildPropsFile = pluginDir.resolve("build.properties");
        assertTrue(Files.exists(buildPropsFile));
        
        String content = Files.readString(buildPropsFile);
        assertTrue(content.contains("source.. = src/"));
        assertTrue(content.contains("output.. = bin/"));
        assertTrue(content.contains("bin.includes = META-INF/"));
    }

    @Test
    void testTargetPlatformGeneration() throws BuildException, IOException {
        generator.generateTychoConfiguration();
        
        Path targetFile = pluginDir.resolve("polarion-platform.target");
        assertTrue(Files.exists(targetFile));
        
        String content = Files.readString(targetFile);
        assertTrue(content.contains("<?xml version=\"1.0\" encoding=\"UTF-8\""));
        assertTrue(content.contains("<target"));
        assertTrue(content.contains("</target>"));
        assertTrue(content.contains(polarionPath.toString()));
    }

    @Test
    void testClasspathProcessing() throws BuildException, IOException {
        // 添加 JUnit 依赖到 .classpath
        String classpathWithJunit = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
            "<classpath>\n" +
            "    <classpathentry kind=\"con\" path=\"org.eclipse.jdt.launching.JRE_CONTAINER\"/>\n" +
            "    <classpathentry kind=\"con\" path=\"org.eclipse.pde.core.requiredPlugins\"/>\n" +
            "    <classpathentry kind=\"con\" path=\"org.eclipse.jdt.junit.JUNIT_CONTAINER/5\"/>\n" +
            "    <classpathentry kind=\"src\" path=\"src\"/>\n" +
            "    <classpathentry kind=\"src\" path=\"src/test/java\"/>\n" +
            "    <classpathentry kind=\"output\" path=\"bin\"/>\n" +
            "</classpath>\n";
        Files.writeString(pluginDir.resolve(".classpath"), classpathWithJunit);
        
        generator.generateTychoConfiguration();
        
        // 验证 JUnit 依赖被移除
        String fixedContent = Files.readString(pluginDir.resolve(".classpath"));
        assertFalse(fixedContent.contains("JUNIT_CONTAINER"));
        assertFalse(fixedContent.contains("src/test/java"));
        
        // 验证其他内容保留
        assertTrue(fixedContent.contains("org.eclipse.jdt.launching.JRE_CONTAINER"));
        assertTrue(fixedContent.contains("org.eclipse.pde.core.requiredPlugins"));
        
        // 验证备份文件被创建
        assertTrue(Files.exists(pluginDir.resolve(".classpath.backup")));
    }

    @Test
    void testConfigurationOptions() throws BuildException, IOException {
        // 测试配置选项
        generator.setSkipObfuscation(false); // 启用混淆才会生成相关内容
        generator.setObfuscationTool("PROGUARD");
        generator.setObfuscationLevel("AGGRESSIVE");
        generator.setLicenseValidity("DAYS_30");

        generator.generateTychoConfiguration();

        // 验证配置在生成的文件中生效
        String pomContent = Files.readString(pluginDir.resolve("pom.xml"));

        // 调试输出
        System.out.println("=== BasicFunctionalityTest 生成的 pom.xml ===");
        System.out.println(pomContent);
        System.out.println("=== 内容结束 ===");

        // 检查实际生成的内容
        boolean hasProguardContent = pomContent.contains("proguard-maven-plugin") ||
                                   pomContent.contains("proguard");
        assertTrue(hasProguardContent, "应该包含ProGuard相关内容");
    }

    @Test
    void testCleanupFunctionality() throws BuildException {
        generator.generateTychoConfiguration();
        
        // 验证备份文件存在
        assertTrue(Files.exists(pluginDir.resolve(".classpath.backup")));
        
        // 执行清理
        generator.cleanup();
        
        // 验证备份文件被删除
        assertFalse(Files.exists(pluginDir.resolve(".classpath.backup")));
    }

    @Test
    void testErrorHandling() {
        // 测试错误处理 - 无效的插件目录
        Path invalidDir = tempDir.resolve("nonexistent");
        TychoConfigGenerator invalidGenerator = new TychoConfigGenerator(invalidDir, polarionPath);
        
        assertThrows(BuildException.class, () -> {
            invalidGenerator.generateTychoConfiguration();
        });
    }

    @Test
    void testMultipleGenerations() throws BuildException, IOException {
        // 测试多次生成的一致性
        generator.generateTychoConfiguration();
        
        String pomContent1 = Files.readString(pluginDir.resolve("pom.xml"));
        String buildProps1 = Files.readString(pluginDir.resolve("build.properties"));
        
        // 删除文件并重新生成
        Files.deleteIfExists(pluginDir.resolve("pom.xml"));
        Files.deleteIfExists(pluginDir.resolve("build.properties"));
        
        generator.generateTychoConfiguration();
        
        String pomContent2 = Files.readString(pluginDir.resolve("pom.xml"));
        String buildProps2 = Files.readString(pluginDir.resolve("build.properties"));
        
        // 验证输出一致性
        assertEquals(pomContent1, pomContent2);
        assertEquals(buildProps1, buildProps2);
    }
}
