<template>
  <div class="plugin-detail">
    <el-page-header @back="$router.back()" title="返回插件列表">
      <template #content>
        <span class="page-title">插件详情 - {{ pluginId }}</span>
      </template>
    </el-page-header>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 许可证信息 -->
      <el-col :span="16">
        <el-card title="许可证信息" shadow="hover" v-loading="loading">
          <template #header>
            <div class="card-header">
              <span>许可证信息</span>
              <el-button type="primary" @click="loadLicenseInfo" :loading="loading">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>

          <el-descriptions :column="2" border v-if="licenseInfo">
            <el-descriptions-item label="插件ID">
              {{ licenseInfo.pluginId }}
            </el-descriptions-item>
            <el-descriptions-item label="许可证状态">
              <el-tag :type="licenseInfo.valid ? 'success' : 'danger'">
                {{ licenseInfo.valid ? '有效' : '无效' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态描述">
              {{ licenseInfo.status || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="许可证类型">
              {{ licenseInfo.licenseType || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="被许可人">
              {{ licenseInfo.licensee || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="签发日期">
              {{ formatDate(licenseInfo.issueDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="到期日期">
              {{ formatDate(licenseInfo.expiryDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="错误信息" v-if="licenseInfo.errorMessage">
              <el-text type="danger">{{ licenseInfo.errorMessage }}</el-text>
            </el-descriptions-item>
          </el-descriptions>

          <el-empty v-else description="暂无许可证信息" />
        </el-card>
      </el-col>

      <!-- 操作面板 -->
      <el-col :span="8">
        <el-card title="操作" shadow="hover">
          <div class="action-buttons">
            <el-button 
              type="success" 
              @click="validateLicense" 
              :loading="validating"
              style="width: 100%"
            >
              <el-icon><Check /></el-icon>
              验证许可证
            </el-button>
            
            <el-button 
              type="primary" 
              @click="showUpdateDialog = true"
              style="width: 100%"
            >
              <el-icon><Upload /></el-icon>
              更新许可证
            </el-button>
          </div>
        </el-card>

        <!-- 功能特性 -->
        <el-card title="功能特性" shadow="hover" style="margin-top: 20px;" v-if="licenseInfo?.features">
          <el-tag 
            v-for="(value, key) in licenseInfo.features" 
            :key="key"
            style="margin: 5px;"
            :type="value ? 'success' : 'info'"
          >
            {{ key }}: {{ value }}
          </el-tag>
        </el-card>
      </el-col>
    </el-row>

    <!-- 更新许可证对话框 -->
    <el-dialog 
      v-model="showUpdateDialog" 
      title="更新许可证" 
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="updateForm" label-width="100px">
        <el-form-item label="文件名">
          <el-input v-model="updateForm.fileName" placeholder="请输入许可证文件名" />
        </el-form-item>
        <el-form-item label="许可证内容">
          <el-input 
            v-model="updateForm.licenseContent" 
            type="textarea" 
            :rows="10"
            placeholder="请粘贴许可证内容（支持Base64编码或原始内容）"
          />
        </el-form-item>
        <el-form-item label="更新原因">
          <el-input v-model="updateForm.updateReason" placeholder="请输入更新原因" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showUpdateDialog = false">取消</el-button>
          <el-button type="primary" @click="updateLicense" :loading="updating">
            确认更新
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { pluginApi } from '../api'
import { 
  Refresh, 
  Check, 
  Upload 
} from '@element-plus/icons-vue'

export default {
  name: 'PluginDetail',
  components: {
    Refresh,
    Check,
    Upload
  },
  props: {
    pluginId: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const route = useRoute()
    const loading = ref(false)
    const validating = ref(false)
    const updating = ref(false)
    const showUpdateDialog = ref(false)
    const licenseInfo = ref(null)
    const updateForm = ref({
      fileName: '',
      licenseContent: '',
      updateReason: ''
    })

    const loadLicenseInfo = async () => {
      loading.value = true
      try {
        const data = await pluginApi.getPluginLicense(props.pluginId)
        licenseInfo.value = data
      } catch (error) {
        console.error('加载许可证信息失败:', error)
        ElMessage.error('加载许可证信息失败')
        licenseInfo.value = null
      } finally {
        loading.value = false
      }
    }

    const validateLicense = async () => {
      validating.value = true
      try {
        const result = await pluginApi.validatePluginLicense(props.pluginId)
        
        if (result.valid) {
          ElMessage.success('许可证验证通过')
        } else {
          ElMessage.warning('许可证验证失败')
        }
        
        // 重新加载许可证信息
        await loadLicenseInfo()
      } catch (error) {
        console.error('验证许可证失败:', error)
        ElMessage.error('验证许可证失败')
      } finally {
        validating.value = false
      }
    }

    const updateLicense = async () => {
      if (!updateForm.value.licenseContent.trim()) {
        ElMessage.warning('请输入许可证内容')
        return
      }

      try {
        await ElMessageBox.confirm(
          '确定要更新此插件的许可证吗？这将覆盖现有的许可证。',
          '确认更新',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        updating.value = true
        
        const response = await pluginApi.updatePluginLicense(props.pluginId, updateForm.value)
        
        if (response.success) {
          ElMessage.success('许可证更新成功')
          showUpdateDialog.value = false
          // 清空表单
          updateForm.value = {
            fileName: '',
            licenseContent: '',
            updateReason: ''
          }
          // 重新加载许可证信息
          await loadLicenseInfo()
        } else {
          ElMessage.error(`更新失败: ${response.message}`)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('更新许可证失败:', error)
          ElMessage.error('更新许可证失败')
        }
      } finally {
        updating.value = false
      }
    }

    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      try {
        return new Date(dateString).toLocaleString('zh-CN')
      } catch {
        return dateString
      }
    }

    onMounted(() => {
      loadLicenseInfo()
    })

    return {
      loading,
      validating,
      updating,
      showUpdateDialog,
      licenseInfo,
      updateForm,
      loadLicenseInfo,
      validateLicense,
      updateLicense,
      formatDate
    }
  }
}
</script>

<style scoped>
.plugin-detail {
  padding: 20px;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
