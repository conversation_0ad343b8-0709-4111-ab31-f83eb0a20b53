package com.fasnote.alm.injection.api;

/**
 * 许可证验证器接口
 * 
 * 定义许可证验证的抽象接口，由具体的许可证管理框架实现
 * 依赖注入框架通过此接口进行许可证验证，保持架构分层清晰
 */
public interface ILicenseValidator {
    
    /**
     * 验证插件许可证
     * 
     * @param pluginId 插件ID
     * @return 验证结果，true表示许可证有效，false表示无效
     */
    boolean validatePluginLicense(String pluginId);
    
    /**
     * 验证插件许可证（详细结果）
     * 
     * @param pluginId 插件ID
     * @return 验证结果对象，包含详细的验证信息
     */
    ValidationResult validatePluginLicenseDetailed(String pluginId);
    
    /**
     * 验证功能权限
     * 
     * @param pluginId 插件ID
     * @param featureName 功能名称
     * @return 是否有权限使用该功能
     */
    boolean validateFeaturePermission(String pluginId, String featureName);
    
    /**
     * 检查许可证是否存在
     * 
     * @param pluginId 插件ID
     * @return 许可证是否存在
     */
    boolean isLicenseExists(String pluginId);
    
    /**
     * 获取许可证状态描述
     * 
     * @param pluginId 插件ID
     * @return 许可证状态描述
     */
    String getLicenseStatus(String pluginId);
    

}
