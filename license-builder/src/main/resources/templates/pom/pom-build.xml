    <profiles>
        <!-- obfuscate-only模式：仅混淆，不执行许可证处理 -->
        <profile>
            <id>obfuscate-only</id>
            <activation>
                <property>
                    <name>build.mode</name>
                    <value>obfuscate-only</value>
                </property>
            </activation>
            <build>
                <finalName>${final_name}</finalName>
                <plugins>
                    {{>tycho/tycho-plugins.xml}}

                    {{#if enable_obfuscation}}
                        {{#if_equals obfuscation_tool "PROGUARD"}}
                            {{>obfuscation/proguard-plugin.xml}}
                        {{/if_equals}}
                        {{#if_equals obfuscation_tool "YGUARD"}}
                            {{>obfuscation/yguard-plugin.xml}}
                        {{/if_equals}}
                    {{/if}}

                    {{#if enable_signing}}
                        {{>signing/maven-jarsigner-plugin.xml}}
                    {{/if}}
                </plugins>
            </build>
        </profile>

        <!-- full模式：完整流程（混淆+许可证处理） -->
        <profile>
            <id>full</id>
            <activation>
                <property>
                    <name>build.mode</name>
                    <value>full</value>
                </property>
            </activation>
            <build>
                <finalName>${final_name}</finalName>
                <plugins>
                    {{>tycho/tycho-plugins.xml}}

                    {{#if enable_obfuscation}}
                        {{#if_equals obfuscation_tool "PROGUARD"}}
                            {{>obfuscation/proguard-plugin.xml}}
                        {{/if_equals}}
                        {{#if_equals obfuscation_tool "YGUARD"}}
                            {{>obfuscation/yguard-plugin.xml}}
                        {{/if_equals}}
                    {{/if}}

                    {{>license/license-processing.xml}}

                    {{#if enable_signing}}
                        {{>signing/maven-jarsigner-plugin.xml}}
                    {{/if}}
                </plugins>
            </build>
        </profile>

        <!-- default模式：向后兼容，等同于full模式 -->
        <profile>
            <id>default</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <finalName>${final_name}</finalName>
                <plugins>
                    {{>tycho/tycho-plugins.xml}}

                    {{#if enable_obfuscation}}
                        {{#if_equals obfuscation_tool "PROGUARD"}}
                            {{>obfuscation/proguard-plugin.xml}}
                        {{/if_equals}}
                        {{#if_equals obfuscation_tool "YGUARD"}}
                            {{>obfuscation/yguard-plugin.xml}}
                        {{/if_equals}}
                    {{/if}}

                    {{>license/license-processing.xml}}

                    {{#if enable_signing}}
                        {{>signing/maven-jarsigner-plugin.xml}}
                    {{/if}}
                </plugins>
            </build>
        </profile>
    </profiles>
