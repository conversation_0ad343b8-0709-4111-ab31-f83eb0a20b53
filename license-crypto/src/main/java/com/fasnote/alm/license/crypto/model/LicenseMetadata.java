package com.fasnote.alm.license.crypto.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 许可证元数据统一管理类
 * 确保builder和manage端使用相同的字段结构
 */
public class LicenseMetadata {
    
    // 核心字段
    private String pluginId;
    private String pluginName;
    private String pluginVersion;
    private String contentHash;
    private String machineCode;
    private String signature;
    
    // 时间字段
    private LocalDateTime issueTime;
    private LocalDateTime expiryTime;
    
    // 加密相关字段
    private String encryptionVersion;
    private String keyVersion;
    private String digitalSignature;
    private String signatureAlgorithm;
    
    // 功能和限制
    private Map<String, Object> features;
    private Map<String, Object> limitations;
    private Map<String, Object> customProperties;
    
    // 其他字段
    private String issuer;
    private String licenseType;
    private String description;
    
    public LicenseMetadata() {
        this.features = new HashMap<>();
        this.limitations = new HashMap<>();
        this.customProperties = new HashMap<>();
    }
    
    public LicenseMetadata(String pluginId) {
        this();
        this.pluginId = pluginId;
    }
    
    // Getter和Setter方法
    public String getPluginId() {
        return pluginId;
    }
    
    public void setPluginId(String pluginId) {
        this.pluginId = pluginId;
    }
    
    public String getPluginName() {
        return pluginName;
    }
    
    public void setPluginName(String pluginName) {
        this.pluginName = pluginName;
    }
    
    public String getPluginVersion() {
        return pluginVersion;
    }
    
    public void setPluginVersion(String pluginVersion) {
        this.pluginVersion = pluginVersion;
    }
    
    public String getContentHash() {
        return contentHash;
    }
    
    public void setContentHash(String contentHash) {
        this.contentHash = contentHash;
    }
    
    public String getMachineCode() {
        return machineCode;
    }
    
    public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
    }
    
    public String getSignature() {
        return signature;
    }
    
    public void setSignature(String signature) {
        this.signature = signature;
    }
    
    public LocalDateTime getIssueTime() {
        return issueTime;
    }
    
    public void setIssueTime(LocalDateTime issueTime) {
        this.issueTime = issueTime;
    }
    
    public LocalDateTime getExpiryTime() {
        return expiryTime;
    }
    
    public void setExpiryTime(LocalDateTime expiryTime) {
        this.expiryTime = expiryTime;
    }
    
    public String getEncryptionVersion() {
        return encryptionVersion;
    }
    
    public void setEncryptionVersion(String encryptionVersion) {
        this.encryptionVersion = encryptionVersion;
    }
    
    public String getKeyVersion() {
        return keyVersion;
    }
    
    public void setKeyVersion(String keyVersion) {
        this.keyVersion = keyVersion;
    }
    
    public String getDigitalSignature() {
        return digitalSignature;
    }
    
    public void setDigitalSignature(String digitalSignature) {
        this.digitalSignature = digitalSignature;
    }
    
    public String getSignatureAlgorithm() {
        return signatureAlgorithm;
    }
    
    public void setSignatureAlgorithm(String signatureAlgorithm) {
        this.signatureAlgorithm = signatureAlgorithm;
    }
    
    public Map<String, Object> getFeatures() {
        return features;
    }
    
    public void setFeatures(Map<String, Object> features) {
        this.features = features != null ? features : new HashMap<>();
    }
    
    public Map<String, Object> getLimitations() {
        return limitations;
    }
    
    public void setLimitations(Map<String, Object> limitations) {
        this.limitations = limitations != null ? limitations : new HashMap<>();
    }
    
    public Map<String, Object> getCustomProperties() {
        return customProperties;
    }
    
    public void setCustomProperties(Map<String, Object> customProperties) {
        this.customProperties = customProperties != null ? customProperties : new HashMap<>();
    }
    
    public String getIssuer() {
        return issuer;
    }
    
    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }
    
    public String getLicenseType() {
        return licenseType;
    }
    
    public void setLicenseType(String licenseType) {
        this.licenseType = licenseType;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    /**
     * 转换为Map格式（用于JSON序列化）
     */
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        
        // 基本字段
        if (pluginId != null) map.put("pluginId", pluginId);
        if (pluginName != null) map.put("pluginName", pluginName);
        if (pluginVersion != null) map.put("pluginVersion", pluginVersion);
        if (contentHash != null) map.put("contentHash", contentHash);
        if (machineCode != null) map.put("machineCode", machineCode);
        if (signature != null) map.put("signature", signature);
        
        // 时间字段
        if (issueTime != null) {
            map.put("issueTime", issueTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        }
        if (expiryTime != null) {
            map.put("expiryTime", expiryTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        }
        
        // 加密字段
        if (encryptionVersion != null) map.put("encryptionVersion", encryptionVersion);
        if (keyVersion != null) map.put("keyVersion", keyVersion);
        if (digitalSignature != null) map.put("digitalSignature", digitalSignature);
        if (signatureAlgorithm != null) map.put("signatureAlgorithm", signatureAlgorithm);
        
        // 复杂字段
        if (features != null && !features.isEmpty()) map.put("features", features);
        if (limitations != null && !limitations.isEmpty()) map.put("limitations", limitations);
        if (customProperties != null && !customProperties.isEmpty()) map.put("customProperties", customProperties);
        
        // 其他字段
        if (issuer != null) map.put("issuer", issuer);
        if (licenseType != null) map.put("licenseType", licenseType);
        if (description != null) map.put("description", description);
        
        return map;
    }
    
    /**
     * 从Map创建LicenseMetadata对象
     */
    public static LicenseMetadata fromMap(Map<String, Object> map) {
        LicenseMetadata metadata = new LicenseMetadata();
        
        if (map == null) return metadata;
        
        // 基本字段
        metadata.setPluginId((String) map.get("pluginId"));
        metadata.setPluginName((String) map.get("pluginName"));
        metadata.setPluginVersion((String) map.get("pluginVersion"));
        metadata.setContentHash((String) map.get("contentHash"));
        metadata.setMachineCode((String) map.get("machineCode"));
        metadata.setSignature((String) map.get("signature"));
        
        // 时间字段
        String issueTimeStr = (String) map.get("issueTime");
        if (issueTimeStr != null) {
            metadata.setIssueTime(LocalDateTime.parse(issueTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        }
        String expiryTimeStr = (String) map.get("expiryTime");
        if (expiryTimeStr != null) {
            metadata.setExpiryTime(LocalDateTime.parse(expiryTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        }
        
        // 加密字段
        metadata.setEncryptionVersion((String) map.get("encryptionVersion"));
        metadata.setKeyVersion((String) map.get("keyVersion"));
        metadata.setDigitalSignature((String) map.get("digitalSignature"));
        metadata.setSignatureAlgorithm((String) map.get("signatureAlgorithm"));
        
        // 复杂字段
        @SuppressWarnings("unchecked")
        Map<String, Object> features = (Map<String, Object>) map.get("features");
        metadata.setFeatures(features);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> limitations = (Map<String, Object>) map.get("limitations");
        metadata.setLimitations(limitations);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> customProperties = (Map<String, Object>) map.get("customProperties");
        metadata.setCustomProperties(customProperties);
        
        // 其他字段
        metadata.setIssuer((String) map.get("issuer"));
        metadata.setLicenseType((String) map.get("licenseType"));
        metadata.setDescription((String) map.get("description"));
        
        return metadata;
    }
    
    /**
     * 创建用于签名的标准化字符串
     * 与builder端的createContentForHash方法保持一致，只包含非空字段
     */
    public String createSignatureContent() {
        StringBuilder content = new StringBuilder();

        // 按字母顺序添加非空字段（除了签名相关字段），与builder端逻辑一致
        if (contentHash != null && !contentHash.isEmpty()) {
            content.append("contentHash=").append(contentHash).append(";");
        }
        if (customProperties != null && !customProperties.isEmpty()) {
            content.append("customProperties=").append(customProperties.toString()).append(";");
        }
        if (description != null && !description.isEmpty()) {
            content.append("description=").append(description).append(";");
        }
        if (issueTime != null) {
            content.append("effectiveDate=").append(issueTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)).append(";");
        }
        if (expiryTime != null) {
            content.append("expiryTime=").append(expiryTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)).append(";");
        }
        if (features != null && !features.isEmpty()) {
            content.append("features=").append(features.toString()).append(";");
        }
        if (issueTime != null) {
            content.append("issueDate=").append(issueTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)).append(";");
        }
        if (issuer != null && !issuer.isEmpty()) {
            content.append("issuer=").append(issuer).append(";");
        }
        if (licenseType != null && !licenseType.isEmpty()) {
            content.append("licenseType=").append(licenseType).append(";");
        }
        if (limitations != null && !limitations.isEmpty()) {
            content.append("limitations=").append(limitations.toString()).append(";");
        }
        if (machineCode != null && !machineCode.isEmpty()) {
            content.append("machineCode=").append(machineCode).append(";");
        }
        if (pluginId != null && !pluginId.isEmpty()) {
            content.append("pluginId=").append(pluginId).append(";");
        }
        if (pluginName != null && !pluginName.isEmpty()) {
            content.append("pluginName=").append(pluginName).append(";");
        }
        if (pluginVersion != null && !pluginVersion.isEmpty()) {
            content.append("pluginVersion=").append(pluginVersion).append(";");
        }

        // 移除最后的分号
        if (content.length() > 0 && content.charAt(content.length() - 1) == ';') {
            content.setLength(content.length() - 1);
        }

        return content.toString();
    }
}
