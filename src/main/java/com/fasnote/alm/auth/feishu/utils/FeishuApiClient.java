package com.fasnote.alm.auth.feishu.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.polarion.core.util.logging.Logger;

/**
 * 飞书API客户端
 * 
 * 提供与飞书开放平台API交互的功能： 1. 获取用户信息 2. 处理Bearer Token认证 3. 处理HTTP请求和响应
 */
public class FeishuApiClient {

	@NotNull
	private static final Logger log = Logger.getLogger(FeishuApiClient.class);

	// 默认的连接超时时间（毫秒）
	private static final int DEFAULT_CONNECT_TIMEOUT = 10000;

	// 默认的读取超时时间（毫秒）
	private static final int DEFAULT_READ_TIMEOUT = 30000;

	private final int connectTimeout;
	private final int readTimeout;

	public FeishuApiClient() {
		this(DEFAULT_CONNECT_TIMEOUT, DEFAULT_READ_TIMEOUT);
	}

	public FeishuApiClient(int connectTimeout, int readTimeout) {
		this.connectTimeout = connectTimeout;
		this.readTimeout = readTimeout;
	}

	/**
	 * 获取飞书用户信息
	 * 
	 * @param userInfoUrl 用户信息API端点URL
	 * @param accessToken 访问令牌
	 * @return 用户信息JSON字符串
	 * @throws IOException 如果API调用失败
	 */
	@NotNull
	public String getUserInfo(@NotNull String userInfoUrl, @NotNull String accessToken) throws IOException {
		log.debug("Fetching user info from Feishu API: " + userInfoUrl);

		URL url = new URL(userInfoUrl);
		HttpURLConnection connection = (HttpURLConnection) url.openConnection();

		try {
			// 配置连接
			connection.setRequestMethod("GET");
			connection.setConnectTimeout(connectTimeout);
			connection.setReadTimeout(readTimeout);

			// 设置请求头
			connection.setRequestProperty("Authorization", "Bearer " + accessToken);
			connection.setRequestProperty("Accept", "application/json");
			connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");

			// 发送请求
			connection.connect();

			// 检查响应状态
			int responseCode = connection.getResponseCode();
			if (responseCode != HttpURLConnection.HTTP_OK) {
				String errorMessage = readErrorResponse(connection);
				throw new IOException("Feishu API request failed with status " + responseCode + ": " + errorMessage);
			}

			// 读取响应
			String response = readResponse(connection);
			log.debug("Feishu API response received: " + response);

			return response;

		} finally {
			connection.disconnect();
		}
	}



	/**
	 * 读取HTTP响应
	 */
	@NotNull
	private String readResponse(@NotNull HttpURLConnection connection) throws IOException {
		try (BufferedReader reader = new BufferedReader(
				new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {

			StringBuilder response = new StringBuilder();
			String line;
			while ((line = reader.readLine()) != null) {
				response.append(line);
			}
			return response.toString();
		}
	}

	/**
	 * 读取HTTP错误响应
	 */
	@NotNull
	private String readErrorResponse(@NotNull HttpURLConnection connection) {
		try {
			if (connection.getErrorStream() != null) {
				try (BufferedReader reader = new BufferedReader(
						new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {

					StringBuilder error = new StringBuilder();
					String line;
					while ((line = reader.readLine()) != null) {
						error.append(line);
					}
					return error.toString();
				}
			}
		} catch (IOException e) {
			log.warn("Failed to read error response", e);
		}

		return "Unknown error";
	}

	/**
	 * 验证访问令牌格式
	 */
	public static boolean isValidAccessToken(@Nullable String accessToken) {
		return accessToken != null && !accessToken.trim().isEmpty();
	}

	/**
	 * 构建Bearer认证头
	 */
	@NotNull
	public static String buildBearerAuthHeader(@NotNull String accessToken) {
		return "Bearer " + accessToken;
	}
}
