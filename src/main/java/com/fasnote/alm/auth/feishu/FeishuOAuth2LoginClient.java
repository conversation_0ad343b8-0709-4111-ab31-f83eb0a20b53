package com.fasnote.alm.auth.feishu;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.fasnote.alm.auth.feishu.utils.FeishuStateUtils;
import com.google.common.base.Joiner;
import com.polarion.core.util.logging.Logger;
import com.polarion.platform.internal.security.auth.oauth2.OAuth2LoginClient;
import com.polarion.platform.security.auth.IAuthenticationContext;
import com.polarion.platform.security.auth.impl.oauth2.OAuth2Configuration;
import com.polarion.platform.security.auth.impl.oauth2.OAuth2LoginHandler;

/**
 * 飞书OAuth2登录客户端
 * 
 * 包装原始的OAuth2LoginClient，添加飞书特定的处理逻辑： 1. 自定义redirect_uri构建逻辑 2. 集成飞书用户信息获取 3.
 * 处理飞书特定的认证参数
 */
@SuppressWarnings("restriction")
public class FeishuOAuth2LoginClient {

	@NotNull
	private static final Logger log = Logger.getLogger(FeishuOAuth2LoginClient.class);

	@NotNull
	private final OAuth2LoginClient delegate;

	@NotNull
	private final OAuth2Configuration configuration;

	public FeishuOAuth2LoginClient(@NotNull OAuth2Configuration configuration) {
		this.configuration = configuration;
		this.delegate = new OAuth2LoginClient(configuration);
	}

	/**
	 * 获取认证参数，使用标准OAuth2方式但在state中编码目标URL
	 */
	@NotNull
	public Map<String, String> getAuthenticationParams(@NotNull OAuth2LoginHandler loginHandler,
			@Nullable String targetUrl) {

		log.debug("getAuthenticationParams called with targetUrl: " + targetUrl);

		// 如果有目标URL，需要特殊处理state缓存
		if (targetUrl != null && !targetUrl.isEmpty()) {
			// 先获取标准OAuth2参数（不初始化缓存）
			Map<String, String> params = delegate.getAuthenticationParams(null, targetUrl);
			String originalState = params.get("state");

			// 生成增强的state参数
			String enhancedState = FeishuStateUtils.encodeTargetUrlInState(originalState, targetUrl);
			params.put("state", enhancedState);

			// 使用增强的state初始化缓存
			if (loginHandler != null) {
				delegate.initStateCache(enhancedState, loginHandler, targetUrl);
			}

			log.info("Enhanced state with target URL: " + targetUrl + ", original state: " + originalState
					+ ", enhanced state: " + enhancedState);
			return params;
		} else {
			// 没有目标URL，使用标准流程
			log.debug("No target URL found, using standard OAuth2 flow");
			return delegate.getAuthenticationParams(loginHandler, targetUrl);
		}
	}

	/**
	 * 获取认证参数的简化版本
	 */
	@NotNull
	public Map<String, String> getAuthenticationParams(@NotNull String targetUrl) {
		return getAuthenticationParams(null, targetUrl);
	}

	/**
	 * 获取认证URL
	 */
	@NotNull
	public String getAuthenticationUrl(@NotNull OAuth2LoginHandler loginHandler, @Nullable String targetUrl) {
		StringBuilder redirectUri = new StringBuilder(getAuthorizeUrl());
		redirectUri.append("?");
		Joiner.on("&").withKeyValueSeparator("=").appendTo(redirectUri,
				getAuthenticationParams(loginHandler, targetUrl));
		return redirectUri.toString();
	}

	/**
	 * 获取认证URL的简化版本
	 */
	@NotNull
	public String getAuthenticationUrl(@NotNull String targetUrl) {
		StringBuilder redirectUri = new StringBuilder(getAuthorizeUrl());
		redirectUri.append("?");
		Joiner.on("&").withKeyValueSeparator("=").appendTo(redirectUri, getAuthenticationParams(targetUrl));
		return redirectUri.toString();
	}

	// ========== 委托方法，确保完全兼容原版本 ==========

	/**
	 * 获取授权URL
	 */
	@NotNull
	public String getAuthorizeUrl() {
		return delegate.getAuthorizeUrl();
	}

	/**
	 * 交换授权码 - 使用飞书特定的登录处理器
	 */
	public boolean exchangeAuthorizationCode(@NotNull String authorizationCode, @NotNull HttpServletRequest request,
			@NotNull HttpServletResponse response, @NotNull IAuthenticationContext context) throws java.io.IOException {

		log.debug("Starting Feishu authorization code exchange");

		String originalState = request.getParameter("state");
		log.debug("Original state from request: " + originalState);

		// 如果是增强state，需要重新编码以匹配缓存中的key
		if (FeishuStateUtils.hasTargetUrl(originalState)) {
			String baseState = FeishuStateUtils.extractBaseState(originalState);
			String targetUrl = FeishuStateUtils.extractTargetUrlFromState(originalState);

			// 重新生成编码后的增强state以匹配缓存
			String encodedEnhancedState = FeishuStateUtils.encodeTargetUrlInState(baseState, targetUrl);
			log.debug("Re-encoded enhanced state for cache matching: " + encodedEnhancedState);

			// 创建包装request，返回编码后的增强state
			HttpServletRequestWrapper wrappedRequest = new HttpServletRequestWrapper(request) {
				@Override
				public String getParameter(String name) {
					if ("state".equals(name)) {
						return encodedEnhancedState;
					}
					return super.getParameter(name);
				}
			};

			// 使用包装的request执行授权码交换
			return delegate.exchangeAuthorizationCode(authorizationCode, wrappedRequest, response, context);
		} else {
			// 标准state，直接处理
			return delegate.exchangeAuthorizationCode(authorizationCode, request, response, context);
		}
	}

	/**
	 * 获取配置
	 */
	@NotNull
	public OAuth2Configuration getConfiguration() {
		return this.configuration;
	}
}
