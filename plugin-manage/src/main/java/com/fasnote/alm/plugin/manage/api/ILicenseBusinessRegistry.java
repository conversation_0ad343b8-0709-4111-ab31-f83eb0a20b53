package com.fasnote.alm.plugin.manage.api;

import java.util.List;
import java.util.Map;

/**
 * 许可证业务注册器接口
 *
 * 统一管理许可证实现和回退实现的注册，支持： 1. 手动注册（向后兼容） 2. 注解驱动的自动注册 3. 优先级管理 4. 服务映射查询
 */
public interface ILicenseBusinessRegistry {

	/**
	 * 注册统计信息
	 */
	class RegistrationStats {
		private final int licenseImplementationCount;
		private final int fallbackImplementationCount;
		private final int namedLicenseImplementationCount;
		private final int namedFallbackImplementationCount;

		public RegistrationStats(int licenseImplementationCount, int fallbackImplementationCount,
				int namedLicenseImplementationCount, int namedFallbackImplementationCount) {
			this.licenseImplementationCount = licenseImplementationCount;
			this.fallbackImplementationCount = fallbackImplementationCount;
			this.namedLicenseImplementationCount = namedLicenseImplementationCount;
			this.namedFallbackImplementationCount = namedFallbackImplementationCount;
		}

		public int getFallbackImplementationCount() {
			return fallbackImplementationCount;
		}

		public int getLicenseImplementationCount() {
			return licenseImplementationCount;
		}

		public int getNamedFallbackImplementationCount() {
			return namedFallbackImplementationCount;
		}

		public int getNamedLicenseImplementationCount() {
			return namedLicenseImplementationCount;
		}

		public int getTotalCount() {
			return licenseImplementationCount + fallbackImplementationCount + namedLicenseImplementationCount
					+ namedFallbackImplementationCount;
		}
	}

	/**
	 * 清空所有注册的映射
	 */
	void clear();

	/**
	 * 获取所有回退实现映射
	 *
	 * @return 回退实现映射的副本
	 */
	Map<Class<?>, Class<?>> getFallbackImplementationMappings();

	/**
	 * 获取所有许可证实现映射
	 *
	 * @return 许可证实现映射的副本
	 */
	Map<Class<?>, Class<?>> getLicenseImplementationMappings();

	/**
	 * 获取所有命名回退实现映射
	 *
	 * @return 命名回退实现映射的副本
	 */
	Map<String, Class<?>> getNamedFallbackImplementationMappings();

	/**
	 * 获取所有命名许可证实现映射
	 *
	 * @return 命名许可证实现映射的副本
	 */
	Map<String, Class<?>> getNamedLicenseImplementationMappings();

	/**
	 * 获取已注册的服务接口列表
	 *
	 * @return 服务接口列表
	 */
	List<Class<?>> getRegisteredServiceInterfaces();

	/**
	 * 获取注册统计信息
	 *
	 * @return 注册统计信息
	 */
	RegistrationStats getRegistrationStats();

	/**
	 * 检查是否有回退实现
	 *
	 * @param serviceInterface 服务接口类
	 * @return 是否有回退实现
	 */
	boolean hasFallbackImplementation(Class<?> serviceInterface);

	/**
	 * 检查是否有许可证实现
	 *
	 * @param serviceInterface 服务接口类
	 * @return 是否有许可证实现
	 */
	boolean hasLicenseImplementation(Class<?> serviceInterface);

	/**
	 * 注册回退实现
	 *
	 * @param <T>                    服务类型
	 * @param serviceInterface       服务接口类
	 * @param fallbackImplementation 回退实现类
	 */
	<T> void registerFallbackImplementation(Class<T> serviceInterface, Class<? extends T> fallbackImplementation);

	/**
	 * 注册命名回退实现
	 *
	 * @param <T>                    服务类型
	 * @param serviceInterface       服务接口类
	 * @param fallbackImplementation 回退实现类
	 * @param name                   服务名称
	 */
	<T> void registerFallbackImplementation(Class<T> serviceInterface, Class<? extends T> fallbackImplementation,
			String name);

	/**
	 * 注册许可证实现
	 *
	 * @param <T>                   服务类型
	 * @param serviceInterface      服务接口类
	 * @param licenseImplementation 许可证实现类
	 */
	<T> void registerLicenseImplementation(Class<T> serviceInterface, Class<? extends T> licenseImplementation);

	/**
	 * 注册命名许可证实现
	 *
	 * @param <T>                   服务类型
	 * @param serviceInterface      服务接口类
	 * @param licenseImplementation 许可证实现类
	 * @param name                  服务名称
	 */
	<T> void registerLicenseImplementation(Class<T> serviceInterface, Class<? extends T> licenseImplementation,
			String name);

	/**
	 * 扫描指定包路径下的注解实现类并自动注册
	 *
	 * @param packagePaths 包路径列表
	 */
	void scanAndRegister(String... packagePaths);
}
