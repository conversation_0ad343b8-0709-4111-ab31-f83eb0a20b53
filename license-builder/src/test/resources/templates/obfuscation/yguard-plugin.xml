            <!-- YGuard Obfuscation Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <taskdef name="yguard" classname="com.yworks.yguard.YGuardTask"/>
                                <yguard>
                                    <inoutpair in="${project.build.directory}/${project.build.finalName}.jar"
                                              out="${project.build.directory}/${project.build.finalName}-obfuscated.jar"/>
                                    <rename logfile="${project.build.directory}/yguard.log.xml">
                                        <keep>
                                            <class name="${bundle_activator}"/>
                                            {{#each exported_packages}}
                                            <class name="${package_name}.**"/>
                                            {{/each}}
                                        </keep>
                                    </rename>
                                </yguard>
                            </target>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.yworks</groupId>
                        <artifactId>yguard</artifactId>
                        <version>4.0.0</version>
                    </dependency>
                </dependencies>
            </plugin>
