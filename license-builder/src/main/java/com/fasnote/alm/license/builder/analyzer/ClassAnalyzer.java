package com.fasnote.alm.license.builder.analyzer;

import com.fasnote.alm.license.builder.core.BuildConfiguration;
import com.fasnote.alm.license.builder.model.ClassInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.DataInputStream;
import java.io.IOException;

/**
 * 类分析器
 * 使用字节码分析技术解析class文件
 */
public class ClassAnalyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(ClassAnalyzer.class);
    
    private final BuildConfiguration config;
    
    public ClassAnalyzer(BuildConfiguration config) {
        this.config = config;
    }
    
    /**
     * 分析类字节码
     * 
     * @param className 类名
     * @param classBytes 类字节码
     * @return 类信息
     * @throws IOException 分析异常
     */
    public ClassInfo analyzeClass(String className, byte[] classBytes) throws IOException {
        logger.debug("分析类: {}", className);
        
        ClassInfo classInfo = new ClassInfo();
        classInfo.setClassName(className);
        
        try {
            // 简化的class文件解析
            // 实际实现中应该使用ASM等专业的字节码分析库
            parseClassFile(classInfo, classBytes);
            
        } catch (Exception e) {
            logger.warn("解析类文件失败: {} - {}", className, e.getMessage());
            // 创建基本的类信息
            classInfo.setPublic(true); // 默认假设为公共类
        }
        
        return classInfo;
    }
    
    /**
     * 解析class文件
     * 这是一个简化的实现，实际应该使用ASM库
     */
    private void parseClassFile(ClassInfo classInfo, byte[] classBytes) throws IOException {
        DataInputStream dis = new DataInputStream(new ByteArrayInputStream(classBytes));
        
        // 验证魔数
        int magic = dis.readInt();
        if (magic != 0xCAFEBABE) {
            throw new IOException("无效的class文件魔数");
        }
        
        // 跳过版本号
        dis.readShort(); // minor_version
        dis.readShort(); // major_version
        
        // 读取常量池
        int constantPoolCount = dis.readShort();
        String[] constantPool = parseConstantPool(dis, constantPoolCount);
        
        // 读取访问标志
        int accessFlags = dis.readShort();
        parseAccessFlags(classInfo, accessFlags);
        
        // 读取类索引
        int thisClass = dis.readShort();
        int superClass = dis.readShort();
        
        if (superClass > 0 && superClass < constantPool.length && constantPool[superClass] != null) {
            classInfo.setSuperClass(constantPool[superClass]);
        }
        
        // 读取接口
        int interfacesCount = dis.readShort();
        for (int i = 0; i < interfacesCount; i++) {
            int interfaceIndex = dis.readShort();
            if (interfaceIndex > 0 && interfaceIndex < constantPool.length && constantPool[interfaceIndex] != null) {
                classInfo.getInterfaces().add(constantPool[interfaceIndex]);
            }
        }
        
        // 跳过字段
        int fieldsCount = dis.readShort();
        for (int i = 0; i < fieldsCount; i++) {
            skipField(dis);
        }
        
        // 跳过方法
        int methodsCount = dis.readShort();
        for (int i = 0; i < methodsCount; i++) {
            skipMethod(dis);
        }
        
        // 读取类属性，包括注解
        int attributesCount = dis.readShort();
        parseClassAttributes(dis, constantPool, classInfo, attributesCount);
        
        logger.debug("解析完成 - 类: {}, 接口数: {}, 注解数: {}", 
                    classInfo.getClassName(), interfacesCount, classInfo.getAnnotations().size());
    }
    
    /**
     * 解析常量池
     */
    private String[] parseConstantPool(DataInputStream dis, int count) throws IOException {
        String[] constantPool = new String[count];
        
        for (int i = 1; i < count; i++) {
            int tag = dis.readByte() & 0xFF; // 确保无符号读取
            
            switch (tag) {
                case 1: // CONSTANT_Utf8
                    int length = dis.readShort();
                    byte[] bytes = new byte[length];
                    dis.readFully(bytes);
                    constantPool[i] = new String(bytes, "UTF-8");
                    break;
                    
                case 7: // CONSTANT_Class
                    int nameIndex = dis.readShort();
                    // 类引用将在第二遍中解析
                    constantPool[i] = "CLASS_REF:" + nameIndex;
                    break;
                    
                case 8: // CONSTANT_String
                    dis.readShort();
                    break;
                    
                case 3: // CONSTANT_Integer
                case 4: // CONSTANT_Float
                case 9: // CONSTANT_Fieldref
                case 10: // CONSTANT_Methodref
                case 11: // CONSTANT_InterfaceMethodref
                case 12: // CONSTANT_NameAndType
                    dis.readInt();
                    break;
                    
                case 5: // CONSTANT_Long
                case 6: // CONSTANT_Double
                    dis.readLong();
                    i++; // long和double占用两个常量池位置
                    break;
                    
                case 15: // CONSTANT_MethodHandle (Java 7+)
                    dis.readByte(); // reference_kind
                    dis.readShort(); // reference_index
                    break;
                    
                case 16: // CONSTANT_MethodType (Java 7+)
                    dis.readShort(); // descriptor_index
                    break;
                    
                case 18: // CONSTANT_InvokeDynamic (Java 7+)
                    dis.readShort(); // bootstrap_method_attr_index
                    dis.readShort(); // name_and_type_index
                    break;
                    
                case 19: // CONSTANT_Module (Java 9+)
                case 20: // CONSTANT_Package (Java 9+)
                    dis.readShort(); // name_index
                    break;
                    
                default:
                    logger.warn("未知的常量池标签: {} (index: {})", tag, i);
                    // 尝试跳过，但这可能导致解析失败
                    throw new IOException("无法解析常量池 - 遇到未知标签: " + tag);
            }
        }
        
        // 第二遍：解析类引用
        for (int i = 1; i < count; i++) {
            if (constantPool[i] != null && constantPool[i].startsWith("CLASS_REF:")) {
                try {
                    int nameIndex = Integer.parseInt(constantPool[i].substring(10));
                    if (nameIndex > 0 && nameIndex < constantPool.length && constantPool[nameIndex] != null) {
                        constantPool[i] = constantPool[nameIndex].replace('/', '.');
                    }
                } catch (Exception e) {
                    logger.debug("无法解析类引用: {}", constantPool[i]);
                }
            }
        }
        
        return constantPool;
    }
    
    /**
     * 解析访问标志
     */
    private void parseAccessFlags(ClassInfo classInfo, int accessFlags) {
        classInfo.setPublic((accessFlags & 0x0001) != 0);
        classInfo.setAbstract((accessFlags & 0x0400) != 0);
        classInfo.setInterface((accessFlags & 0x0200) != 0);
        classInfo.setEnum((accessFlags & 0x4000) != 0);
        classInfo.setAnnotation((accessFlags & 0x2000) != 0);
    }
    
    /**
     * 检查类是否有指定注解
     */
    public boolean hasAnnotation(ClassInfo classInfo, String annotationName) {
        return classInfo.getAnnotations().contains(annotationName);
    }
    
    /**
     * 检查类是否实现了指定接口
     */
    public boolean implementsInterface(ClassInfo classInfo, String interfaceName) {
        return classInfo.getInterfaces().contains(interfaceName) ||
               classInfo.getSuperClasses().contains(interfaceName);
    }
    
    /**
     * 跳过字段
     */
    private void skipField(DataInputStream dis) throws IOException {
        dis.readShort(); // access_flags
        dis.readShort(); // name_index
        dis.readShort(); // descriptor_index
        
        int attributesCount = dis.readShort();
        for (int i = 0; i < attributesCount; i++) {
            skipAttribute(dis);
        }
    }
    
    /**
     * 跳过方法
     */
    private void skipMethod(DataInputStream dis) throws IOException {
        dis.readShort(); // access_flags
        dis.readShort(); // name_index
        dis.readShort(); // descriptor_index
        
        int attributesCount = dis.readShort();
        for (int i = 0; i < attributesCount; i++) {
            skipAttribute(dis);
        }
    }
    
    /**
     * 跳过属性
     */
    private void skipAttribute(DataInputStream dis) throws IOException {
        dis.readShort(); // attribute_name_index
        int length = dis.readInt();
        dis.skipBytes(length);
    }
    
    /**
     * 解析类属性，包括注解
     */
    private void parseClassAttributes(DataInputStream dis, String[] constantPool, 
                                     ClassInfo classInfo, int attributesCount) throws IOException {
        for (int i = 0; i < attributesCount; i++) {
            int nameIndex = dis.readShort();
            int length = dis.readInt();
            
            if (nameIndex > 0 && nameIndex < constantPool.length) {
                String attributeName = constantPool[nameIndex];
                
                if ("RuntimeVisibleAnnotations".equals(attributeName)) {
                    parseRuntimeVisibleAnnotations(dis, constantPool, classInfo);
                } else {
                    // 跳过其他属性
                    dis.skipBytes(length);
                }
            } else {
                dis.skipBytes(length);
            }
        }
    }
    
    /**
     * 解析运行时可见注解
     */
    private void parseRuntimeVisibleAnnotations(DataInputStream dis, String[] constantPool, 
                                               ClassInfo classInfo) throws IOException {
        int numAnnotations = dis.readShort();
        
        for (int i = 0; i < numAnnotations; i++) {
            int typeIndex = dis.readShort();
            if (typeIndex > 0 && typeIndex < constantPool.length) {
                String annotationType = constantPool[typeIndex];
                // 将描述符转换为类名格式
                if (annotationType.startsWith("L") && annotationType.endsWith(";")) {
                    annotationType = annotationType.substring(1, annotationType.length() - 1).replace('/', '.');
                }
                classInfo.getAnnotations().add(annotationType);
                logger.debug("找到注解: {}", annotationType);
            }
            
            // 跳过注解的元素值对
            int numElementValuePairs = dis.readShort();
            for (int j = 0; j < numElementValuePairs; j++) {
                dis.readShort(); // element_name_index
                skipElementValue(dis);
            }
        }
    }
    
    /**
     * 跳过元素值
     */
    private void skipElementValue(DataInputStream dis) throws IOException {
        int tag = dis.readByte();
        
        switch (tag) {
            case 'B':
            case 'C':
            case 'D':
            case 'F':
            case 'I':
            case 'J':
            case 'S':
            case 'Z':
            case 's':
            case 'c':
                dis.readShort(); // const_value_index
                break;
            case 'e':
                dis.readShort(); // type_name_index
                dis.readShort(); // const_name_index
                break;
            case '@':
                // 嵌套注解 - 简化处理
                dis.readShort(); // type_index
                int numPairs = dis.readShort();
                for (int i = 0; i < numPairs; i++) {
                    dis.readShort(); // element_name_index
                    skipElementValue(dis); // 递归跳过
                }
                break;
            case '[':
                int numValues = dis.readShort();
                for (int i = 0; i < numValues; i++) {
                    skipElementValue(dis); // 递归跳过数组元素
                }
                break;
        }
    }
}