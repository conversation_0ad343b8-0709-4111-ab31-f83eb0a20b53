package com.fasnote.alm.license.builder.analyzer;

import com.fasnote.alm.license.builder.core.BuildConfiguration;
import com.fasnote.alm.license.builder.model.ClassInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 接口分析器
 * 负责分析接口和实现类的关系
 */
public class InterfaceAnalyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(InterfaceAnalyzer.class);
    
    private final BuildConfiguration config;
    
    public InterfaceAnalyzer(BuildConfiguration config) {
        this.config = config;
    }
    
    /**
     * 查找接口的所有实现类
     * 
     * @param interfaceName 接口名称
     * @param allClasses 所有类信息
     * @return 实现类集合
     */
    public Set<String> findImplementations(String interfaceName, List<ClassInfo> allClasses) {
        logger.debug("查找接口 {} 的实现类", interfaceName);
        
        Set<String> implementations = allClasses.stream()
            .filter(classInfo -> !classInfo.isInterface() && !classInfo.isAbstract())
            .filter(classInfo -> implementsInterface(classInfo, interfaceName))
            .map(ClassInfo::getClassName)
            .collect(Collectors.toSet());
        
        logger.debug("接口 {} 的实现类: {}", interfaceName, implementations);
        return implementations;
    }
    
    /**
     * 查找类的所有接口
     * 
     * @param className 类名
     * @param allClasses 所有类信息
     * @return 接口集合
     */
    public Set<String> findInterfaces(String className, List<ClassInfo> allClasses) {
        ClassInfo classInfo = allClasses.stream()
            .filter(info -> info.getClassName().equals(className))
            .findFirst()
            .orElse(null);
        
        if (classInfo == null) {
            return Set.of();
        }
        
        return Set.copyOf(classInfo.getInterfaces());
    }
    
    /**
     * 检查类是否实现了指定接口（包括继承链）
     */
    private boolean implementsInterface(ClassInfo classInfo, String interfaceName) {
        // 直接实现
        if (classInfo.getInterfaces().contains(interfaceName)) {
            return true;
        }
        
        // 通过父类实现
        return classInfo.getSuperClasses().contains(interfaceName);
    }
    
    /**
     * 检查类是否是许可证相关的实现
     */
    public boolean isLicenseImplementation(ClassInfo classInfo) {
        // 检查是否实现了许可证接口
        for (String licenseInterface : config.getAnalysis().getLicenseInterfaces()) {
            if (implementsInterface(classInfo, licenseInterface)) {
                return true;
            }
        }
        
        // 检查是否有许可证注解
        for (String licenseAnnotation : config.getAnalysis().getLicenseAnnotations()) {
            if (classInfo.getAnnotations().contains(licenseAnnotation)) {
                return true;
            }
        }
        
        return false;
    }
}