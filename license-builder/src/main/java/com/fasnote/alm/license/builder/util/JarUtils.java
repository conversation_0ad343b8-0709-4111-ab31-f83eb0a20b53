package com.fasnote.alm.license.builder.util;


import com.fasnote.alm.license.builder.exception.BuildException;
import com.fasnote.alm.license.builder.model.BundleInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Enumeration;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.util.jar.Manifest;

/**
 * JAR文件处理工具类
 * 统一处理JAR文件的读取、解析等操作
 */
public class JarUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(JarUtils.class);
    
    /**
     * JAR条目处理器接口
     */
    @FunctionalInterface
    public interface JarEntryProcessor {
        /**
         * 处理JAR条目
         * @param entry JAR条目
         * @param inputStream 条目输入流
         * @throws Exception 处理异常
         */
        void process(JarEntry entry, InputStream inputStream) throws Exception;
    }
    
    /**
     * 处理JAR文件中的所有条目
     * @param jarPath JAR文件路径
     * @param processor 条目处理器
     * @throws BuildException 处理异常
     */
    public static void processJarEntries(Path jarPath, JarEntryProcessor processor) throws BuildException {
        if (!Files.exists(jarPath)) {
            throw new BuildException("JAR文件不存在: " + jarPath);
        }
        
        try (JarFile jarFile = new JarFile(jarPath.toFile())) {
            Enumeration<JarEntry> entries = jarFile.entries();
            
            while (entries.hasMoreElements()) {
                JarEntry entry = entries.nextElement();
                
                if (!entry.isDirectory()) {
                    try (InputStream inputStream = jarFile.getInputStream(entry)) {
                        processor.process(entry, inputStream);
                    } catch (Exception e) {
                        throw new BuildException("处理JAR条目失败: " + entry.getName(), e);
                    }
                }
            }
        } catch (IOException e) {
            throw new BuildException("读取JAR文件失败: " + jarPath, e);
        }
    }
    
    /**
     * 从JAR文件解析Bundle信息
     * @param jarPath JAR文件路径
     * @return Bundle信息
     * @throws BuildException 解析异常
     */
    public static BundleInfo parseBundleInfo(Path jarPath) throws BuildException {
        if (!Files.exists(jarPath)) {
            throw new BuildException("JAR文件不存在: " + jarPath);
        }
        
        try (JarFile jarFile = new JarFile(jarPath.toFile())) {
            Manifest manifest = jarFile.getManifest();
            if (manifest == null) {
                throw new BuildException("JAR文件中没有找到MANIFEST.MF: " + jarPath);
            }
            
            BundleInfo bundleInfo = ManifestParser.parseManifest(manifest);
            if (bundleInfo == null || bundleInfo.getSymbolicName() == null) {
                throw new BuildException("无法解析Bundle信息或缺少Bundle-SymbolicName: " + jarPath);
            }
            
            logger.debug("解析Bundle信息: {} v{}", bundleInfo.getSymbolicName(), bundleInfo.getVersion());
            return bundleInfo;
            
        } catch (IOException e) {
            throw new BuildException("读取JAR文件失败: " + jarPath, e);
        }
    }
    
    /**
     * 读取JAR文件的Manifest
     * @param jarPath JAR文件路径
     * @return Manifest对象
     * @throws BuildException 读取异常
     */
    public static Manifest readManifest(Path jarPath) throws BuildException {
        if (!Files.exists(jarPath)) {
            throw new BuildException("JAR文件不存在: " + jarPath);
        }
        
        try (JarFile jarFile = new JarFile(jarPath.toFile())) {
            Manifest manifest = jarFile.getManifest();
            if (manifest == null) {
                throw new BuildException("JAR文件中没有找到MANIFEST.MF: " + jarPath);
            }
            return manifest;
        } catch (IOException e) {
            throw new BuildException("读取MANIFEST.MF失败: " + jarPath, e);
        }
    }
    
    /**
     * 检查JAR文件中是否包含指定的条目
     * @param jarPath JAR文件路径
     * @param entryName 条目名称
     * @return 如果包含返回true
     * @throws BuildException 检查异常
     */
    public static boolean containsEntry(Path jarPath, String entryName) throws BuildException {
        if (!Files.exists(jarPath)) {
            throw new BuildException("JAR文件不存在: " + jarPath);
        }
        
        try (JarFile jarFile = new JarFile(jarPath.toFile())) {
            return jarFile.getJarEntry(entryName) != null;
        } catch (IOException e) {
            throw new BuildException("检查JAR条目失败: " + jarPath, e);
        }
    }
    
    /**
     * 读取JAR文件中的指定条目内容
     * @param jarPath JAR文件路径
     * @param entryName 条目名称
     * @return 条目内容字节数组
     * @throws BuildException 读取异常
     */
    public static byte[] readEntry(Path jarPath, String entryName) throws BuildException {
        if (!Files.exists(jarPath)) {
            throw new BuildException("JAR文件不存在: " + jarPath);
        }
        
        try (JarFile jarFile = new JarFile(jarPath.toFile())) {
            JarEntry entry = jarFile.getJarEntry(entryName);
            if (entry == null) {
                throw new BuildException("JAR文件中不存在条目: " + entryName);
            }
            
            try (InputStream inputStream = jarFile.getInputStream(entry)) {
                return inputStream.readAllBytes();
            }
        } catch (IOException e) {
            throw new BuildException("读取JAR条目失败: " + entryName, e);
        }
    }
    
    /**
     * 统计JAR文件中的class文件数量
     * @param jarPath JAR文件路径
     * @return class文件数量
     * @throws BuildException 统计异常
     */
    public static int countClassFiles(Path jarPath) throws BuildException {
        final int[] count = {0};
        
        processJarEntries(jarPath, (entry, inputStream) -> {
            if (entry.getName().endsWith(".class") && !entry.isDirectory()) {
                count[0]++;
            }
        });
        
        return count[0];
    }
    
    /**
     * 检查是否为有效的JAR文件
     * @param jarPath 文件路径
     * @return 如果是有效JAR文件返回true
     */
    public static boolean isValidJarFile(Path jarPath) {
        if (!Files.exists(jarPath)) {
            return false;
        }
        
        try (JarFile jarFile = new JarFile(jarPath.toFile())) {
            // 尝试读取至少一个条目来验证文件格式
            Enumeration<JarEntry> entries = jarFile.entries();
            return entries.hasMoreElements();
        } catch (IOException e) {
            logger.debug("无效的JAR文件: {}", jarPath, e);
            return false;
        }
    }
    
    /**
     * 获取JAR文件大小（字节）
     * @param jarPath JAR文件路径
     * @return 文件大小
     * @throws BuildException 获取异常
     */
    public static long getJarFileSize(Path jarPath) throws BuildException {
        if (!Files.exists(jarPath)) {
            throw new BuildException("JAR文件不存在: " + jarPath);
        }
        
        try {
            return Files.size(jarPath);
        } catch (IOException e) {
            throw new BuildException("获取JAR文件大小失败: " + jarPath, e);
        }
    }
}