package com.fasnote.alm.license.builder.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * RSA密钥管理器 (Builder版本)
 * 负责RSA密钥的生成、加载、存储和加密解密操作
 * 
 * 功能特性：
 * 1. RSA-2048密钥对生成
 * 2. 私钥安全存储和加载
 * 3. 公钥硬编码备份
 * 4. 密钥版本管理
 * 5. RSA加密解密操作
 */
public class RSAKeyManager {
    
    private static final Logger logger = LoggerFactory.getLogger(RSAKeyManager.class);
    private static final String LOG_PREFIX = "[RSAKeyManager] ";
    
    private static final String RSA_ALGORITHM = "RSA";
    private static final String RSA_TRANSFORMATION = "RSA/ECB/PKCS1Padding";
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
    private static final int RSA_KEY_SIZE = 2048;

    // RSA加密块大小限制 (2048位密钥 - PKCS1填充 = 245字节)
    private static final int RSA_ENCRYPT_BLOCK_SIZE = 245;
    // RSA解密块大小 (2048位密钥 = 256字节)
    private static final int RSA_DECRYPT_BLOCK_SIZE = 256;
    
    // 当前密钥版本
    private static final String CURRENT_KEY_VERSION = "v1";
    
    // 默认密钥路径
    private static final String DEFAULT_KEY_PATH = System.getProperty("user.home") + "/opt/license";
    
    private PrivateKey privateKey;
    private PublicKey publicKey;
    private String keyVersion;
    private String keyPath;

    public RSAKeyManager() {
        this(null);
    }

    public RSAKeyManager(String customKeyPath) {
        this.keyPath = (customKeyPath != null && !customKeyPath.trim().isEmpty()) ? customKeyPath.trim() : DEFAULT_KEY_PATH;
        loadKeys();
    }
    
    /**
     * 加载RSA密钥对
     */
    private void loadKeys() {
        try {
            logger.info(LOG_PREFIX + "开始从路径加载RSA密钥对: " + keyPath);

            // 1. 尝试从指定路径加载私钥
            privateKey = loadPrivateKeyFromPath();

            // 2. 尝试从指定路径加载公钥
            publicKey = loadPublicKeyFromPath();

            if (privateKey != null && publicKey != null) {
                keyVersion = CURRENT_KEY_VERSION;
                logger.info(LOG_PREFIX + "成功加载RSA密钥对，版本: " + keyVersion);
            } else {
                throw new SecurityException("无法从路径加载完整的RSA密钥对: " + keyPath);
            }

        } catch (Exception e) {
            logger.error(LOG_PREFIX + "RSA密钥加载失败", e);
            throw new SecurityException("RSA密钥加载失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从指定路径加载私钥
     */
    private PrivateKey loadPrivateKeyFromPath() {
        try {
            String privateKeyPath = keyPath + "/license-private.key";
            File keyFile = new File(privateKeyPath);

            if (!keyFile.exists() || !keyFile.isFile() || !keyFile.canRead()) {
                logger.error(LOG_PREFIX + "私钥文件不存在或无法读取: " + privateKeyPath);
                return null;
            }

            logger.info(LOG_PREFIX + "从路径加载私钥: " + privateKeyPath);

            byte[] keyBytes = Files.readAllBytes(Paths.get(privateKeyPath));
            String keyContent = new String(keyBytes, "UTF-8").trim();

            // 移除PEM格式的头尾标记
            keyContent = keyContent.replaceAll("-----BEGIN PRIVATE KEY-----", "")
                                 .replaceAll("-----END PRIVATE KEY-----", "")
                                 .replaceAll("\\s", "");

            byte[] decodedKey = Base64.getDecoder().decode(keyContent);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodedKey);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);

            return keyFactory.generatePrivate(keySpec);

        } catch (Exception e) {
            logger.error(LOG_PREFIX + "从路径加载私钥失败", e);
            return null;
        }
    }
    
    /**
     * 从指定路径加载公钥
     */
    private PublicKey loadPublicKeyFromPath() {
        try {
            String publicKeyPath = keyPath + "/license-public.key";
            File keyFile = new File(publicKeyPath);

            if (!keyFile.exists() || !keyFile.isFile() || !keyFile.canRead()) {
                logger.error(LOG_PREFIX + "公钥文件不存在或无法读取: " + publicKeyPath);
                return null;
            }

            logger.info(LOG_PREFIX + "从路径加载公钥: " + publicKeyPath);

            byte[] keyBytes = Files.readAllBytes(Paths.get(publicKeyPath));
            String keyContent = new String(keyBytes, "UTF-8").trim();

            // 移除PEM格式的头尾标记
            keyContent = keyContent.replaceAll("-----BEGIN PUBLIC KEY-----", "")
                                 .replaceAll("-----END PUBLIC KEY-----", "")
                                 .replaceAll("\\s", "");

            byte[] decodedKey = Base64.getDecoder().decode(keyContent);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decodedKey);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);

            return keyFactory.generatePublic(keySpec);

        } catch (Exception e) {
            logger.error(LOG_PREFIX + "从路径加载公钥失败", e);
            return null;
        }
    }
    

    
    /**
     * 使用RSA公钥分块加密数据
     *
     * @param data 要加密的数据
     * @return 加密后的数据
     */
    public byte[] encrypt(byte[] data) {
        if (publicKey == null) {
            throw new SecurityException("公钥未加载，无法执行加密操作");
        }

        try {
            Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);

            return encryptInBlocks(cipher, data, RSA_ENCRYPT_BLOCK_SIZE);
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "RSA加密失败", e);
            throw new SecurityException("RSA加密失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 使用RSA私钥分块解密数据
     *
     * @param encryptedData 加密的数据
     * @return 解密后的数据
     */
    public byte[] decrypt(byte[] encryptedData) {
        if (privateKey == null) {
            throw new SecurityException("私钥未加载，无法执行解密操作");
        }

        try {
            Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, privateKey);

            return decryptInBlocks(cipher, encryptedData, RSA_DECRYPT_BLOCK_SIZE);
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "RSA解密失败", e);
            throw new SecurityException("RSA解密失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 使用RSA私钥对数据进行数字签名
     * 
     * @param data 要签名的数据
     * @return 数字签名
     */
    public byte[] sign(byte[] data) {
        if (privateKey == null) {
            throw new SecurityException("私钥未加载，无法执行签名操作");
        }
        
        try {
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initSign(privateKey);
            signature.update(data);
            return signature.sign();
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "数字签名失败", e);
            throw new SecurityException("数字签名失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 使用RSA公钥验证数字签名
     * 
     * @param data 原始数据
     * @param signatureBytes 数字签名
     * @return 验证结果
     */
    public boolean verify(byte[] data, byte[] signatureBytes) {
        if (publicKey == null) {
            throw new SecurityException("公钥未加载，无法执行签名验证");
        }
        
        try {
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initVerify(publicKey);
            signature.update(data);
            return signature.verify(signatureBytes);
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "签名验证失败", e);
            return false;
        }
    }
    
    /**
     * 获取当前密钥版本
     */
    public String getKeyVersion() {
        return keyVersion;
    }
    
    /**
     * 检查是否有私钥（用于判断是否可以执行签名和解密操作）
     */
    public boolean hasPrivateKey() {
        return privateKey != null;
    }
    
    /**
     * 检查是否有公钥（用于判断是否可以执行加密和验证操作）
     */
    public boolean hasPublicKey() {
        return publicKey != null;
    }
    
    /**
     * 获取公钥的Base64编码（用于配置和调试）
     */
    public String getPublicKeyBase64() {
        if (publicKey == null) {
            return null;
        }
        return Base64.getEncoder().encodeToString(publicKey.getEncoded());
    }

    /**
     * 分块加密数据
     *
     * @param cipher 已初始化的加密器
     * @param data 要加密的数据
     * @param blockSize 块大小
     * @return 加密后的数据
     */
    private byte[] encryptInBlocks(Cipher cipher, byte[] data, int blockSize) throws Exception {
        int dataLength = data.length;
        int totalBlocks = (dataLength + blockSize - 1) / blockSize;

        // 计算输出数据大小
        int outputBlockSize = cipher.getOutputSize(blockSize);
        byte[] result = new byte[totalBlocks * outputBlockSize];

        int resultOffset = 0;
        for (int i = 0; i < dataLength; i += blockSize) {
            int currentBlockSize = Math.min(blockSize, dataLength - i);
            byte[] encryptedBlock = cipher.doFinal(data, i, currentBlockSize);

            System.arraycopy(encryptedBlock, 0, result, resultOffset, encryptedBlock.length);
            resultOffset += encryptedBlock.length;
        }

        // 如果实际输出小于预期，调整数组大小
        if (resultOffset < result.length) {
            byte[] trimmedResult = new byte[resultOffset];
            System.arraycopy(result, 0, trimmedResult, 0, resultOffset);
            return trimmedResult;
        }

        return result;
    }

    /**
     * 分块解密数据
     *
     * @param cipher 已初始化的解密器
     * @param encryptedData 加密的数据
     * @param blockSize 块大小
     * @return 解密后的数据
     */
    private byte[] decryptInBlocks(Cipher cipher, byte[] encryptedData, int blockSize) throws Exception {
        int dataLength = encryptedData.length;

        if (dataLength % blockSize != 0) {
            throw new IllegalArgumentException("加密数据长度不是块大小的整数倍");
        }

        int totalBlocks = dataLength / blockSize;
        byte[] result = new byte[totalBlocks * (blockSize - 11)]; // 估算解密后大小

        int resultOffset = 0;
        for (int i = 0; i < dataLength; i += blockSize) {
            byte[] decryptedBlock = cipher.doFinal(encryptedData, i, blockSize);

            System.arraycopy(decryptedBlock, 0, result, resultOffset, decryptedBlock.length);
            resultOffset += decryptedBlock.length;
        }

        // 调整数组大小到实际解密数据长度
        byte[] trimmedResult = new byte[resultOffset];
        System.arraycopy(result, 0, trimmedResult, 0, resultOffset);

        return trimmedResult;
    }
}
