<?xml version="1.0" encoding="UTF-8"?>
<!-- 
Tycho集成配置模板
用于将许可证构建器集成到Tycho构建流程中
-->
<project>
    <!-- 许可证构建器插件配置 -->
    <plugin>
        <groupId>com.fasnote.alm</groupId>
        <artifactId>license-builder</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <executions>
            <!-- 在initialize阶段生成Tycho配置 -->
            <execution>
                <id>generate-tycho-config</id>
                <phase>initialize</phase>
                <goals>
                    <goal>tycho-config</goal>
                </goals>
                <configuration>
                    <obfuscationTool>YGUARD</obfuscationTool>
                    <obfuscationLevel>STANDARD</obfuscationLevel>
                    <signingEnabled>${license.signing.enabled}</signingEnabled>
                    <keystorePath>${license.signing.keystore}</keystorePath>
                    <signingAlias>${license.signing.alias}</signingAlias>
                    <polarionHome>${polarion.home}</polarionHome>
                </configuration>
            </execution>

            <!-- 在package阶段执行许可证生成 -->
            <execution>
                <id>generate-license</id>
                <phase>package</phase>
                <goals>
                    <goal>license-build</goal>
                </goals>
                <configuration>
                    <!-- 使用Tycho构建的JAR作为输入 -->
                    <inputJar>${project.build.directory}/${project.build.finalName}.jar</inputJar>
                    <outputDir>${project.build.directory}/license</outputDir>
                    <buildMode>license</buildMode>

                    <!-- 类分离配置 -->
                    <separationEnabled>true</separationEnabled>
                    <separationStrategy>AUTO</separationStrategy>

                    <!-- 加密配置 -->
                    <encryptionEnabled>true</encryptionEnabled>
                    <encryptionAlgorithm>AES_256_GCM</encryptionAlgorithm>
                    <keyPath>${license.keyPath}</keyPath>

                    <!-- 验证配置 -->
                    <validationEnabled>true</validationEnabled>
                </configuration>
            </execution>
        </executions>
    </plugin>
    
    <!-- 属性配置示例 -->
    <properties>
        <!-- 许可证构建相关属性 -->
        <license.keyPath>${user.home}/.fasnote/keys/rsa-private.pem</license.keyPath>

        <!-- Tycho配置相关属性 -->
        <license.signing.enabled>false</license.signing.enabled>
        <license.signing.keystore>${user.home}/.fasnote/keystore.jks</license.signing.keystore>
        <license.signing.alias>fasnote</license.signing.alias>
        <polarion.home>/opt/polarion</polarion.home>

        <!-- 许可证生成配置 -->
        <license.separation.strategy>AUTO</license.separation.strategy>
        <license.encryption.algorithm>AES_256_GCM</license.encryption.algorithm>
    </properties>
</project>
