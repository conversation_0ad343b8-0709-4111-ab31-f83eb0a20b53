<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <!-- 统计卡片 -->
      <el-col :span="6" v-for="stat in statistics" :key="stat.title">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon" :style="{ backgroundColor: stat.color }">
              <el-icon :size="24">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 系统信息 -->
      <el-col :span="12">
        <el-card title="系统信息" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>系统信息</span>
              <el-button type="text" @click="loadSystemInfo" :loading="loading">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </template>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="框架版本">
              {{ systemInfo.frameworkVersion || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="Java版本">
              {{ systemInfo.javaVersion || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="操作系统">
              {{ systemInfo.osName || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="配置状态">
              <el-tag :type="systemInfo.configurationLoaded ? 'success' : 'danger'">
                {{ systemInfo.configurationLoaded ? '已加载' : '未加载' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>

      <!-- 快速操作 -->
      <el-col :span="12">
        <el-card title="快速操作" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>快速操作</span>
            </div>
          </template>
          <div class="quick-actions">
            <el-button type="primary" @click="$router.push('/plugins')" size="large">
              <el-icon><List /></el-icon>
              查看所有插件
            </el-button>
            <el-button type="success" @click="refreshAllLicenses" size="large" :loading="refreshing">
              <el-icon><Refresh /></el-icon>
              刷新许可证
            </el-button>
            <el-button type="info" @click="loadSystemInfo" size="large" :loading="loading">
              <el-icon><Monitor /></el-icon>
              更新统计
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { pluginApi } from '../api'
import { 
  Setting, 
  Document, 
  Check, 
  Warning, 
  Refresh, 
  List, 
  Monitor 
} from '@element-plus/icons-vue'

export default {
  name: 'Dashboard',
  components: {
    Setting,
    Document,
    Check,
    Warning,
    Refresh,
    List,
    Monitor
  },
  setup() {
    const loading = ref(false)
    const refreshing = ref(false)
    const statistics = ref([
      { title: '总插件数', value: 0, icon: 'Setting', color: '#409EFF' },
      { title: '有效许可证', value: 0, icon: 'Check', color: '#67C23A' },
      { title: '无效许可证', value: 0, icon: 'Warning', color: '#F56C6C' },
      { title: '总服务数', value: 0, icon: 'Document', color: '#E6A23C' }
    ])
    const systemInfo = ref({})

    const loadSystemInfo = async () => {
      loading.value = true
      try {
        const data = await pluginApi.getFrameworkStatistics()
        systemInfo.value = data
        
        // 更新统计数据
        statistics.value[0].value = data.totalLicenses || 0
        statistics.value[3].value = data.totalServices || 0
        
        ElMessage.success('系统信息更新成功')
      } catch (error) {
        console.error('获取系统信息失败:', error)
        ElMessage.error('获取系统信息失败')
      } finally {
        loading.value = false
      }
    }

    const refreshAllLicenses = async () => {
      refreshing.value = true
      try {
        await pluginApi.refreshAllLicenses()
        ElMessage.success('许可证刷新成功')
        // 刷新后重新加载统计信息
        await loadSystemInfo()
      } catch (error) {
        console.error('刷新许可证失败:', error)
        ElMessage.error('刷新许可证失败')
      } finally {
        refreshing.value = false
      }
    }

    // 监听全局刷新事件
    const handleGlobalRefresh = () => {
      loadSystemInfo()
    }

    onMounted(() => {
      loadSystemInfo()
      window.addEventListener('refresh-data', handleGlobalRefresh)
    })

    return {
      loading,
      refreshing,
      statistics,
      systemInfo,
      loadSystemInfo,
      refreshAllLicenses
    }
  }
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.quick-actions .el-button {
  justify-content: flex-start;
}
</style>
