package com.fasnote.alm.plugin.manage.security;

import com.fasnote.alm.plugin.manage.exception.SecurityException;
import com.polarion.platform.internal.LicenseSecurityManager;

/**
 * Polarion 机器码提供者 使用 Polarion 内置的机器码获取机制
 */
public class PolarionMachineCodeProvider implements MachineCodeProvider {

	@Override
	public String getMachineCode() throws Exception {
		// 直接使用Polarion的机器码
		String polarionMachineCode = LicenseSecurityManager.getLicenseManager().getLicenseStore()
				.getCurrentHardwareKey();

		if (polarionMachineCode != null && !polarionMachineCode.trim().isEmpty()) {
			return polarionMachineCode.trim();
		} else {
			throw new SecurityException("无法获取Polarion机器码");
		}
	}
}
