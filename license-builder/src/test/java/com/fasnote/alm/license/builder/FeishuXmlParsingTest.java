package com.fasnote.alm.license.builder;

import com.fasnote.alm.license.builder.model.BundleInfo;
import com.fasnote.alm.license.builder.template.TemplateContext;
import com.fasnote.alm.license.builder.util.BundleInfoResolver;
import com.fasnote.alm.license.builder.util.XmlConfigParser;
import org.junit.jupiter.api.Test;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

/**
 * 飞书插件XML解析测试
 * 验证新的XML配置解析功能
 */
public class FeishuXmlParsingTest {

    @Test
    void testFeishuPluginXmlParsing() throws Exception {
        // 使用测试资源目录中的插件
        Path feishuPluginDir = Paths.get("src/test/resources/test-plugin");
        
        System.out.println("=== 飞书插件XML配置解析测试 ===");
        System.out.println("插件目录: " + feishuPluginDir.toAbsolutePath());
        
        // 1. 直接测试XML配置解析
        System.out.println("\n1. 直接XML配置解析:");
        XmlConfigParser.XmlConfigInfo xmlConfig = XmlConfigParser.scanXmlConfigs(feishuPluginDir);
        
        System.out.println("发现的XML配置类总数: " + xmlConfig.getAllClasses().size());
        
        System.out.println("\nWeb.xml类 (" + xmlConfig.getWebXmlClasses().size() + "个):");
        xmlConfig.getWebXmlClasses().forEach(className -> 
            System.out.println("  - " + className));
        
        System.out.println("\nHiveMind类 (" + xmlConfig.getHiveModuleClasses().size() + "个):");
        xmlConfig.getHiveModuleClasses().forEach(className -> 
            System.out.println("  - " + className));
        
        System.out.println("\nPlugin.xml类 (" + xmlConfig.getPluginXmlClasses().size() + "个):");
        xmlConfig.getPluginXmlClasses().forEach(className -> 
            System.out.println("  - " + className));
        
        // 2. 测试BundleInfo集成
        System.out.println("\n2. BundleInfo集成测试:");
        BundleInfo bundleInfo = BundleInfoResolver.getBundleInfoFromDirectory(feishuPluginDir);
        
        System.out.println("Bundle符号名: " + bundleInfo.getSymbolicName());
        System.out.println("Bundle版本: " + bundleInfo.getVersion());
        
        if (bundleInfo.getXmlConfigInfo() != null) {
            System.out.println("XML配置信息已集成到BundleInfo中");
            System.out.println("集成的XML配置类数量: " + bundleInfo.getXmlConfigInfo().getAllClasses().size());
        } else {
            System.out.println("警告: XML配置信息未集成到BundleInfo中");
        }
        
        // 3. 测试模板上下文生成
        System.out.println("\n3. 模板上下文生成测试:");
        TemplateContext context = TemplateContext.fromBundleInfo(bundleInfo);
        
        // 检查XML配置类是否在模板上下文中
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> xmlConfigClasses = (List<Map<String, Object>>) context.get("xml_config_classes");
        
        if (xmlConfigClasses != null) {
            System.out.println("模板上下文中的XML配置类 (" + xmlConfigClasses.size() + "个):");
            xmlConfigClasses.forEach(classInfo -> 
                System.out.println("  - " + classInfo.get("class_name") + " (包: " + classInfo.get("package_name") + ")"));
        } else {
            System.out.println("警告: 模板上下文中没有XML配置类信息");
        }
        
        // 4. 生成ProGuard规则示例
        System.out.println("\n4. ProGuard规则生成示例:");
        if (xmlConfigClasses != null && !xmlConfigClasses.isEmpty()) {
            System.out.println("<!-- XML配置文件引用类保护 -->");
            xmlConfigClasses.forEach(classInfo ->
                System.out.println("-keep class " + classInfo.get("class_name") + " { *; }"));
        }

        System.out.println("\n=== 测试完成 ===");
    }
}
