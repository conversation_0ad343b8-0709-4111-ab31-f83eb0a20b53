package com.fasnote.alm.plugin.manage.web.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.monitor.FrameworkMonitor;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;
import com.fasnote.alm.plugin.manage.config.LicenseConfiguration;
import com.fasnote.alm.plugin.manage.web.dto.LicenseInfoDto;
import com.fasnote.alm.plugin.manage.web.dto.LicenseUpdateRequest;
import com.fasnote.alm.plugin.manage.web.dto.LicenseUpdateResponse;
import com.fasnote.alm.plugin.manage.web.dto.PluginInfoDto;
import com.fasnote.alm.plugin.manage.web.service.PluginManagementService;

/**
 * 插件管理服务实现类 直接使用内部组件
 */
public class PluginManagementServiceImpl implements PluginManagementService {

	private static final Logger logger = LoggerFactory.getLogger(PluginManagementServiceImpl.class);
	private static final String LOG_PREFIX = "[PluginManagementService] ";

	private final LicenseManager licenseManager;
	private final FrameworkMonitor frameworkMonitor;

	public PluginManagementServiceImpl() {
		// 创建依赖组件
		SecurityValidator securityValidator = new SecurityValidator();
		this.licenseManager = new LicenseManager(securityValidator);
		this.frameworkMonitor = new FrameworkMonitor(licenseManager, LicenseConfiguration.getInstance());
	}

	public PluginManagementServiceImpl(LicenseManager licenseManager, FrameworkMonitor frameworkMonitor) {
		this.licenseManager = licenseManager;
		this.frameworkMonitor = frameworkMonitor;
	}

	@Override
	public List<PluginInfoDto> getAllPlugins() {
		logger.info(LOG_PREFIX + "获取所有插件列表");

		List<PluginInfoDto> plugins = new ArrayList<>();

		try {
			// 从许可证管理器获取已注册的插件ID
			List<String> registeredPluginIds = licenseManager.getRegisteredPluginIds();

			for (String pluginId : registeredPluginIds) {
				try {
					// 验证许可证
					boolean licenseValid = licenseManager.hasValidLicense(pluginId);
					String licenseStatus = getLicenseStatus(pluginId);

					// 创建插件信息DTO
					PluginInfoDto plugin = new PluginInfoDto(pluginId, getPluginDisplayName(pluginId),
							getPluginVersion(pluginId), "ACTIVE", // 默认状态
							licenseValid, licenseStatus, getPluginDescription(pluginId));

					plugins.add(plugin);

				} catch (Exception e) {
					logger.warn(LOG_PREFIX + "获取插件信息失败: " + pluginId, e);
					// 创建错误状态的插件信息
					PluginInfoDto plugin = new PluginInfoDto(pluginId, pluginId, "未知", "ERROR", false,
							"获取状态失败: " + e.getMessage(), "插件信息获取失败");
					plugins.add(plugin);
				}
			}

			logger.info(LOG_PREFIX + "成功获取 " + plugins.size() + " 个插件信息");

		} catch (Exception e) {
			logger.error(LOG_PREFIX + "获取插件列表失败", e);
		}

		return plugins;
	}

	@Override
	public Map<String, Object> getFrameworkStatistics() {
		logger.info(LOG_PREFIX + "获取框架统计信息");

		try {
			return frameworkMonitor.getFrameworkStatistics();
		} catch (Exception e) {
			logger.error(LOG_PREFIX + "获取框架统计信息失败", e);
			Map<String, Object> errorStats = new HashMap<>();
			errorStats.put("error", "获取统计信息失败: " + e.getMessage());
			return errorStats;
		}
	}

	private Map<String, Object> getLicenseDetails(String pluginId) {
		// 尝试获取更详细的许可证信息
		// 这里可以扩展以获取更多信息
		Map<String, Object> details = new HashMap<>();
		details.put("type", "STANDARD");
		details.put("licensee", "未知");
		details.put("features", new HashMap<>());
		return details;
	}

	private String getPluginDescription(String pluginId) {
		// 可以从配置或其他地方获取插件描述
		return "插件: " + pluginId;
	}

	// 辅助方法
	private String getPluginDisplayName(String pluginId) {
		// 可以从配置或其他地方获取插件显示名称
		return pluginId.replace(".", " ").replace("-", " ");
	}

	@Override
	public LicenseInfoDto getPluginLicense(String pluginId) {
		logger.info(LOG_PREFIX + "获取插件许可证信息: " + pluginId);

		try {
			// 验证许可证
			boolean licenseValid = licenseManager.hasValidLicense(pluginId);
			String status = getLicenseStatus(pluginId);

			// 构建许可证信息
			LicenseInfoDto licenseInfo = new LicenseInfoDto();
			licenseInfo.setPluginId(pluginId);
			licenseInfo.setValid(licenseValid);
			licenseInfo.setStatus(status);

			if (!licenseValid) {
				licenseInfo.setErrorMessage("许可证验证失败");
			}

			// 尝试获取更多许可证详细信息
			try {
				Map<String, Object> licenseDetails = getLicenseDetails(pluginId);
				if (licenseDetails != null) {
					licenseInfo.setLicenseType((String) licenseDetails.get("type"));
					licenseInfo.setLicensee((String) licenseDetails.get("licensee"));
					Object features = licenseDetails.get("features");
					if (features instanceof Map) {
						@SuppressWarnings("unchecked")
						Map<String, Object> featuresMap = (Map<String, Object>) features;
						licenseInfo.setFeatures(featuresMap);
					}
				}
			} catch (Exception e) {
				logger.debug(LOG_PREFIX + "获取许可证详细信息失败: " + pluginId, e);
			}

			return licenseInfo;

		} catch (Exception e) {
			logger.error(LOG_PREFIX + "获取许可证信息失败: " + pluginId, e);

			LicenseInfoDto errorInfo = new LicenseInfoDto();
			errorInfo.setPluginId(pluginId);
			errorInfo.setValid(false);
			errorInfo.setStatus("ERROR");
			errorInfo.setErrorMessage("获取许可证信息失败: " + e.getMessage());
			return errorInfo;
		}
	}

	private String getPluginVersion(String pluginId) {
		// 可以从Bundle信息或其他地方获取版本信息
		return "1.0.0";
	}

	@Override
	public void refreshAllLicenses() {
		logger.info(LOG_PREFIX + "刷新所有许可证状态");

		try {
			licenseManager.refreshAllLicenses();
			logger.info(LOG_PREFIX + "许可证状态刷新完成");
		} catch (Exception e) {
			logger.error(LOG_PREFIX + "刷新许可证状态失败", e);
		}
	}

	@Override
	public LicenseUpdateResponse updatePluginLicense(String pluginId, LicenseUpdateRequest request) {
		logger.info(LOG_PREFIX + "更新插件许可证: " + pluginId);

		try {
			// 验证请求参数
			if (request.getLicenseContent() == null || request.getLicenseContent().trim().isEmpty()) {
				return new LicenseUpdateResponse(false, "许可证内容不能为空", "INVALID_CONTENT");
			}

			// 尝试更新许可证
			boolean success = updatePluginLicenseInternal(pluginId, request.getLicenseContent());

			if (success) {
				logger.info(LOG_PREFIX + "许可证更新成功: " + pluginId);
				return new LicenseUpdateResponse(true, "许可证更新成功", null);
			} else {
				logger.warn(LOG_PREFIX + "许可证更新失败: " + pluginId);
				return new LicenseUpdateResponse(false, "许可证更新失败", "UPDATE_FAILED");
			}

		} catch (Exception e) {
			logger.error(LOG_PREFIX + "更新许可证时发生异常: " + pluginId, e);
			return new LicenseUpdateResponse(false, "更新失败: " + e.getMessage(), "EXCEPTION");
		}
	}

	@Override
	public boolean validatePluginLicense(String pluginId) {
		logger.debug(LOG_PREFIX + "验证插件许可证: " + pluginId);

		try {
			return licenseManager.hasValidLicense(pluginId);
		} catch (Exception e) {
			logger.error(LOG_PREFIX + "验证许可证失败: " + pluginId, e);
			return false;
		}
	}

	// 辅助方法

	/**
	 * 获取许可证状态描述
	 */
	private String getLicenseStatus(String pluginId) {
		try {
			if (licenseManager.hasValidLicense(pluginId)) {
				return "有效";
			} else if (licenseManager.hasPluginLicense(pluginId)) {
				return "无效";
			} else {
				return "未安装";
			}
		} catch (Exception e) {
			return "错误: " + e.getMessage();
		}
	}

	/**
	 * 内部许可证更新方法
	 */
	private boolean updatePluginLicenseInternal(String pluginId, String licenseContent) {
		logger.info("更新插件许可证: {}", pluginId);

		try {
			// 将许可证内容转换为字节数组
			byte[] licenseData;
			try {
				// 尝试Base64解码
				licenseData = java.util.Base64.getDecoder().decode(licenseContent);
			} catch (IllegalArgumentException e) {
				// 如果不是Base64，直接使用UTF-8编码
				licenseData = licenseContent.getBytes("UTF-8");
			}

			// 注册新的许可证（会覆盖旧的）
			ValidationResult result = licenseManager.registerPluginLicense(pluginId, licenseData);

			if (result.isValid()) {
				logger.info("许可证更新成功: {}", pluginId);
				return true;
			} else {
				logger.warn("许可证更新失败: {}, 原因: {}", pluginId, result.getMessage());
				return false;
			}

		} catch (Exception e) {
			logger.error("许可证更新异常: {}", pluginId, e);
			return false;
		}
	}
}
