package com.fasnote.alm.plugin.manage;

import org.junit.runner.RunWith;
import org.junit.runners.Suite;
import org.junit.runners.Suite.SuiteClasses;

import com.fasnote.alm.plugin.manage.bootstrap.LicenseFrameworkBootstrapTest;
import com.fasnote.alm.plugin.manage.monitor.FrameworkMonitorTest;
import com.fasnote.alm.plugin.manage.registry.LicenseServiceRegistryTest;

/**
 * 重构后架构的完整测试套件
 *
 * 包含所有新架构组件的单元测试： - LicenseFrameworkBootstrap 测试 - LicenseServiceRegistry 测试 -
 * LicenseManagementFacade 测试 - LicenseFrameworkFacade 测试 - FrameworkMonitor 测试
 */
@RunWith(Suite.class)
@SuiteClasses({ LicenseFrameworkBootstrapTest.class, LicenseServiceRegistryTest.class, FrameworkMonitorTest.class })
public class AllRefactoringTests {
	// 测试套件类，无需实现内容
	// JUnit会自动运行所有指定的测试类
}
