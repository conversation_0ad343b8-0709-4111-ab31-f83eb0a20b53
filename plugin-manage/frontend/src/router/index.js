import { createRouter, createWebHistory } from 'vue-router'
import PluginList from '../views/PluginList.vue'
import PluginDetail from '../views/PluginDetail.vue'
import Dashboard from '../views/Dashboard.vue'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard
  },
  {
    path: '/plugins',
    name: 'PluginList',
    component: PluginList
  },
  {
    path: '/plugins/:pluginId',
    name: 'PluginDetail',
    component: PluginDetail,
    props: true
  }
]

const router = createRouter({
  history: createWebHistory('/polarion/plugin-manage/'),
  routes
})

export default router
