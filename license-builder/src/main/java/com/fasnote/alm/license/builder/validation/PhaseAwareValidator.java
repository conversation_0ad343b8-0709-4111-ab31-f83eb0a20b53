package com.fasnote.alm.license.builder.validation;

import com.fasnote.alm.license.builder.model.BundleInfo;

import java.util.Set;

/**
 * 阶段感知的验证器接口
 * 支持根据构建阶段和插件类型进行智能选择的验证器
 */
public interface PhaseAwareValidator {
    
    /**
     * 获取验证器名称
     * @return 验证器的唯一标识名称
     */
    String getName();
    
    /**
     * 获取支持的构建阶段
     * @return 该验证器支持的构建阶段集合
     */
    Set<BuildPhase> getSupportedPhases();
    
    /**
     * 获取支持的插件类型
     * @return 该验证器支持的插件类型集合
     */
    Set<PluginType> getSupportedPluginTypes();
    
    /**
     * 获取验证器优先级
     * 数字越小优先级越高，相同优先级的验证器按名称排序
     * @return 优先级数值
     */
    int getPriority();
    
    /**
     * 判断验证器是否适用于当前上下文
     * 可以根据具体的上下文信息进行更细粒度的判断
     * @param context 验证上下文
     * @return true表示适用，false表示不适用
     */
    boolean isApplicable(ValidationContext context);
    
    /**
     * 执行验证
     * @param bundleInfo Bundle信息
     * @param context 验证上下文
     * @return 验证结果
     */
    ValidationResult validate(BundleInfo bundleInfo, ValidationContext context);
    
    /**
     * 获取验证器描述
     * @return 验证器的详细描述
     */
    default String getDescription() {
        return getName() + " 验证器";
    }
    
    /**
     * 是否为关键验证器
     * 关键验证器失败时会中断构建流程
     * @return true表示关键验证器，false表示非关键验证器
     */
    default boolean isCritical() {
        return true;
    }
    
    /**
     * 获取验证器版本
     * @return 验证器版本号
     */
    default String getVersion() {
        return "1.0.0";
    }
}
