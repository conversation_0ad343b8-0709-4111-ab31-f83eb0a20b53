package com.fasnote.alm.plugin.manage.config;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.exception.ConfigurationException;

/**
 * 许可证配置管理器 负责管理许可证验证框架的所有配置参数
 *
 * 配置来源优先级： 1. 系统属性 (System.getProperty) 2. 环境变量 (System.getenv) 3. 配置文件
 * (license-config.properties) 4. 默认值
 */
public class LicenseConfiguration {

	private static final Logger logger = LoggerFactory.getLogger(LicenseConfiguration.class);

	// 配置文件名
	private static final String CONFIG_FILE_NAME = "license-config.properties";

	// 默认配置值
	private static final String DEFAULT_LICENSE_DIR = "licenses";
	private static final String DEFAULT_CACHE_SIZE = "1000";
	private static final String DEFAULT_CACHE_TTL = "3600"; // 1小时
	private static final String DEFAULT_AUDIT_ENABLED = "true";
	private static final String DEFAULT_ENCRYPTION_ENABLED = "true";
	private static final String DEFAULT_MACHINE_BINDING_ENABLED = "true";
	private static final String DEFAULT_SIGNATURE_VALIDATION_ENABLED = "true";
	private static final String DEFAULT_LICENSE_CHECK_INTERVAL = "300"; // 5分钟

	// 配置键名
	public static final String KEY_LICENSE_DIR = "license.directory";
	public static final String KEY_CACHE_SIZE = "license.cache.size";
	public static final String KEY_CACHE_TTL = "license.cache.ttl";
	public static final String KEY_AUDIT_ENABLED = "license.audit.enabled";
	public static final String KEY_ENCRYPTION_ENABLED = "license.encryption.enabled";
	public static final String KEY_MACHINE_BINDING_ENABLED = "license.machine.binding.enabled";
	public static final String KEY_SIGNATURE_VALIDATION_ENABLED = "license.signature.validation.enabled";
	public static final String KEY_LICENSE_CHECK_INTERVAL = "license.check.interval";
	public static final String KEY_PUBLIC_KEY_PATH = "license.public.key.path";
	public static final String KEY_ENCRYPTION_KEY_PATH = "license.encryption.key.path";
	public static final String KEY_LOG_LEVEL = "license.log.level";
	public static final String KEY_MAX_PLUGINS = "license.max.plugins";
	public static final String KEY_AUTO_SCAN_ENABLED = "license.auto.scan.enabled";
	public static final String KEY_SCAN_INTERVAL = "license.scan.interval";
	public static final String KEY_HOT_RELOAD_ENABLED = "license.hot.reload.enabled";
	public static final String KEY_VALIDATION_TIMEOUT = "license.validation.timeout";

	// 单例实例
	private static volatile LicenseConfiguration instance;

	/**
	 * 获取单例实例
	 */
	public static LicenseConfiguration getInstance() {
		if (instance == null) {
			synchronized (LicenseConfiguration.class) {
				if (instance == null) {
					instance = new LicenseConfiguration();
				}
			}
		}
		return instance;
	}

	// 配置缓存
	private final Map<String, String> configCache = new ConcurrentHashMap<>();

	// 配置文件属性
	private Properties configProperties;

	/**
	 * 私有构造函数
	 */
	private LicenseConfiguration() {
		this.configProperties = new Properties();
	}

	/**
	 * 清空配置缓存
	 */
	public void clearCache() {
		configCache.clear();
		logger.info("清空配置缓存");
	}

	/**
	 * 获取所有配置信息（用于调试）
	 */
	public Map<String, String> getAllConfigs() {
		Map<String, String> allConfigs = new ConcurrentHashMap<>();

		// 添加缓存中的配置
		allConfigs.putAll(configCache);

		// 添加配置文件中的配置
		for (String key : configProperties.stringPropertyNames()) {
			if (!allConfigs.containsKey(key)) {
				allConfigs.put(key, configProperties.getProperty(key));
			}
		}

		return allConfigs;
	}

	/**
	 * 获取布尔配置值
	 *
	 * @param key          配置键
	 * @param defaultValue 默认值
	 * @return 布尔值
	 */
	public boolean getBooleanConfig(String key, boolean defaultValue) {
		String value = getConfigValue(key, String.valueOf(defaultValue));
		return Boolean.parseBoolean(value);
	}

	/**
	 * 获取缓存大小
	 */
	public int getCacheSize() {
		return getIntConfig(KEY_CACHE_SIZE, Integer.parseInt(DEFAULT_CACHE_SIZE));
	}

	/**
	 * 获取缓存TTL（秒）
	 */
	public long getCacheTTL() {
		return getLongConfig(KEY_CACHE_TTL, Long.parseLong(DEFAULT_CACHE_TTL));
	}

	/**
	 * 获取配置值 按优先级顺序：系统属性 > 环境变量 > 配置文件 > 默认值
	 *
	 * @param key          配置键
	 * @param defaultValue 默认值
	 * @return 配置值
	 */
	public String getConfigValue(String key, String defaultValue) {
		// 检查缓存
		String cachedValue = configCache.get(key);
		if (cachedValue != null) {
			return cachedValue;
		}

		String value = null;

		// 1. 系统属性
		value = System.getProperty(key);
		if (value != null && !value.trim().isEmpty()) {
			configCache.put(key, value);
			return value;
		}

		// 2. 环境变量（将点号替换为下划线并转大写）
		String envKey = key.replace('.', '_').toUpperCase();
		value = System.getenv(envKey);
		if (value != null && !value.trim().isEmpty()) {
			configCache.put(key, value);
			return value;
		}

		// 3. 配置文件
		value = configProperties.getProperty(key);
		if (value != null && !value.trim().isEmpty()) {
			configCache.put(key, value);
			return value;
		}

		// 4. 默认值
		if (defaultValue != null) {
			configCache.put(key, defaultValue);
			return defaultValue;
		}

		return null;
	}

	/**
	 * 获取加密密钥文件路径
	 */
	public String getEncryptionKeyPath() {
		return getConfigValue(KEY_ENCRYPTION_KEY_PATH, null);
	}

	// 具体配置获取方法

	/**
	 * 获取整数配置值
	 *
	 * @param key          配置键
	 * @param defaultValue 默认值
	 * @return 整数值
	 */
	public int getIntConfig(String key, int defaultValue) {
		String value = getConfigValue(key, String.valueOf(defaultValue));
		try {
			return Integer.parseInt(value);
		} catch (NumberFormatException e) {
			logger.warn("配置值格式错误: {}={}, 使用默认值: {}", key, value, defaultValue);
			return defaultValue;
		}
	}

	/**
	 * 获取许可证检查间隔（秒）
	 */
	public long getLicenseCheckInterval() {
		return getLongConfig(KEY_LICENSE_CHECK_INTERVAL, Long.parseLong(DEFAULT_LICENSE_CHECK_INTERVAL));
	}

	/**
	 * 获取许可证目录
	 */
	public String getLicenseDirectory() {
		return getConfigValue(KEY_LICENSE_DIR, DEFAULT_LICENSE_DIR);
	}

	/**
	 * 获取日志级别
	 */
	public String getLogLevel() {
		return getConfigValue(KEY_LOG_LEVEL, "INFO");
	}

	/**
	 * 获取长整数配置值
	 *
	 * @param key          配置键
	 * @param defaultValue 默认值
	 * @return 长整数值
	 */
	public long getLongConfig(String key, long defaultValue) {
		String value = getConfigValue(key, String.valueOf(defaultValue));
		try {
			return Long.parseLong(value);
		} catch (NumberFormatException e) {
			logger.warn("配置值格式错误: {}={}, 使用默认值: {}", key, value, defaultValue);
			return defaultValue;
		}
	}

	/**
	 * 获取最大插件数量
	 */
	public int getMaxPlugins() {
		return getIntConfig(KEY_MAX_PLUGINS, 100);
	}

	/**
	 * 获取公钥文件路径
	 */
	public String getPublicKeyPath() {
		return getConfigValue(KEY_PUBLIC_KEY_PATH, null);
	}

	/**
	 * 获取扫描间隔（秒）
	 */
	public long getScanInterval() {
		return getLongConfig(KEY_SCAN_INTERVAL, 600); // 10分钟
	}

	/**
	 * 获取验证超时时间（毫秒）
	 */
	public long getValidationTimeout() {
		return getLongConfig(KEY_VALIDATION_TIMEOUT, 5000); // 5秒
	}

	/**
	 * 是否启用审计日志
	 */
	public boolean isAuditEnabled() {
		return getBooleanConfig(KEY_AUDIT_ENABLED, Boolean.parseBoolean(DEFAULT_AUDIT_ENABLED));
	}

	/**
	 * 是否启用自动扫描
	 */
	public boolean isAutoScanEnabled() {
		return getBooleanConfig(KEY_AUTO_SCAN_ENABLED, true);
	}

	/**
	 * 是否启用加密
	 */
	public boolean isEncryptionEnabled() {
		return getBooleanConfig(KEY_ENCRYPTION_ENABLED, Boolean.parseBoolean(DEFAULT_ENCRYPTION_ENABLED));
	}

	/**
	 * 是否启用热加载
	 */
	public boolean isHotReloadEnabled() {
		return getBooleanConfig(KEY_HOT_RELOAD_ENABLED, true);
	}

	/**
	 * 是否启用机器码绑定
	 */
	public boolean isMachineBindingEnabled() {
		return getBooleanConfig(KEY_MACHINE_BINDING_ENABLED, Boolean.parseBoolean(DEFAULT_MACHINE_BINDING_ENABLED));
	}

	/**
	 * 是否启用签名验证
	 */
	public boolean isSignatureValidationEnabled() {
		return getBooleanConfig(KEY_SIGNATURE_VALIDATION_ENABLED,
				Boolean.parseBoolean(DEFAULT_SIGNATURE_VALIDATION_ENABLED));
	}

	/**
	 * 加载配置文件
	 */
	private void loadConfigFile() {
		// 尝试从多个位置加载配置文件
		String[] configPaths = { "/etc/license/" + CONFIG_FILE_NAME, CONFIG_FILE_NAME // 类路径下
		};

		for (String configPath : configPaths) {
			File configFile = new File(configPath);
			if (configFile.exists() && configFile.isFile()) {
				try (FileInputStream fis = new FileInputStream(configFile)) {
					configProperties.load(fis);
					logger.info("从文件加载配置: {}", configPath);
					return;
				} catch (IOException e) {
					logger.warn("无法加载配置文件: {}", configPath, e);
				}
			}
		}

		// 尝试从类路径加载
		try {
			var inputStream = getClass().getClassLoader().getResourceAsStream(CONFIG_FILE_NAME);
			if (inputStream != null) {
				configProperties.load(inputStream);
				logger.info("从类路径加载配置: {}", CONFIG_FILE_NAME);
				return;
			}
		} catch (IOException e) {
			logger.warn("无法从类路径加载配置文件", e);
		}

		logger.info("未找到配置文件，使用默认配置");
	}

	/**
	 * 加载配置
	 */
	public void loadConfiguration() {
		logger.info("加载许可证配置");

		try {
			// 加载配置文件
			loadConfigFile();

			// 预加载常用配置到缓存
			preloadCommonConfigs();

			logger.info("许可证配置加载完成");

		} catch (Exception e) {
			logger.error("许可证配置加载失败", e);
			throw new ConfigurationException("许可证配置加载失败", e);
		}
	}

	/**
	 * 预加载常用配置
	 */
	private void preloadCommonConfigs() {
		// 预加载常用配置到缓存中，提高访问性能
		getLicenseDirectory();
		getCacheSize();
		getCacheTTL();
		isAuditEnabled();
		isEncryptionEnabled();
		isMachineBindingEnabled();
		isSignatureValidationEnabled();
		getLicenseCheckInterval();
	}

	/**
	 * 重新加载配置
	 */
	public void reloadConfiguration() {
		logger.info("重新加载配置");
		clearCache();
		configProperties.clear();
		loadConfiguration();
	}

	/**
	 * 移除配置值
	 *
	 * @param key 配置键
	 */
	public void removeConfigValue(String key) {
		configCache.remove(key);
		logger.info("移除配置: {}", key);
	}

	/**
	 * 设置配置值（运行时动态设置）
	 *
	 * @param key   配置键
	 * @param value 配置值
	 */
	public void setConfigValue(String key, String value) {
		configCache.put(key, value);
		logger.info("设置配置: {}={}", key, value);
	}
}
