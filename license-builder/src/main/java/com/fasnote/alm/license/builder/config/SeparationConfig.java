package com.fasnote.alm.license.builder.config;

import java.util.ArrayList;
import java.util.List;

/**
 * 分离配置
 */
public class SeparationConfig {
    
    public enum Strategy {
        AUTO,      // 自动检测
        MANUAL,    // 手动指定
        HYBRID     // 混合模式
    }
    
    private Strategy strategy = Strategy.AUTO;
    private List<String> licensedPackages = new ArrayList<>();
    private List<String> licensedClasses = new ArrayList<>();
    private List<String> excludedClasses = new ArrayList<>();
    private List<String> excludedPackages = new ArrayList<>();
    private ResourceSeparationConfig resources = new ResourceSeparationConfig();
    
    public Strategy getStrategy() { return strategy; }
    public void setStrategy(String strategy) { 
        this.strategy = Strategy.valueOf(strategy.toUpperCase()); 
    }
    public void setStrategy(Strategy strategy) { this.strategy = strategy; }
    
    public List<String> getLicensedPackages() { return licensedPackages; }
    public void setLicensedPackages(List<String> licensedPackages) { 
        this.licensedPackages = licensedPackages; 
    }
    
    public List<String> getLicensedClasses() { return licensedClasses; }
    public void setLicensedClasses(List<String> licensedClasses) { 
        this.licensedClasses = licensedClasses; 
    }
    
    public List<String> getExcludedClasses() { return excludedClasses; }
    public void setExcludedClasses(List<String> excludedClasses) { 
        this.excludedClasses = excludedClasses; 
    }
    
    public List<String> getExcludedPackages() { return excludedPackages; }
    public void setExcludedPackages(List<String> excludedPackages) { 
        this.excludedPackages = excludedPackages; 
    }
    
    public ResourceSeparationConfig getResources() { return resources; }
    public void setResources(ResourceSeparationConfig resources) { this.resources = resources; }
    
    /**
     * 资源分离配置
     */
    public static class ResourceSeparationConfig {
        private boolean enabled = true;
        private List<String> patterns = new ArrayList<>();
        private List<String> excludePatterns = new ArrayList<>();
        
        public ResourceSeparationConfig() {
            patterns.add("premium/**");
            patterns.add("license/**");
            excludePatterns.add("**/*.class");
        }
        
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        
        public List<String> getPatterns() { return patterns; }
        public void setPatterns(List<String> patterns) { this.patterns = patterns; }
        
        public List<String> getExcludePatterns() { return excludePatterns; }
        public void setExcludePatterns(List<String> excludePatterns) { 
            this.excludePatterns = excludePatterns; 
        }
    }
}