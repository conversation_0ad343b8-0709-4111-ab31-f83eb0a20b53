Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: ALM Plugin Management Framework
Bundle-SymbolicName: com.fasnote.alm.plugin.manage;singleton:=true
Bundle-Version: 2.0.0.qualifier
Export-Package: com.fasnote.alm.plugin.manage.annotation;version="2.0.0",
 com.fasnote.alm.plugin.manage.api;version="2.0.0",
 com.fasnote.alm.plugin.manage.core;version="2.0.0",
 com.fasnote.alm.plugin.manage.model;version="2.0.0"
Bundle-Activator: com.fasnote.alm.plugin.manage.Activator
Bundle-ClassPath: lib/license-crypto-1.0.0.jar,
 .
Bundle-Vendor: FasNote
Bundle-RequiredExecutionEnvironment: JavaSE-11
Import-Package: com.polarion.portal.tomcat.servlets,
 org.osgi.framework;version="1.3.0",
 org.osgi.service.component;version="1.2.0",
 org.osgi.util.tracker;version="1.5.0",
 org.slf4j;version="1.7.0"
Require-Bundle: org.eclipse.osgi;bundle-version="3.13.0",
 com.fasnote.alm.injection;bundle-version="[1.0.0,2.0.0)";visibility:=reexport,
 com.polarion.portal.tomcat;bundle-version="9.0.53",
 org.springframework.spring-aop;bundle-version="5.2.10",
 org.springframework.spring-beans;bundle-version="5.2.10",
 org.springframework.spring-context;bundle-version="5.2.10",
 org.springframework.spring-core;bundle-version="5.2.10",
 org.springframework.spring-expression;bundle-version="5.2.10",
 org.springframework.spring-web;bundle-version="5.2.10",
 org.springframework.spring-webmvc;bundle-version="5.2.10",
 com.polarion.platform;bundle-version="3.22.1",
 com.fasterxml.jackson.dataformat.jackson-dataformat-yaml;bundle-version="2.11.1",
 com.fasterxml.jackson.jaxrs;bundle-version="2.11.1",
 com.fasnote.alm.test;bundle-version="1.0.0"
Bundle-ActivationPolicy: lazy

