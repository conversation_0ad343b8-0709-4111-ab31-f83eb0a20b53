package com.fasnote.alm.injection.aop;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IEnhancedServiceInterceptor;

/**
 * 方法拦截代理
 * 
 * 在OSGi环境下提供方法级别的AOP支持
 * 使用JDK动态代理实现方法拦截
 */
public class MethodInterceptorProxy implements InvocationHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(MethodInterceptorProxy.class);
    
    private final Object target;
    private final List<IEnhancedServiceInterceptor> interceptors;
    
    public MethodInterceptorProxy(Object target, List<IEnhancedServiceInterceptor> interceptors) {
        this.target = target;
        this.interceptors = interceptors;
    }
    
    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        // 对于Object类的方法（如toString、equals等），直接调用原方法
        if (method.getDeclaringClass() == Object.class) {
            return method.invoke(target, args);
        }
        
        try {
            // 执行前置拦截
            Object beforeResult = executeBeforeInterceptors(target, method, args);
            if (beforeResult != null) {
                return beforeResult;
            }
            
            // 执行原方法
            Object result = method.invoke(target, args);
            
            // 执行后置拦截
            return executeAfterInterceptors(target, method, args, result);
            
        } catch (Throwable e) {
            // 执行异常拦截
            Throwable handledException = executeExceptionInterceptors(target, method, args, e);
            if (handledException != null) {
                throw handledException;
            }
            return null; // 异常被处理，返回null
        }
    }
    
    /**
     * 执行前置拦截器
     */
    private Object executeBeforeInterceptors(Object target, Method method, Object[] args) {
        for (IEnhancedServiceInterceptor interceptor : interceptors) {
            if (interceptor.shouldInterceptMethod(target, method, args)) {
                Object result = interceptor.beforeMethodInvocation(target, method, args);
                if (result != null) {
                    logger.debug("拦截器 {} 返回前置结果，跳过原方法执行", 
                               interceptor.getInterceptorName());
                    return result;
                }
            }
        }
        return null;
    }
    
    /**
     * 执行后置拦截器
     */
    private Object executeAfterInterceptors(Object target, Method method, Object[] args, Object result) {
        Object currentResult = result;
        for (IEnhancedServiceInterceptor interceptor : interceptors) {
            if (interceptor.shouldInterceptMethod(target, method, args)) {
                currentResult = interceptor.afterMethodInvocation(target, method, args, currentResult);
            }
        }
        return currentResult;
    }
    
    /**
     * 执行异常拦截器
     */
    private Throwable executeExceptionInterceptors(Object target, Method method, Object[] args, Throwable exception) {
        Throwable currentException = exception;
        for (IEnhancedServiceInterceptor interceptor : interceptors) {
            if (interceptor.shouldInterceptMethod(target, method, args)) {
                currentException = interceptor.onMethodException(target, method, args, currentException);
                if (currentException == null) {
                    logger.debug("拦截器 {} 处理了异常，不再抛出", 
                               interceptor.getInterceptorName());
                    return null;
                }
            }
        }
        return currentException;
    }
    
    /**
     * 创建代理对象
     * 
     * @param target 目标对象
     * @param serviceInterface 服务接口
     * @param interceptors 拦截器列表
     * @return 代理对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T createProxy(T target, Class<T> serviceInterface, List<IEnhancedServiceInterceptor> interceptors) {
        if (target == null || serviceInterface == null || interceptors.isEmpty()) {
            return target;
        }
        
        // 检查是否有拦截器需要方法拦截
        boolean needsInterception = interceptors.stream()
            .anyMatch(interceptor -> interceptor.needsMethodInterception(serviceInterface, target));
        
        if (!needsInterception) {
            return target;
        }
        
        logger.debug("为服务创建方法拦截代理: {}", serviceInterface.getSimpleName());
        
        return (T) Proxy.newProxyInstance(
            serviceInterface.getClassLoader(),
            new Class<?>[]{serviceInterface},
            new MethodInterceptorProxy(target, interceptors)
        );
    }
    
    /**
     * 检查对象是否为代理对象
     */
    public static boolean isProxy(Object obj) {
        return Proxy.isProxyClass(obj.getClass());
    }
    
    /**
     * 获取代理对象的原始目标对象
     */
    public static Object getTarget(Object proxy) {
        if (!isProxy(proxy)) {
            return proxy;
        }
        
        InvocationHandler handler = Proxy.getInvocationHandler(proxy);
        if (handler instanceof MethodInterceptorProxy) {
            return ((MethodInterceptorProxy) handler).target;
        }
        
        return proxy;
    }
}
