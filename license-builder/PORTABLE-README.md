# ALM License Builder - 便携版本

这是ALM License Builder的便携版本，包含所有依赖和混淆工具在单一JAR文件中。

## 文件结构

```
license-builder-all.jar    # 包含所有依赖的完整JAR文件
scripts/build-plugin.sh    # 统一构建脚本
```

## 环境要求

- Java 11 或更高版本
- 无需额外安装混淆工具（已包含在JAR中）

## 使用方法

### 使用构建脚本（推荐）

```bash
# 给脚本执行权限
chmod +x scripts/build-plugin.sh

# 仅打包和混淆（默认模式）
./scripts/build-plugin.sh /path/to/plugin

# 完整流程（打包、混淆、类分离、许可证处理）
./scripts/build-plugin.sh /path/to/plugin --mode full

# 自定义配置
./scripts/build-plugin.sh /path/to/plugin /opt/polarion/polarion --obfuscation-tool PROGUARD --verbose

# 显示帮助
./scripts/build-plugin.sh --help
```

### 直接使用JAR

```bash
# 使用配置文件
java -jar license-builder-all.jar --config config.yml

# 使用命令行参数
java -jar license-builder-all.jar --input plugin.jar --output ./output --eclipse-path /opt/polarion

# 显示帮助
java -jar license-builder-all.jar --help
```

## 支持的混淆工具

- **YGUARD** - 默认混淆工具，支持高级混淆技术
- **PROGUARD** - 经典的Java混淆工具
- **INTERNAL** - 内置的简化混淆器

## 构建便携版本

如果您需要重新构建便携版本：

```bash
# 构建包含所有依赖的JAR
mvn clean package

# 生成的文件位于
target/license-builder-all.jar
```

## 部署说明

要部署到其他机器，只需复制以下文件：

1. `license-builder-all.jar` - 主程序文件
2. `scripts/build-plugin.sh` - 统一构建脚本（可选）

无需复制任何其他依赖或工具文件。

## 故障排除

### 找不到JAR文件

确保以下文件之一存在：
- `license-builder-all.jar`
- `target/license-builder-all.jar`
- `license-builder.jar`
- `target/license-builder.jar`

### Java版本问题

检查Java版本：
```bash
java -version
```

确保使用Java 11或更高版本。

### 混淆工具不可用

所有混淆工具都已包含在JAR中。如果仍然报错，请检查：
1. JAR文件是否完整
2. 是否有足够的磁盘空间
3. 是否有适当的文件权限
