package com.fasnote.alm.plugin.manage.web.dto;

/**
 * 许可证更新响应DTO
 */
public class LicenseUpdateResponse {
	private boolean success;
	private String message;
	private String errorCode;

	public LicenseUpdateResponse() {
	}

	public LicenseUpdateResponse(boolean success, String message, String errorCode) {
		this.success = success;
		this.message = message;
		this.errorCode = errorCode;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public String getMessage() {
		return message;
	}

	// Getters and Setters
	public boolean isSuccess() {
		return success;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}
}
