package com.fasnote.alm.license.builder.model;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 类信息模型
 */
public class ClassInfo {
    
    private String className;
    private String packageName;
    private boolean isPublic;
    private boolean isInterface;
    private boolean isAbstract;
    private boolean isEnum;
    private boolean isAnnotation;
    private String superClass;
    private List<String> interfaces = new ArrayList<>();
    private List<String> superClasses = new ArrayList<>();
    private Set<String> annotations = new HashSet<>();
    private List<String> innerClasses = new ArrayList<>();
    private List<MethodInfo> methods = new ArrayList<>();
    private List<FieldInfo> fields = new ArrayList<>();
    
    public String getClassName() { return className; }
    public void setClassName(String className) { 
        this.className = className; 
        if (className != null) {
            int lastDotIndex = className.lastIndexOf('.');
            this.packageName = lastDotIndex > 0 ? className.substring(0, lastDotIndex) : "";
        }
    }
    
    public String getPackageName() { return packageName; }
    public void setPackageName(String packageName) { this.packageName = packageName; }
    
    public boolean isPublic() { return isPublic; }
    public void setPublic(boolean isPublic) { this.isPublic = isPublic; }
    
    public boolean isInterface() { return isInterface; }
    public void setInterface(boolean isInterface) { this.isInterface = isInterface; }
    
    public boolean isAbstract() { return isAbstract; }
    public void setAbstract(boolean isAbstract) { this.isAbstract = isAbstract; }
    
    public boolean isEnum() { return isEnum; }
    public void setEnum(boolean isEnum) { this.isEnum = isEnum; }
    
    public boolean isAnnotation() { return isAnnotation; }
    public void setAnnotation(boolean isAnnotation) { this.isAnnotation = isAnnotation; }
    
    public String getSuperClass() { return superClass; }
    public void setSuperClass(String superClass) { this.superClass = superClass; }
    
    public List<String> getInterfaces() { return interfaces; }
    public void setInterfaces(List<String> interfaces) { this.interfaces = interfaces; }
    
    public List<String> getSuperClasses() { return superClasses; }
    public void setSuperClasses(List<String> superClasses) { this.superClasses = superClasses; }
    
    public Set<String> getAnnotations() { return annotations; }
    public void setAnnotations(Set<String> annotations) { this.annotations = annotations; }
    
    public List<String> getInnerClasses() { return innerClasses; }
    public void setInnerClasses(List<String> innerClasses) { this.innerClasses = innerClasses; }
    
    public List<MethodInfo> getMethods() { return methods; }
    public void setMethods(List<MethodInfo> methods) { this.methods = methods; }
    
    public List<FieldInfo> getFields() { return fields; }
    public void setFields(List<FieldInfo> fields) { this.fields = fields; }
    
    @Override
    public String toString() {
        return "ClassInfo{" +
                "className='" + className + '\'' +
                ", isPublic=" + isPublic +
                ", isInterface=" + isInterface +
                '}';
    }
    
    /**
     * 方法信息
     */
    public static class MethodInfo {
        private String name;
        private boolean isPublic;
        private boolean isStatic;
        private boolean isAbstract;
        private String returnType;
        private List<String> parameterTypes = new ArrayList<>();
        private Set<String> annotations = new HashSet<>();
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public boolean isPublic() { return isPublic; }
        public void setPublic(boolean isPublic) { this.isPublic = isPublic; }
        
        public boolean isStatic() { return isStatic; }
        public void setStatic(boolean isStatic) { this.isStatic = isStatic; }
        
        public boolean isAbstract() { return isAbstract; }
        public void setAbstract(boolean isAbstract) { this.isAbstract = isAbstract; }
        
        public String getReturnType() { return returnType; }
        public void setReturnType(String returnType) { this.returnType = returnType; }
        
        public List<String> getParameterTypes() { return parameterTypes; }
        public void setParameterTypes(List<String> parameterTypes) { this.parameterTypes = parameterTypes; }
        
        public Set<String> getAnnotations() { return annotations; }
        public void setAnnotations(Set<String> annotations) { this.annotations = annotations; }
    }
    
    /**
     * 字段信息
     */
    public static class FieldInfo {
        private String name;
        private boolean isPublic;
        private boolean isStatic;
        private boolean isFinal;
        private String type;
        private Set<String> annotations = new HashSet<>();
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public boolean isPublic() { return isPublic; }
        public void setPublic(boolean isPublic) { this.isPublic = isPublic; }
        
        public boolean isStatic() { return isStatic; }
        public void setStatic(boolean isStatic) { this.isStatic = isStatic; }
        
        public boolean isFinal() { return isFinal; }
        public void setFinal(boolean isFinal) { this.isFinal = isFinal; }
        
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public Set<String> getAnnotations() { return annotations; }
        public void setAnnotations(Set<String> annotations) { this.annotations = annotations; }
    }
}