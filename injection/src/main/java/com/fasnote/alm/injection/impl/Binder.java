package com.fasnote.alm.injection.impl;

import com.fasnote.alm.injection.api.IBinder;
import com.fasnote.alm.injection.api.IBindingBuilder;
import com.fasnote.alm.injection.api.IServiceInterceptor;
import com.fasnote.alm.injection.api.IServiceProvider;

/**
 * 依赖绑定器实现
 */
public class Binder implements IBinder {
    
    private final DependencyInjector injector;
    
    public Binder(DependencyInjector injector) {
        this.injector = injector;
    }
    
    @Override
    public <T> IBindingBuilder<T> bind(Class<T> interfaceClass, Class<? extends T> implementationClass) {
        return new BindingBuilder<>(injector, interfaceClass, implementationClass);
    }
    
    @Override
    public <T> void bind(Class<T> interfaceClass, T instance) {
        injector.registerSingleton(interfaceClass, instance);
    }
    
    @Override
    public <T> IBindingBuilder<T> bind(Class<T> clazz) {
        return new BindingBuilder<>(injector, clazz, clazz);
    }
    
    @Override
    public void registerProvider(IServiceProvider<?> provider) {
        // 注册通用提供者（需要在调用时确定类型）
        // 这里可以添加更复杂的逻辑来处理提供者注册
    }
    
    @Override
    public void registerInterceptor(IServiceInterceptor interceptor) {
        injector.registerInterceptor(interceptor);
    }

    @Override
    public <T> void registerImplementation(Class<T> serviceClass, Class<? extends T> implementationClass) {
        injector.registerImplementation(serviceClass, implementationClass);
    }

    @Override
    public <T> void registerImplementation(Class<T> serviceClass, Class<? extends T> implementationClass,
                                         String name, boolean primary) {
        injector.registerImplementation(serviceClass, implementationClass, name, primary);
    }
}