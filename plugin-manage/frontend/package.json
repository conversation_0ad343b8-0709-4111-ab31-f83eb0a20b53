{"name": "plugin-management-frontend", "version": "1.0.0", "description": "Plugin Management Frontend for Eclipse Plugin", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "axios": "^1.4.0", "element-plus": "^2.3.8", "@element-plus/icons-vue": "^2.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5"}, "keywords": ["vue", "plugin-management", "license-management"], "author": "FasNote", "license": "MIT"}