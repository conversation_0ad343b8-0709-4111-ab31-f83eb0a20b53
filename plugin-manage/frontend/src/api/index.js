import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('发送请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('收到响应:', response.status, response.config.url)
    return response.data
  },
  error => {
    console.error('响应错误:', error.response?.status, error.response?.data || error.message)
    
    // 统一错误处理
    if (error.response?.status === 401) {
      // 未授权，可能需要重新登录
      console.warn('未授权访问')
    } else if (error.response?.status === 500) {
      // 服务器错误
      console.error('服务器内部错误')
    }
    
    return Promise.reject(error)
  }
)

// API方法
export const pluginApi = {
  // 获取所有插件
  getAllPlugins() {
    return api.get('/plugins')
  },

  // 获取插件许可证信息
  getPluginLicense(pluginId) {
    return api.get(`/plugins/${pluginId}/license`)
  },

  // 更新插件许可证
  updatePluginLicense(pluginId, licenseData) {
    return api.post(`/plugins/${pluginId}/license`, licenseData)
  },

  // 验证插件许可证
  validatePluginLicense(pluginId) {
    return api.get(`/plugins/${pluginId}/validate`)
  },

  // 获取框架统计信息
  getFrameworkStatistics() {
    return api.get('/framework/statistics')
  },

  // 刷新所有许可证
  refreshAllLicenses() {
    return api.post('/framework/refresh')
  },

  // 健康检查
  healthCheck() {
    return api.get('/health')
  }
}

export default api
