package com.fasnote.alm.license.builder.config;

import java.util.ArrayList;
import java.util.List;

/**
 * 混淆配置
 */
public class ObfuscationConfig {
    
    public enum Level {
        LIGHT,
        STANDARD, 
        AGGRESSIVE
    }
    
    public enum Tool {
        INTERNAL,
        YGUARD,
        PROGUARD
    }
    
    public enum Target {
        LICENSE_ONLY,
        ALL
    }
    
    private boolean enabled = true;
    private Level level = Level.STANDARD;
    private Tool tool = Tool.YGUARD;
    private Target target = Target.LICENSE_ONLY;
    private boolean encryptionEnabled = false;  // 加密默认关闭
    private ProGuardConfig proguard = new ProGuardConfig();
    private YGuardConfig yguard = new YGuardConfig();
    private List<String> excludedPackages = new ArrayList<>();
    private List<String> includedPackages = new ArrayList<>();
    private List<String> excludedClasses = new ArrayList<>();
    
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    public Level getLevel() { return level; }
    public void setLevel(String level) { this.level = Level.valueOf(level.toUpperCase()); }
    public void setLevel(Level level) { this.level = level; }
    
    public Tool getTool() { return tool; }
    public void setTool(String tool) { this.tool = Tool.valueOf(tool.toUpperCase()); }
    public void setTool(Tool tool) { this.tool = tool; }
    
    public Target getTarget() { return target; }
    public void setTarget(String target) { this.target = Target.valueOf(target.toUpperCase()); }
    public void setTarget(Target target) { this.target = target; }
    
    public boolean isEncryptionEnabled() { return encryptionEnabled; }
    public void setEncryptionEnabled(boolean encryptionEnabled) { this.encryptionEnabled = encryptionEnabled; }
    
    public ProGuardConfig getProguard() { return proguard; }
    public void setProguard(ProGuardConfig proguard) { this.proguard = proguard; }
    
    public YGuardConfig getYguard() { return yguard; }
    public void setYguard(YGuardConfig yguard) { this.yguard = yguard; }
    
    public List<String> getExcludedPackages() { return excludedPackages; }
    public void setExcludedPackages(List<String> excludedPackages) { this.excludedPackages = excludedPackages; }
    
    public List<String> getIncludedPackages() { return includedPackages; }
    public void setIncludedPackages(List<String> includedPackages) { this.includedPackages = includedPackages; }
    
    public List<String> getExcludedClasses() { return excludedClasses; }
    public void setExcludedClasses(List<String> excludedClasses) { this.excludedClasses = excludedClasses; }
    
    /**
     * ProGuard配置
     */
    public static class ProGuardConfig {
        private String jarPath;
        private String configTemplate;
        private List<String> additionalOptions = new ArrayList<>();
        
        public ProGuardConfig() {
            additionalOptions.add("-dontoptimize");
            additionalOptions.add("-verbose");
        }
        
        public String getJarPath() { return jarPath; }
        public void setJarPath(String jarPath) { this.jarPath = jarPath; }
        
        public String getConfigTemplate() { return configTemplate; }
        public void setConfigTemplate(String configTemplate) { this.configTemplate = configTemplate; }
        
        public List<String> getAdditionalOptions() { return additionalOptions; }
        public void setAdditionalOptions(List<String> additionalOptions) { 
            this.additionalOptions = additionalOptions; 
        }
    }
    
    /**
     * yGuard配置
     */
    public static class YGuardConfig {
        private String jarPath;
        private String configTemplate;
        private boolean flowObfuscation = true;
        private String flowObfuscationLevel = "maximum";
        private boolean stringObfuscation = true;
        private String stringObfuscationLevel = "maximum";
        private boolean controlFlowObfuscation = true;
        private String controlFlowObfuscationLevel = "maximum";
        private boolean removeDebugInfo = true;
        private boolean removeLineNumbers = false;
        private boolean exceptionObfuscation = true;
        private boolean reflectionObfuscation = true;
        private List<String> additionalOptions = new ArrayList<>();
        
        public YGuardConfig() {
            additionalOptions.add("conserveManifest=true");
            additionalOptions.add("replaceClassNameStrings=true");
            additionalOptions.add("error-checking=true");
        }
        
        public String getJarPath() { return jarPath; }
        public void setJarPath(String jarPath) { this.jarPath = jarPath; }
        
        public String getConfigTemplate() { return configTemplate; }
        public void setConfigTemplate(String configTemplate) { this.configTemplate = configTemplate; }
        
        public boolean isFlowObfuscation() { return flowObfuscation; }
        public void setFlowObfuscation(boolean flowObfuscation) { this.flowObfuscation = flowObfuscation; }
        
        public String getFlowObfuscationLevel() { return flowObfuscationLevel; }
        public void setFlowObfuscationLevel(String flowObfuscationLevel) { this.flowObfuscationLevel = flowObfuscationLevel; }
        
        public boolean isStringObfuscation() { return stringObfuscation; }
        public void setStringObfuscation(boolean stringObfuscation) { this.stringObfuscation = stringObfuscation; }
        
        public String getStringObfuscationLevel() { return stringObfuscationLevel; }
        public void setStringObfuscationLevel(String stringObfuscationLevel) { this.stringObfuscationLevel = stringObfuscationLevel; }
        
        public boolean isControlFlowObfuscation() { return controlFlowObfuscation; }
        public void setControlFlowObfuscation(boolean controlFlowObfuscation) { this.controlFlowObfuscation = controlFlowObfuscation; }
        
        public String getControlFlowObfuscationLevel() { return controlFlowObfuscationLevel; }
        public void setControlFlowObfuscationLevel(String controlFlowObfuscationLevel) { this.controlFlowObfuscationLevel = controlFlowObfuscationLevel; }
        
        public boolean isRemoveDebugInfo() { return removeDebugInfo; }
        public void setRemoveDebugInfo(boolean removeDebugInfo) { this.removeDebugInfo = removeDebugInfo; }
        
        public boolean isRemoveLineNumbers() { return removeLineNumbers; }
        public void setRemoveLineNumbers(boolean removeLineNumbers) { this.removeLineNumbers = removeLineNumbers; }
        
        public boolean isExceptionObfuscation() { return exceptionObfuscation; }
        public void setExceptionObfuscation(boolean exceptionObfuscation) { this.exceptionObfuscation = exceptionObfuscation; }
        
        public boolean isReflectionObfuscation() { return reflectionObfuscation; }
        public void setReflectionObfuscation(boolean reflectionObfuscation) { this.reflectionObfuscation = reflectionObfuscation; }
        
        public List<String> getAdditionalOptions() { return additionalOptions; }
        public void setAdditionalOptions(List<String> additionalOptions) { 
            this.additionalOptions = additionalOptions; 
        }
    }
    

}