package com.fasnote.alm.injection.api;

import java.util.Collections;
import java.util.List;

/**
 * 依赖注入模块接口
 * 类似于Spring的Configuration，支持子模块自动扫描
 */
public interface IModule {

    /**
     * 配置模块的依赖绑定
     *
     * @param binder 绑定器
     */
    void configure(IBinder binder);

    /**
     * 模块名称
     *
     * @return 模块名称
     */
    default String getName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 模块优先级（数字越小优先级越高）
     *
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 获取组件扫描包路径
     * 类似于Spring的@ComponentScan，用于自动发现子模块
     *
     * @return 扫描包路径列表，默认为空
     */
    default List<String> getComponentScanPackages() {
        return Collections.emptyList();
    }

    /**
     * 是否启用组件扫描
     *
     * @return true表示启用，false表示禁用
     */
    default boolean isComponentScanEnabled() {
        return !getComponentScanPackages().isEmpty();
    }
}