package com.fasnote.alm.license.builder.validation.validators;

import com.fasnote.alm.license.builder.model.BundleInfo;
import com.fasnote.alm.license.builder.validation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Set;

/**
 * 许可证完整性验证器
 * 验证许可证文件的完整性和正确性
 */
public class LicenseIntegrityValidator implements PhaseAwareValidator {
    
    private static final Logger logger = LoggerFactory.getLogger(LicenseIntegrityValidator.class);
    
    @Override
    public String getName() {
        return "LICENSE_INTEGRITY";
    }
    
    @Override
    public Set<BuildPhase> getSupportedPhases() {
        return Set.of(BuildPhase.POST_LICENSE_BUILD);
    }
    
    @Override
    public Set<PluginType> getSupportedPluginTypes() {
        // 只有业务插件需要许可证验证
        return Set.of(PluginType.BUSINESS);
    }
    
    @Override
    public int getPriority() {
        return 200; // 中等优先级
    }
    
    @Override
    public boolean isApplicable(ValidationContext context) {
        // 只在license模式下执行
        String buildMode = context.getBuildMode();
        return "license".equals(buildMode) || "all".equals(buildMode);
    }
    
    @Override
    public String getDescription() {
        return "许可证完整性验证器 - 验证许可证文件的完整性和正确性";
    }
    
    @Override
    public ValidationResult validate(BundleInfo bundleInfo, ValidationContext context) {
        logger.info("开始执行许可证完整性验证...");
        
        try {
            ValidationResult.Builder resultBuilder = ValidationResult.builder()
                    .validatorName(getName());
            
            // 验证Bundle信息
            if (bundleInfo == null) {
                return resultBuilder
                        .success(false)
                        .message("Bundle信息为空")
                        .build();
            }
            
            // 获取输出目录
            String outputDir = getOutputDir(context);
            if (outputDir == null) {
                return resultBuilder
                        .success(false)
                        .message("未指定输出目录")
                        .build();
            }
            
            // 验证许可证文件存在性
            ValidationResult licenseFileValidation = validateLicenseFiles(outputDir, bundleInfo, resultBuilder);
            if (!licenseFileValidation.isSuccess()) {
                return licenseFileValidation;
            }
            
            // 验证接口JAR文件
            ValidationResult interfaceJarValidation = validateInterfaceJar(outputDir, bundleInfo, resultBuilder);
            if (!interfaceJarValidation.isSuccess()) {
                return interfaceJarValidation;
            }
            
            logger.info("许可证完整性验证通过");
            return resultBuilder
                    .success(true)
                    .message("许可证完整性验证通过")
                    .build();
            
        } catch (Exception e) {
            logger.error("许可证完整性验证失败", e);
            return ValidationResult.failure(getName(), "许可证完整性验证过程中发生异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取输出目录
     */
    private String getOutputDir(ValidationContext context) {
        // 优先从属性中获取
        String outputDir = context.getProperty("outputDir", String.class);
        if (outputDir != null) {
            return outputDir;
        }
        
        // 从配置中获取
        if (context.getConfiguration() != null && 
            context.getConfiguration().getBuild() != null) {
            return context.getConfiguration().getBuild().getOutputDir();
        }
        
        return null;
    }
    
    /**
     * 验证许可证文件
     */
    private ValidationResult validateLicenseFiles(String outputDir, BundleInfo bundleInfo, 
                                                 ValidationResult.Builder resultBuilder) {
        try {
            Path outputPath = Paths.get(outputDir);
            if (!Files.exists(outputPath)) {
                return resultBuilder
                        .success(false)
                        .message("输出目录不存在: " + outputDir)
                        .build();
            }
            
            // 查找许可证文件
            String symbolicName = bundleInfo.getSymbolicName();
            String expectedLicenseFile = symbolicName + ".lic";
            Path licenseFilePath = outputPath.resolve(expectedLicenseFile);
            
            if (!Files.exists(licenseFilePath)) {
                return resultBuilder
                        .success(false)
                        .message("许可证文件不存在: " + expectedLicenseFile)
                        .build();
            }
            
            // 检查许可证文件大小
            long fileSize = Files.size(licenseFilePath);
            if (fileSize == 0) {
                return resultBuilder
                        .success(false)
                        .message("许可证文件为空: " + expectedLicenseFile)
                        .build();
            }
            
            resultBuilder.message("许可证文件存在且有效: " + expectedLicenseFile + " (大小: " + fileSize + " 字节)");
            
            // 验证许可证文件格式（简单检查）
            if (fileSize < 100) {
                resultBuilder.warning("许可证文件大小异常小，可能存在问题");
            }
            
            return resultBuilder.success(true).build();
            
        } catch (Exception e) {
            return resultBuilder
                    .success(false)
                    .message("验证许可证文件时发生错误: " + e.getMessage())
                    .exception(e)
                    .build();
        }
    }
    
    /**
     * 验证接口JAR文件
     */
    private ValidationResult validateInterfaceJar(String outputDir, BundleInfo bundleInfo, 
                                                 ValidationResult.Builder resultBuilder) {
        try {
            Path outputPath = Paths.get(outputDir);
            
            // 查找接口JAR文件
            String symbolicName = bundleInfo.getSymbolicName();
            String expectedInterfaceJar = symbolicName + "-interface.jar";
            Path interfaceJarPath = outputPath.resolve(expectedInterfaceJar);
            
            if (!Files.exists(interfaceJarPath)) {
                return resultBuilder
                        .success(false)
                        .message("接口JAR文件不存在: " + expectedInterfaceJar)
                        .build();
            }
            
            // 检查接口JAR文件大小
            long fileSize = Files.size(interfaceJarPath);
            if (fileSize == 0) {
                return resultBuilder
                        .success(false)
                        .message("接口JAR文件为空: " + expectedInterfaceJar)
                        .build();
            }
            
            resultBuilder.message("接口JAR文件存在且有效: " + expectedInterfaceJar + " (大小: " + fileSize + " 字节)");
            
            return resultBuilder.success(true).build();
            
        } catch (Exception e) {
            return resultBuilder
                    .success(false)
                    .message("验证接口JAR文件时发生错误: " + e.getMessage())
                    .exception(e)
                    .build();
        }
    }
}
