package com.fasnote.alm.injection.api;

/**
 * 绑定构建器接口
 * 用于配置绑定的详细选项
 */
public interface IBindingBuilder<T> {
    
    /**
     * 设置服务名称
     * 
     * @param name 服务名称
     * @return 构建器实例
     */
    IBindingBuilder<T> named(String name);
    
    /**
     * 设置为单例
     * 
     * @return 构建器实例
     */
    IBindingBuilder<T> asSingleton();
    
    /**
     * 设置为原型（每次创建新实例）
     * 
     * @return 构建器实例
     */
    IBindingBuilder<T> asPrototype();
    
    /**
     * 设置为主要实现
     * 
     * @return 构建器实例
     */
    IBindingBuilder<T> asPrimary();
    
    /**
     * 设置优先级
     * 
     * @param priority 优先级（数字越小优先级越高）
     * @return 构建器实例
     */
    IBindingBuilder<T> withPriority(int priority);
    
    /**
     * 使用自定义提供者
     * 
     * @param provider 服务提供者
     * @return 构建器实例
     */
    IBindingBuilder<T> toProvider(IServiceProvider<T> provider);
    
    /**
     * 完成绑定配置
     */
    void build();
}