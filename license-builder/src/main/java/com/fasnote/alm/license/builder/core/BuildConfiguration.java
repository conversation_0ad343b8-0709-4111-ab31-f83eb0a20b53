package com.fasnote.alm.license.builder.core;

import com.fasnote.alm.license.builder.config.SeparationConfig;
import com.fasnote.alm.license.builder.config.ObfuscationConfig;

/**
 * 许可证构建配置
 * 包含所有构建过程所需的配置信息
 */
public class BuildConfiguration {
    
    private BuildConfig build = new BuildConfig();
    private InputConfig input = new InputConfig();
    private AnalysisConfig analysis = new AnalysisConfig();
    private SeparationConfig separation = new SeparationConfig();
    private ObfuscationConfig obfuscation = new ObfuscationConfig();
    private EncryptionConfig encryption = new EncryptionConfig();
    private PackagingConfig packaging = new PackagingConfig();
    private SigningConfig signing = new SigningConfig();
    private ValidationConfig validation = new ValidationConfig();
    private LoggingConfig logging = new LoggingConfig();
    
    // Getters and Setters
    public BuildConfig getBuild() { return build; }
    public void setBuild(BuildConfig build) { this.build = build; }
    
    public InputConfig getInput() { return input; }
    public void setInput(InputConfig input) { this.input = input; }
    
    public AnalysisConfig getAnalysis() { return analysis; }
    public void setAnalysis(AnalysisConfig analysis) { this.analysis = analysis; }
    
    public SeparationConfig getSeparation() { return separation; }
    public void setSeparation(SeparationConfig separation) { this.separation = separation; }
    
    public ObfuscationConfig getObfuscation() { return obfuscation; }
    public void setObfuscation(ObfuscationConfig obfuscation) { this.obfuscation = obfuscation; }
    
    public EncryptionConfig getEncryption() { return encryption; }
    public void setEncryption(EncryptionConfig encryption) { this.encryption = encryption; }
    
    public PackagingConfig getPackaging() { return packaging; }
    public void setPackaging(PackagingConfig packaging) { this.packaging = packaging; }
    
    public SigningConfig getSigning() { return signing; }
    public void setSigning(SigningConfig signing) { this.signing = signing; }
    
    public ValidationConfig getValidation() { return validation; }
    public void setValidation(ValidationConfig validation) { this.validation = validation; }
    
    public LoggingConfig getLogging() { return logging; }
    public void setLogging(LoggingConfig logging) { this.logging = logging; }
    
    /**
     * 构建配置
     */
    public static class BuildConfig {
        private String workDir = "./build-workspace";
        private String outputDir = "./target";
        private boolean cleanBuild = true;
        
        public String getWorkDir() { return workDir; }
        public void setWorkDir(String workDir) { this.workDir = workDir; }
        
        public String getOutputDir() { return outputDir; }
        public void setOutputDir(String outputDir) { this.outputDir = outputDir; }
        
        public boolean isCleanBuild() { return cleanBuild; }
        public void setCleanBuild(boolean cleanBuild) { this.cleanBuild = cleanBuild; }
    }
    
    /**
     * 输入配置
     */
    public static class InputConfig {
        private String sourceJar;
        private String eclipsePath; // Polarion安装路径 (如: /opt/polarion/polarion) - 仅用于ProGuard库JAR查找

        public String getSourceJar() { return sourceJar; }
        public void setSourceJar(String sourceJar) { this.sourceJar = sourceJar; }

        public String getEclipsePath() { return eclipsePath; }
        public void setEclipsePath(String eclipsePath) { this.eclipsePath = eclipsePath; }
    }
    
    /**
     * 分析配置
     */
    public static class AnalysisConfig {
        private boolean autoDetectLicenseImplementations = true;
        private java.util.List<String> licenseAnnotations = new java.util.ArrayList<>();
        private java.util.List<String> licenseInterfaces = new java.util.ArrayList<>();
        
        public AnalysisConfig() {
            licenseAnnotations.add("com.fasnote.alm.plugin.manage.annotation.LicenseImplementation");
            licenseAnnotations.add("com.fasnote.alm.plugin.manage.annotation.PremiumFeature");
            licenseAnnotations.add("com.fasnote.alm.plugin.manage.annotation.LicenseRequired");
            licenseAnnotations.add("com.fasnote.alm.plugin.manage.annotation.FeatureRequired");
            licenseAnnotations.add("com.fasnote.alm.plugin.manage.annotation.UserLimitCheck");
        }
        
        public boolean isAutoDetectLicenseImplementations() { return autoDetectLicenseImplementations; }
        public void setAutoDetectLicenseImplementations(boolean autoDetectLicenseImplementations) {
            this.autoDetectLicenseImplementations = autoDetectLicenseImplementations;
        }
        
        public java.util.List<String> getLicenseAnnotations() { return licenseAnnotations; }
        public void setLicenseAnnotations(java.util.List<String> licenseAnnotations) { 
            this.licenseAnnotations = licenseAnnotations; 
        }
        
        public java.util.List<String> getLicenseInterfaces() { return licenseInterfaces; }
        public void setLicenseInterfaces(java.util.List<String> licenseInterfaces) { 
            this.licenseInterfaces = licenseInterfaces; 
        }
    }
    
    /**
     * 加密配置（仅支持RSA）
     */
    public static class EncryptionConfig {

        public enum Algorithm {
            RSA_2048("RSA-2048"),
            RSA_4096("RSA-4096");

            private final String value;

            Algorithm(String value) {
                this.value = value;
            }

            public String getValue() {
                return value;
            }
        }

        public enum KeyGeneration {
            PROVIDED,
            GENERATED
        }

        private boolean enabled = true;
        private Algorithm algorithm = Algorithm.RSA_2048;
        private KeyGeneration keyGeneration = KeyGeneration.PROVIDED;
        private String keyPath;

        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }

        public Algorithm getAlgorithm() { return algorithm; }
        public void setAlgorithm(Algorithm algorithm) { this.algorithm = algorithm; }

        public KeyGeneration getKeyGeneration() { return keyGeneration; }
        public void setKeyGeneration(KeyGeneration keyGeneration) { this.keyGeneration = keyGeneration; }

        public String getKeyPath() { return keyPath; }
        public void setKeyPath(String keyPath) { this.keyPath = keyPath; }
    }
    
    /**
     * 打包配置
     */
    public static class PackagingConfig {
        private LicenseConfig license = new LicenseConfig();
        private PluginConfig plugin = new PluginConfig();
        
        public LicenseConfig getLicense() { return license; }
        public void setLicense(LicenseConfig license) { this.license = license; }
        
        public PluginConfig getPlugin() { return plugin; }
        public void setPlugin(PluginConfig plugin) { this.plugin = plugin; }
        
        public static class LicenseConfig {
            public enum ValidityDuration {
                UNLIMITED(-1, "无限制"),
                DAYS_30(30, "30天"),
                DAYS_90(90, "90天"),
                DAYS_180(180, "180天"),
                DAYS_365(365, "365天"),
                CUSTOM(0, "自定义");
                
                private final int days;
                private final String description;
                
                ValidityDuration(int days, String description) {
                    this.days = days;
                    this.description = description;
                }
                
                public int getDays() { return days; }
                public String getDescription() { return description; }
                public boolean isUnlimited() { return days == -1; }
            }
            
            private String outputName = "${bundle.symbolicName}_${bundle.version}.lic";
            private String format = "BINARY";
            private boolean compressed = true;
            private ValidityDuration validityDuration = ValidityDuration.DAYS_90; // 默认3个月
            private int customValidityDays = 90; // 自定义天数
            private java.util.Map<String, String> metadata = new java.util.HashMap<>();
            
            public LicenseConfig() {
                metadata.put("generator", "ALM License Builder");
                metadata.put("generatedAt", "${timestamp}");
                metadata.put("version", "1.0");
            }
            
            public String getOutputName() { return outputName; }
            public void setOutputName(String outputName) { this.outputName = outputName; }
            
            public String getFormat() { return format; }
            public void setFormat(String format) { this.format = format; }
            
            public boolean isCompressed() { return compressed; }
            public void setCompressed(boolean compressed) { this.compressed = compressed; }
            
            public ValidityDuration getValidityDuration() { return validityDuration; }
            public void setValidityDuration(ValidityDuration validityDuration) { this.validityDuration = validityDuration; }
            
            public int getCustomValidityDays() { return customValidityDays; }
            public void setCustomValidityDays(int customValidityDays) { this.customValidityDays = customValidityDays; }
            
            /**
             * 获取实际的有效期天数
             * @return 有效期天数，-1表示无限制
             */
            public int getActualValidityDays() {
                if (validityDuration == ValidityDuration.UNLIMITED) {
                    return -1;
                } else if (validityDuration == ValidityDuration.CUSTOM) {
                    return customValidityDays;
                } else {
                    return validityDuration.getDays();
                }
            }
            
            /**
             * 检查许可证是否为无限期
             * @return true表示无限期
             */
            public boolean isUnlimitedValidity() {
                return getActualValidityDays() == -1;
            }
            
            public java.util.Map<String, String> getMetadata() { return metadata; }
            public void setMetadata(java.util.Map<String, String> metadata) { this.metadata = metadata; }
        }
        
        public static class PluginConfig {
            private String outputName = "${bundle.symbolicName}_${bundle.version}_interface.jar";
            private boolean updateManifest = true;
            private ManifestConfig manifest = new ManifestConfig();
            
            public String getOutputName() { return outputName; }
            public void setOutputName(String outputName) { this.outputName = outputName; }
            
            public boolean isUpdateManifest() { return updateManifest; }
            public void setUpdateManifest(boolean updateManifest) { this.updateManifest = updateManifest; }
            
            public ManifestConfig getManifest() { return manifest; }
            public void setManifest(ManifestConfig manifest) { this.manifest = manifest; }
            
            public static class ManifestConfig {
                private boolean addLicenseHeaders = true;
                private boolean updateBundleVersion = true; // 默认启用版本更新
                private java.util.Map<String, String> customHeaders = new java.util.HashMap<>();
                
                public ManifestConfig() {
                    customHeaders.put("ALM-License-Required", "true");
                    customHeaders.put("ALM-License-Version", "1.0");
                }
                
                public boolean isAddLicenseHeaders() { return addLicenseHeaders; }
                public void setAddLicenseHeaders(boolean addLicenseHeaders) { 
                    this.addLicenseHeaders = addLicenseHeaders; 
                }
                
                public boolean isUpdateBundleVersion() { return updateBundleVersion; }
                public void setUpdateBundleVersion(boolean updateBundleVersion) { 
                    this.updateBundleVersion = updateBundleVersion; 
                }
                
                public java.util.Map<String, String> getCustomHeaders() { return customHeaders; }
                public void setCustomHeaders(java.util.Map<String, String> customHeaders) { 
                    this.customHeaders = customHeaders; 
                }
            }
        }
    }
    
    /**
     * 验证配置
     */
    public static class ValidationConfig {
        private boolean enabled = true;
        private java.util.List<String> checks = new java.util.ArrayList<>();
        
        public ValidationConfig() {
            checks.add("PLUGIN_COMPLETENESS");
            checks.add("LICENSE_INTEGRITY");
            checks.add("DEPENDENCY_RESOLUTION");
            checks.add("MANIFEST_VALIDATION");
            checks.add("CLASS_LOADING");
        }
        
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        
        public java.util.List<String> getChecks() { return checks; }
        public void setChecks(java.util.List<String> checks) { this.checks = checks; }
    }
    
    /**
     * 日志配置
     */
    public static class LoggingConfig {
        private String level = "INFO";
        private String format = "DETAILED";
        private String file = "./logs/license-builder.log";
        private boolean console = true;
        
        public String getLevel() { return level; }
        public void setLevel(String level) { this.level = level; }
        
        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }
        
        public String getFile() { return file; }
        public void setFile(String file) { this.file = file; }
        
        public boolean isConsole() { return console; }
        public void setConsole(boolean console) { this.console = console; }
    }
    
    /**
     * JAR数字签名配置
     */
    public static class SigningConfig {
        private boolean enabled = false;
        private String keystorePath;
        private String keystorePassword;
        private String keyAlias;
        private String keyPassword;
        private String keystoreType = "JKS";
        private String signatureAlgorithm = "SHA256withRSA";
        private String digestAlgorithm = "SHA-256";
        private String timestampUrl;
        private boolean verifySignature = true;
        
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        
        public String getKeystorePath() { return keystorePath; }
        public void setKeystorePath(String keystorePath) { this.keystorePath = keystorePath; }
        
        public String getKeystorePassword() { return keystorePassword; }
        public void setKeystorePassword(String keystorePassword) { this.keystorePassword = keystorePassword; }
        
        public String getKeyAlias() { return keyAlias; }
        public void setKeyAlias(String keyAlias) { this.keyAlias = keyAlias; }
        
        public String getKeyPassword() { return keyPassword; }
        public void setKeyPassword(String keyPassword) { this.keyPassword = keyPassword; }
        
        public String getKeystoreType() { return keystoreType; }
        public void setKeystoreType(String keystoreType) { this.keystoreType = keystoreType; }
        
        public String getSignatureAlgorithm() { return signatureAlgorithm; }
        public void setSignatureAlgorithm(String signatureAlgorithm) { this.signatureAlgorithm = signatureAlgorithm; }
        
        public String getDigestAlgorithm() { return digestAlgorithm; }
        public void setDigestAlgorithm(String digestAlgorithm) { this.digestAlgorithm = digestAlgorithm; }
        
        public String getTimestampUrl() { return timestampUrl; }
        public void setTimestampUrl(String timestampUrl) { this.timestampUrl = timestampUrl; }
        
        public boolean isVerifySignature() { return verifySignature; }
        public void setVerifySignature(boolean verifySignature) { this.verifySignature = verifySignature; }
    }
}