package com.fasnote.alm.plugin.manage.web.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Spring MVC配置类 手动配置Bean，集成现有的injection依赖注入系统
 */
@Configuration
@EnableWebMvc
public class WebConfig implements WebMvcConfigurer {

	/**
	 * 配置CORS支持
	 */
	@Override
	public void addCorsMappings(CorsRegistry registry) {
		registry.addMapping("/api/**").allowedOrigins("*").allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
				.allowedHeaders("*").allowCredentials(false).maxAge(3600);
	}

	/**
	 * 配置静态资源处理
	 */
	@Override
	public void addResourceHandlers(ResourceHandlerRegistry registry) {
		// 配置前端静态资源
		registry.addResourceHandler("/**").addResourceLocations("/");
	}

	/**
	 * 许可证管理框架Bean 暂时禁用，因为正在迁移到新的依赖注入架构
	 */
	/*
	 * @Bean public LicenseFrameworkFacade licenseFrameworkFacade() { return
	 * LicenseFrameworkFacade.getInstance(); }
	 *
	 * /** 插件管理服务Bean
	 */
	/*
	 * @Bean public PluginManagementService pluginManagementService() { return new
	 * PluginManagementServiceImpl(licenseFrameworkFacade()); }
	 *
	 * /** 插件管理控制器Bean
	 */
	/*
	 * @Bean public PluginManagementController pluginManagementController() { return
	 * new PluginManagementController(pluginManagementService()); }
	 */
}
