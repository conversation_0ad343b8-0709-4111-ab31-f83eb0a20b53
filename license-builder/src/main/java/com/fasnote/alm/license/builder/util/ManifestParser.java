package com.fasnote.alm.license.builder.util;

import com.fasnote.alm.license.builder.model.BundleInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.jar.Attributes;
import java.util.jar.Manifest;

/**
 * 极简的Manifest解析器
 * 只解析许可证构建器真正需要的字段
 */
public class ManifestParser {

    /**
     * 从Manifest解析Bundle信息
     * 解析所有字段以兼容现有代码，但使用简化的解析逻辑
     */
    public static BundleInfo parseManifest(Manifest manifest) {
        if (manifest == null) {
            return null;
        }

        Attributes mainAttributes = manifest.getMainAttributes();
        BundleInfo bundleInfo = new BundleInfo();

        // 解析基本信息
        bundleInfo.setSymbolicName(parseSymbolicName(mainAttributes.getValue("Bundle-SymbolicName")));
        bundleInfo.setVersion(mainAttributes.getValue("Bundle-Version"));
        bundleInfo.setName(mainAttributes.getValue("Bundle-Name"));
        bundleInfo.setVendor(mainAttributes.getValue("Bundle-Vendor"));
        bundleInfo.setActivator(mainAttributes.getValue("Bundle-Activator"));
        bundleInfo.setExecutionEnvironment(mainAttributes.getValue("Bundle-RequiredExecutionEnvironment"));
        bundleInfo.setBundleClassPath(mainAttributes.getValue("Bundle-ClassPath"));

        // 解析Require-Bundle
        String requireBundle = mainAttributes.getValue("Require-Bundle");
        if (requireBundle != null) {
            bundleInfo.setRequiredBundles(parseRequireBundles(requireBundle));
        }

        // 解析Export-Package和Import-Package（简化版本）
        String exportPackage = mainAttributes.getValue("Export-Package");
        if (exportPackage != null) {
            bundleInfo.setExportedPackages(parseSimplePackages(exportPackage));
        }

        String importPackage = mainAttributes.getValue("Import-Package");
        if (importPackage != null) {
            bundleInfo.setImportedPackages(parseSimplePackages(importPackage));
        }

        // 保存原始Manifest
        bundleInfo.setManifest(manifest);

        return bundleInfo;
    }
    
    /**
     * 解析Bundle-SymbolicName，移除参数
     */
    private static String parseSymbolicName(String symbolicName) {
        if (symbolicName == null) {
            return null;
        }
        
        // 移除分号后的参数
        int semicolonIndex = symbolicName.indexOf(';');
        if (semicolonIndex > 0) {
            return symbolicName.substring(0, semicolonIndex).trim();
        }
        
        return symbolicName.trim();
    }
    
    /**
     * 解析Require-Bundle条目（简化版本）
     */
    private static List<String> parseRequireBundles(String requireBundle) {
        List<String> bundles = new ArrayList<>();
        
        if (requireBundle == null || requireBundle.trim().isEmpty()) {
            return bundles;
        }
        
        // 简单按逗号分割
        String[] parts = requireBundle.split(",");
        
        for (String part : parts) {
            String trimmed = part.trim();
            if (!trimmed.isEmpty()) {
                bundles.add(trimmed);
            }
        }
        
        return bundles;
    }

    /**
     * 解析包列表（简化版本）
     */
    private static List<String> parseSimplePackages(String packages) {
        List<String> packageList = new ArrayList<>();

        if (packages == null || packages.trim().isEmpty()) {
            return packageList;
        }

        // 简单按逗号分割，提取包名
        String[] parts = packages.split(",");

        for (String part : parts) {
            String trimmed = part.trim();
            if (!trimmed.isEmpty()) {
                // 提取包名（分号前的部分）
                int semicolonIndex = trimmed.indexOf(';');
                String packageName = semicolonIndex > 0 ?
                    trimmed.substring(0, semicolonIndex).trim() : trimmed;

                if (!packageName.isEmpty()) {
                    packageList.add(packageName);
                }
            }
        }

        return packageList;
    }

}
