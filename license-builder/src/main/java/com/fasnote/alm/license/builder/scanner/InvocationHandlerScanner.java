package com.fasnote.alm.license.builder.scanner;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

import org.objectweb.asm.ClassReader;
import org.objectweb.asm.ClassVisitor;
import org.objectweb.asm.Opcodes;

/**
 * 扫描器，用于检测实现了 InvocationHandler 接口的类
 */
public class InvocationHandlerScanner {
    private static final Logger logger = LoggerFactory.getLogger(InvocationHandlerScanner.class);
    
    private static final String INVOCATION_HANDLER_INTERFACE = "java/lang/reflect/InvocationHandler";
    
    /**
     * 扫描JAR文件中实现了InvocationHandler的类
     * 
     * @param jarPath JAR文件路径
     * @return 实现了InvocationHandler的类名列表
     */
    public List<String> scanJarFile(Path jarPath) {
        List<String> invocationHandlerClasses = new ArrayList<>();
        
        if (!Files.exists(jarPath)) {
            logger.warn("JAR文件不存在: {}", jarPath);
            return invocationHandlerClasses;
        }
        
        try (JarFile jarFile = new JarFile(jarPath.toFile())) {
            Enumeration<JarEntry> entries = jarFile.entries();
            
            while (entries.hasMoreElements()) {
                JarEntry entry = entries.nextElement();
                
                if (entry.getName().endsWith(".class") && !entry.isDirectory()) {
                    try (InputStream inputStream = jarFile.getInputStream(entry)) {
                        String className = analyzeClass(inputStream);
                        if (className != null) {
                            invocationHandlerClasses.add(className);
                            logger.info("发现InvocationHandler实现类: {}", className);
                        }
                    } catch (Exception e) {
                        logger.debug("分析类文件失败: {} - {}", entry.getName(), e.getMessage());
                    }
                }
            }
        } catch (IOException e) {
            logger.error("读取JAR文件失败: {} - {}", jarPath, e.getMessage());
        }
        
        return invocationHandlerClasses;
    }
    
    /**
     * 扫描目录中的class文件
     * 
     * @param classesDir 类文件目录
     * @return 实现了InvocationHandler的类名列表
     */
    public List<String> scanDirectory(Path classesDir) {
        List<String> invocationHandlerClasses = new ArrayList<>();
        
        if (!Files.exists(classesDir) || !Files.isDirectory(classesDir)) {
            logger.warn("类文件目录不存在: {}", classesDir);
            return invocationHandlerClasses;
        }
        
        try {
            Files.walk(classesDir)
                .filter(path -> path.toString().endsWith(".class"))
                .forEach(classFile -> {
                    try (InputStream inputStream = Files.newInputStream(classFile)) {
                        String className = analyzeClass(inputStream);
                        if (className != null) {
                            invocationHandlerClasses.add(className);
                            logger.info("发现InvocationHandler实现类: {}", className);
                        }
                    } catch (Exception e) {
                        logger.debug("分析类文件失败: {} - {}", classFile, e.getMessage());
                    }
                });
        } catch (IOException e) {
            logger.error("扫描目录失败: {} - {}", classesDir, e.getMessage());
        }
        
        return invocationHandlerClasses;
    }
    
    /**
     * 分析单个类文件，检查是否实现了InvocationHandler接口
     * 
     * @param inputStream 类文件输入流
     * @return 如果实现了InvocationHandler则返回类名，否则返回null
     */
    private String analyzeClass(InputStream inputStream) throws IOException {
        ClassReader classReader = new ClassReader(inputStream);
        InvocationHandlerClassVisitor visitor = new InvocationHandlerClassVisitor();
        classReader.accept(visitor, ClassReader.SKIP_CODE | ClassReader.SKIP_DEBUG | ClassReader.SKIP_FRAMES);
        
        return visitor.isInvocationHandler() ? visitor.getClassName() : null;
    }
    
    /**
     * ASM ClassVisitor，用于检测InvocationHandler接口的实现
     */
    private static class InvocationHandlerClassVisitor extends ClassVisitor {
        private String className;
        private boolean isInvocationHandler = false;
        
        public InvocationHandlerClassVisitor() {
            super(Opcodes.ASM9);
        }
        
        @Override
        public void visit(int version, int access, String name, String signature, 
                         String superName, String[] interfaces) {
            this.className = name.replace('/', '.');
            
            // 检查是否直接实现了InvocationHandler接口
            if (interfaces != null) {
                for (String interfaceName : interfaces) {
                    if (INVOCATION_HANDLER_INTERFACE.equals(interfaceName)) {
                        this.isInvocationHandler = true;
                        break;
                    }
                }
            }
        }
        
        public String getClassName() {
            return className;
        }
        
        public boolean isInvocationHandler() {
            return isInvocationHandler;
        }
    }
    
    /**
     * 生成ProGuard保护规则
     * 
     * @param invocationHandlerClasses InvocationHandler实现类列表
     * @return ProGuard规则列表
     */
    public List<String> generateProGuardRules(List<String> invocationHandlerClasses) {
        List<String> rules = new ArrayList<>();
        
        for (String className : invocationHandlerClasses) {
            String rule = String.format(
                "-keep class %s { public java.lang.Object invoke(java.lang.Object, java.lang.reflect.Method, java.lang.Object[]); }",
                className
            );
            rules.add(rule);
        }
        
        return rules;
    }
}
