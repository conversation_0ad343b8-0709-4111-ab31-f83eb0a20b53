package com.fasnote.alm.license.builder.packager;

import com.fasnote.alm.license.builder.core.BuildConfiguration;
import com.fasnote.alm.license.builder.exception.PackagingException;
import com.fasnote.alm.license.builder.model.BundleInfo;
import com.fasnote.alm.license.builder.model.SeparationResult;
import com.fasnote.alm.license.crypto.RSALicenseEncryption;
import com.fasnote.alm.license.crypto.RSAKeyManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import java.util.zip.GZIPOutputStream;

/**
 * 许可证打包器
 * 负责打包许可证文件
 */
public class LicensePackager {

    private static final Logger logger = LoggerFactory.getLogger(LicensePackager.class);

    private final BuildConfiguration config;
    private final RSALicenseEncryption rsaEncryption;

    public LicensePackager(BuildConfiguration config) {
        this.config = config;
        // 支持自定义密钥路径
        String keyPath = config.getPackaging().getLicense().getMetadata().get("keyPath");
        logger.debug("从配置中获取的密钥路径: {}", keyPath);
        logger.debug("配置中的metadata内容: {}", config.getPackaging().getLicense().getMetadata());
        RSAKeyManager keyManager = new RSAKeyManager(keyPath);
        this.rsaEncryption = new RSALicenseEncryption(keyManager);
    }

    /**
     * 无参构造函数 - 用于BuildContext自动创建
     * 使用默认配置和系统属性中的密钥路径
     */
    public LicensePackager() {
        // 从系统属性获取密钥路径
        String keyPath = System.getProperty("packaging.license.metadata.keyPath");
        logger.debug("从系统属性获取的密钥路径: {}", keyPath);

        // 创建默认配置
        this.config = createDefaultConfiguration();

        // 如果系统属性中没有密钥路径，尝试从配置中获取
        if (keyPath == null) {
            keyPath = config.getPackaging().getLicense().getMetadata().get("keyPath");
            logger.debug("从配置中获取的密钥路径: {}", keyPath);
        }

        RSAKeyManager keyManager = new RSAKeyManager(keyPath);
        this.rsaEncryption = new RSALicenseEncryption(keyManager);
    }

    /**
     * 创建默认配置
     */
    private BuildConfiguration createDefaultConfiguration() {
        BuildConfiguration config = new BuildConfiguration();

        // 设置默认的输出目录
        config.getBuild().setOutputDir("./target/license-output");

        // 设置默认的许可证元数据
        Map<String, String> metadata = new HashMap<>();
        metadata.put("generatedAt", "${timestamp}");
        metadata.put("generator", "ALM License Builder");
        metadata.put("version", "1.0");

        // 从系统属性获取密钥路径，如果没有则使用默认路径
        String keyPath = System.getProperty("packaging.license.metadata.keyPath");
        if (keyPath != null && !keyPath.trim().isEmpty()) {
            metadata.put("keyPath", keyPath.trim());
        } else {
            // 使用默认密钥路径
            metadata.put("keyPath", System.getProperty("user.home") + "/opt/license");
        }

        config.getPackaging().getLicense().setMetadata(metadata);

        return config;
    }

    /**
     * 打包许可证文件
     */
    public void packageLicense(BundleInfo bundleInfo, SeparationResult separationResult) throws PackagingException {
        try {
            logger.info("开始打包许可证文件: {}", bundleInfo.getSymbolicName());
            
            // 生成输出文件名
            String outputName = generateOutputName(bundleInfo);
            Path outputPath = Paths.get(config.getBuild().getOutputDir()).resolve(outputName);
            
            // 创建许可证文件
            createLicenseFile(outputPath, bundleInfo, separationResult);
            
            logger.info("许可证文件打包完成: {}", outputPath);
            
        } catch (Exception e) {
            throw new PackagingException("许可证打包失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 生成输出文件名
     */
    private String generateOutputName(BundleInfo bundleInfo) {
        String template = config.getPackaging().getLicense().getOutputName();

        // 获取实际的版本号（处理 qualifier 时间戳替换）
        String actualVersion = com.fasnote.alm.license.builder.util.VersionUtils.getActualVersion(bundleInfo);

        // 替换模板变量
        return template
            .replace("${bundle.symbolicName}", bundleInfo.getSymbolicName())
            .replace("${bundle.version}", actualVersion);
    }
    
    /**
     * 创建许可证文件
     */
    private void createLicenseFile(Path outputPath, BundleInfo bundleInfo, SeparationResult separationResult) throws IOException {
        
        // 确保输出目录存在
        Files.createDirectories(outputPath.getParent());
        
        // 创建许可证包数据
        LicensePackageData packageData = createLicensePackageData(bundleInfo, separationResult);
        
        // 根据格式写入文件
        String format = config.getPackaging().getLicense().getFormat();
        switch (format.toUpperCase()) {
            case "BINARY":
                writeBinaryFormat(outputPath, packageData);
                break;
            case "JSON":
                writeJsonFormat(outputPath, packageData);
                break;
            default:
                throw new IOException("不支持的许可证格式: " + format);
        }
    }
    
    /**
     * 创建许可证包数据
     */
    private LicensePackageData createLicensePackageData(BundleInfo bundleInfo, SeparationResult separationResult) {
        LicensePackageData packageData = new LicensePackageData();
        
        // 设置元数据
        packageData.metadata = new HashMap<>();
        packageData.metadata.putAll(config.getPackaging().getLicense().getMetadata());
        packageData.metadata.put("bundle.symbolicName", bundleInfo.getSymbolicName());
        packageData.metadata.put("bundle.version", bundleInfo.getVersion());
        packageData.metadata.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        
        // 设置类数据
        packageData.licensedClasses = separationResult.getLicensedClasses();
        
        return packageData;
    }
    
    /**
     * 写入二进制格式
     */
    private void writeBinaryFormat(Path outputPath, LicensePackageData packageData) throws IOException {
        logger.debug("写入二进制格式许可证文件");
        
        OutputStream os = Files.newOutputStream(outputPath);
        
        // 如果启用压缩
        if (config.getPackaging().getLicense().isCompressed()) {
            os = new GZIPOutputStream(os);
        }
        
        try (ObjectOutputStream oos = new ObjectOutputStream(os)) {
            oos.writeObject(packageData);
        }
        
        logger.debug("二进制格式写入完成");
    }
    
    /**
     * 写入JSON格式
     */
    private void writeJsonFormat(Path outputPath, LicensePackageData packageData) throws IOException {
        logger.debug("写入JSON格式许可证文件");
        
        try {
            // 创建符合manage端PluginLicense格式的JSON
            Map<String, Object> licenseJson = createPluginLicenseJson(packageData);
            
            // 转换为JSON字符串
            String jsonContent = toJsonString(licenseJson);
            
            // 写入文件
            Files.write(outputPath, jsonContent.getBytes(StandardCharsets.UTF_8));
            
            logger.debug("JSON格式写入完成");
            
        } catch (Exception e) {
            throw new IOException("JSON格式写入失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建符合PluginLicense格式的JSON对象
     */
    private Map<String, Object> createPluginLicenseJson(LicensePackageData packageData) throws IOException {
        Map<String, Object> json = new HashMap<>();
        
        // 从metadata中提取基本信息
        String bundleName = packageData.metadata.get("bundle.symbolicName");
        String bundleVersion = packageData.metadata.get("bundle.version");
        String timestamp = packageData.metadata.get("timestamp");
        
        // 基本信息
        json.put("pluginId", bundleName);
        json.put("productName", packageData.metadata.getOrDefault("productName", bundleName));
        json.put("version", bundleVersion);
        json.put("licenseType", packageData.metadata.getOrDefault("licenseType", "Commercial"));
        json.put("issuer", packageData.metadata.getOrDefault("issuer", "ALM License Builder"));
        
        // 时间信息 - 修正格式为plugin.manage期望的格式
        LocalDateTime issueTime = LocalDateTime.parse(timestamp, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        json.put("issueDate", issueTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 生效时间（默认与签发时间相同）
        String effectiveDate = packageData.metadata.get("effectiveDate");
        if (effectiveDate != null) {
            json.put("effectiveDate", effectiveDate);
        } else {
            json.put("effectiveDate", issueTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }

        // 过期时间
        String validity = config.getPackaging().getLicense().getMetadata().get("validity");
        if (validity != null && !"UNLIMITED".equals(validity)) {
            // 计算过期时间
            LocalDateTime expiryTime = calculateExpiryDate(issueTime, validity);
            json.put("expiryDate", expiryTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        
        // 安全信息
        json.put("machineCode", packageData.metadata.getOrDefault("machineCode", ""));
        json.put("signature", packageData.metadata.getOrDefault("signature", ""));
        json.put("contentHash", packageData.metadata.getOrDefault("contentHash", ""));
        
        // 用户信息
        json.put("licensedTo", packageData.metadata.getOrDefault("licensedTo", ""));
        json.put("organization", packageData.metadata.getOrDefault("organization", ""));
        json.put("maxUsers", Integer.parseInt(packageData.metadata.getOrDefault("maxUsers", "1")));
        
        // 功能和限制（默认值）
        json.put("features", new HashMap<String, Object>());
        json.put("limitations", new HashMap<String, Object>());
        json.put("customProperties", new HashMap<String, Object>());
        
        // 使用纯RSA加密方案
        return createRSAEncryptedLicenseJson(packageData, json);
    }

    /**
     * 创建使用纯RSA加密的许可证JSON
     */
    private Map<String, Object> createRSAEncryptedLicenseJson(LicensePackageData packageData, Map<String, Object> json) throws IOException {
        try {
            logger.info("使用crypto模块统一加密方案生成许可证");

            // 直接使用crypto模块的标准化接口，让crypto模块统一管理所有加密相关逻辑
            RSALicenseEncryption.EncryptedLicensePackage encryptedPackage =
                rsaEncryption.encryptAndSignLicensePackage(packageData.licensedClasses, json);

            // 设置crypto模块生成的加密和签名信息
            json.put("encryptedClassData", encryptedPackage.getEncryptedClassData());
            json.put("signature", encryptedPackage.getDigitalSignature());

            // 将加密相关信息放在customProperties中（保持与manage端的兼容性）
            @SuppressWarnings("unchecked")
            Map<String, Object> customProperties = (Map<String, Object>) json.get("customProperties");
            if (customProperties == null) {
                customProperties = new HashMap<>();
                json.put("customProperties", customProperties);
            }
            customProperties.put("encryptionVersion", encryptedPackage.getEncryptionVersion());
            customProperties.put("keyVersion", encryptedPackage.getKeyVersion());
            customProperties.put("digitalSignature", encryptedPackage.getDigitalSignature());
            customProperties.put("signatureAlgorithm", encryptedPackage.getSignatureAlgorithm());

            // 创建服务映射（从配置或类名推断）
            Map<String, String> serviceMappings = createServiceMappings(packageData.licensedClasses);
            json.put("serviceMappings", serviceMappings);

            logger.info("纯RSA加密许可证生成完成");
            return json;

        } catch (Exception e) {
            logger.error("纯RSA加密许可证生成失败", e);
            throw new IOException("纯RSA加密许可证生成失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 计算过期时间
     */
    private LocalDateTime calculateExpiryDate(LocalDateTime issueTime, String validity) {
        switch (validity) {
            case "DAYS_30":
                return issueTime.plusDays(30);
            case "DAYS_90":
                return issueTime.plusDays(90);
            case "DAYS_365":
                return issueTime.plusDays(365);
            default:
                return issueTime.plusYears(1); // 默认1年
        }
    }
    

    
    /**
     * 创建服务映射
     */
    private Map<String, String> createServiceMappings(Map<String, byte[]> classes) {
        Map<String, String> mappings = new HashMap<>();



        // 分析每个类，查找带有 @LicenseImplementation 注解的类
        for (Map.Entry<String, byte[]> entry : classes.entrySet()) {
            String classPath = entry.getKey(); // 这是文件路径格式，如 com/fasnote/alm/auth/feishu/FeishuLicensedAuthenticatorEnhancer.class
            byte[] classBytes = entry.getValue();

            try {
                // 将文件路径转换为Java类名
                String className = convertPathToClassName(classPath);

                // 分析类的接口和注解
                Set<String> interfaces = analyzeClassInterfaces(classBytes);
                boolean hasLicenseAnnotation = hasLicenseImplementationAnnotation(classBytes);

                if (hasLicenseAnnotation && !interfaces.isEmpty()) {
                    // 为每个接口创建映射，使用Java类名格式
                    for (String interfaceName : interfaces) {
                        mappings.put(interfaceName, className);
                        logger.debug("创建服务映射: {} -> {}", interfaceName, className);
                    }
                }
            } catch (Exception e) {
                logger.debug("分析类 {} 失败: {}", classPath, e.getMessage());
            }
        }

        // 如果没有找到基于注解的映射，回退到简单的命名约定
        if (mappings.isEmpty()) {
            logger.debug("未找到基于注解的服务映射，使用命名约定");
            for (String classPath : classes.keySet()) {
                String className = convertPathToClassName(classPath);
                if (className.endsWith("Impl")) {
                    String interfaceName = className.substring(0, className.length() - 4);
                    // 查找对应的接口
                    if (interfaceName.startsWith("I")) {
                        mappings.put(interfaceName, className);
                    } else {
                        mappings.put("I" + interfaceName, className);
                    }
                }
            }
        }

        logger.info("生成服务映射数量: {}", mappings.size());
        for (Map.Entry<String, String> mapping : mappings.entrySet()) {
            logger.info("服务映射: {} -> {}", mapping.getKey(), mapping.getValue());
        }

        return mappings;
    }

    /**
     * 将文件路径转换为Java类名
     * 例如：com/fasnote/alm/auth/feishu/FeishuLicensedAuthenticatorEnhancer.class
     * 转换为：com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer
     */
    private String convertPathToClassName(String classPath) {
        if (classPath.endsWith(".class")) {
            classPath = classPath.substring(0, classPath.length() - 6); // 移除 .class 后缀
        }
        return classPath.replace('/', '.'); // 将路径分隔符替换为包分隔符
    }

    /**
     * 分析类实现的接口
     */
    private Set<String> analyzeClassInterfaces(byte[] classBytes) {
        Set<String> interfaces = new HashSet<>();

        try {
            // 简单的字节码分析，查找接口信息
            // 这里使用简化的方法，实际项目中可以使用ASM等库进行更精确的分析
            String classContent = new String(classBytes, "ISO-8859-1");

            // 查找常见的接口模式
            if (classContent.contains("IFeishuAuthenticatorEnhancer")) {
                interfaces.add("com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer");
            }

            // 可以添加更多接口检测逻辑

        } catch (Exception e) {
            logger.debug("分析类接口失败: {}", e.getMessage());
        }

        return interfaces;
    }

    /**
     * 检查类是否有 @LicenseImplementation 注解
     */
    private boolean hasLicenseImplementationAnnotation(byte[] classBytes) {
        try {
            // 简单的字节码分析，查找注解信息
            String classContent = new String(classBytes, "ISO-8859-1");

            // 查找 LicenseImplementation 注解
            return classContent.contains("LicenseImplementation");

        } catch (Exception e) {
            logger.debug("分析类注解失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 简单的JSON字符串转换
     */
    private String toJsonString(Map<String, Object> data) {
        StringBuilder json = new StringBuilder();
        json.append("{");
        
        boolean first = true;
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            if (!first) {
                json.append(",");
            }
            first = false;
            
            json.append("\"").append(entry.getKey()).append("\": ");
            Object value = entry.getValue();
            
            if (value instanceof String) {
                json.append("\"").append(escapeJson((String) value)).append("\"");
            } else if (value instanceof Number) {
                json.append(value);
            } else if (value instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> mapValue = (Map<String, Object>) value;
                json.append(toJsonString(mapValue));
            } else {
                json.append("\"").append(String.valueOf(value)).append("\"");
            }
        }
        
        json.append("}");
        return json.toString();
    }
    
    /**
     * JSON字符串转义
     */
    private String escapeJson(String str) {
        if (str == null) return "";
        return str.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }


    
    /**
     * 许可证包数据结构
     */
    private static class LicensePackageData implements Serializable {
        private static final long serialVersionUID = 1L;
        
        public Map<String, String> metadata;
        public Map<String, byte[]> licensedClasses;
    }
}