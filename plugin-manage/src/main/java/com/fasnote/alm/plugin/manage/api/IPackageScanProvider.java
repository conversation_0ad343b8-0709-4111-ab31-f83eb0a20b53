package com.fasnote.alm.plugin.manage.api;

/**
 * 包扫描提供者接口
 *
 * 业务插件通过实现此接口来声明需要扫描的包路径， 替代系统属性配置，更符合 OSGi 架构。
 */
public interface IPackageScanProvider {

	/**
	 * 获取提供者名称
	 *
	 * @return 提供者名称
	 */
	String getName();

	/**
	 * 获取插件ID
	 *
	 * @return 插件ID
	 */
	String getPluginId();

	/**
	 * 获取优先级
	 *
	 * @return 优先级（数字越小优先级越高）
	 */
	default int getPriority() {
		return 100;
	}

	/**
	 * 获取需要扫描的包路径
	 *
	 * @return 包路径数组
	 */
	String[] getScanPackages();
}
