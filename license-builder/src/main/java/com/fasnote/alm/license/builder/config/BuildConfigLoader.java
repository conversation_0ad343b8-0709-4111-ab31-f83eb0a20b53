package com.fasnote.alm.license.builder.config;

import com.fasnote.alm.license.builder.core.BuildConfiguration;
import com.fasnote.alm.license.builder.exception.BuildException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import org.apache.commons.cli.CommandLine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * 构建配置加载器
 * 负责从各种来源加载配置信息
 */
public class BuildConfigLoader {
    
    private static final Logger logger = LoggerFactory.getLogger(BuildConfigLoader.class);
    
    private final ObjectMapper yamlMapper;
    
    public BuildConfigLoader() {
        this.yamlMapper = new ObjectMapper(new YAMLFactory());
    }
    
    /**
     * 从文件加载配置
     */
    public BuildConfiguration loadFromFile(Path configPath) throws BuildException {
        try {
            logger.info("从文件加载配置: {}", configPath);
            
            if (!Files.exists(configPath)) {
                throw new BuildException("配置文件不存在: " + configPath);
            }
            
            BuildConfiguration config = yamlMapper.readValue(configPath.toFile(), BuildConfiguration.class);
            
            // 验证配置
            validateConfiguration(config);
            
            logger.debug("配置加载完成");
            return config;
            
        } catch (IOException e) {
            throw new BuildException("配置文件读取失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从命令行参数创建配置
     */
    public BuildConfiguration createFromCommandLine(CommandLine cmd) throws BuildException {
        logger.info("从命令行参数创建配置");
        
        BuildConfiguration config = new BuildConfiguration();
        
        // 设置基本配置
        if (cmd.hasOption("i")) {
            config.getInput().setSourceJar(cmd.getOptionValue("i"));
        }

        if (cmd.hasOption("o")) {
            config.getBuild().setOutputDir(cmd.getOptionValue("o"));
        }

        if (cmd.hasOption("e")) {
            config.getInput().setEclipsePath(cmd.getOptionValue("e"));
        }

        // 从系统属性读取配置
        applySystemProperties(config);

        // 验证配置
        validateConfiguration(config);
        
        return config;
    }
    
    /**
     * 从系统属性应用配置
     */
    private void applySystemProperties(BuildConfiguration config) {
        // 应用RSA密钥路径配置
        String keyPath = System.getProperty("packaging.license.metadata.keyPath");
        logger.debug("读取系统属性 packaging.license.metadata.keyPath: {}", keyPath);
        logger.debug("应用前metadata内容: {}", config.getPackaging().getLicense().getMetadata());

        if (keyPath != null && !keyPath.trim().isEmpty()) {
            config.getPackaging().getLicense().getMetadata().put("keyPath", keyPath.trim());
            logger.debug("从系统属性设置RSA密钥路径: {}", keyPath);
            logger.debug("应用后metadata内容: {}", config.getPackaging().getLicense().getMetadata());
        } else {
            // 使用默认密钥路径
            String defaultKeyPath = System.getProperty("user.home") + "/opt/license";
            config.getPackaging().getLicense().getMetadata().put("keyPath", defaultKeyPath);
            logger.debug("系统属性 packaging.license.metadata.keyPath 为空或未设置，使用默认路径: {}", defaultKeyPath);
            logger.debug("应用后metadata内容: {}", config.getPackaging().getLicense().getMetadata());
        }

        // 可以在这里添加其他系统属性的处理
        logger.debug("系统属性应用完成");
    }

    /**
     * 验证配置
     */
    private void validateConfiguration(BuildConfiguration config) throws BuildException {
        if (config.getInput().getSourceJar() == null || config.getInput().getSourceJar().isEmpty()) {
            throw new BuildException("输入JAR文件路径不能为空");
        }
        
        if (config.getBuild().getOutputDir() == null || config.getBuild().getOutputDir().isEmpty()) {
            throw new BuildException("输出目录不能为空");
        }
        
        logger.debug("配置验证通过");
    }
    
    /**
     * 创建默认配置
     */
    public BuildConfiguration createDefaultConfiguration() {
        logger.info("创建默认配置");
        
        BuildConfiguration config = new BuildConfiguration();
        
        // 设置默认值已在BuildConfiguration构造函数中完成
        
        return config;
    }
}