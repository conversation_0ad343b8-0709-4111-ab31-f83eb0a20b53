package com.fasnote.alm.license.builder.template;

import java.util.*;

/**
 * 模板上下文 - 管理模板变量和数据
 */
public class TemplateContext {
    private final Map<String, Object> variables;
    private final TemplateContext parent;
    
    public TemplateContext() {
        this.variables = new HashMap<>();
        this.parent = null;
    }
    
    private TemplateContext(TemplateContext parent) {
        this.variables = new HashMap<>();
        this.parent = parent;
    }
    
    /**
     * 创建子上下文（用于循环渲染等场景）
     */
    public TemplateContext createChild() {
        return new TemplateContext(this);
    }
    
    /**
     * 设置变量
     */
    public TemplateContext put(String key, Object value) {
        variables.put(key, value);
        return this;
    }
    
    /**
     * 批量设置变量
     */
    public TemplateContext putAll(Map<String, Object> vars) {
        variables.putAll(vars);
        return this;
    }
    
    /**
     * 获取变量值
     */
    public Object get(String key) {
        Object value = variables.get(key);
        if (value == null && parent != null) {
            value = parent.get(key);
        }
        return value;
    }
    
    /**
     * 获取字符串变量
     */
    public String getString(String key) {
        Object value = get(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * 获取字符串变量（带默认值）
     */
    public String getString(String key, String defaultValue) {
        String value = getString(key);
        return value != null ? value : defaultValue;
    }
    
    /**
     * 获取布尔变量
     */
    public boolean getBoolean(String key) {
        Object value = get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return false;
    }
    
    /**
     * 获取整数变量
     */
    public int getInt(String key) {
        Object value = get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        return 0;
    }
    
    /**
     * 获取列表变量
     */
    @SuppressWarnings("unchecked")
    public List<Object> getList(String key) {
        Object value = get(key);
        if (value instanceof List) {
            return (List<Object>) value;
        } else if (value instanceof Collection) {
            return new ArrayList<>((Collection<?>) value);
        } else if (value != null) {
            // 单个值包装为列表
            return Arrays.asList(value);
        }
        return null;
    }
    
    /**
     * 获取Map变量
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getMap(String key) {
        Object value = get(key);
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        return null;
    }
    
    /**
     * 检查变量是否为真值
     */
    public boolean isTrue(String key) {
        Object value = get(key);
        if (value == null) {
            return false;
        } else if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            String str = (String) value;
            return !str.isEmpty() && !str.equalsIgnoreCase("false") && !str.equals("0");
        } else if (value instanceof Number) {
            return ((Number) value).doubleValue() != 0;
        } else if (value instanceof Collection) {
            return !((Collection<?>) value).isEmpty();
        } else if (value instanceof Map) {
            return !((Map<?, ?>) value).isEmpty();
        }
        return true; // 非空对象默认为真
    }
    
    /**
     * 检查变量是否存在
     */
    public boolean has(String key) {
        return variables.containsKey(key) || (parent != null && parent.has(key));
    }
    
    /**
     * 移除变量
     */
    public Object remove(String key) {
        return variables.remove(key);
    }
    
    /**
     * 清空所有变量
     */
    public void clear() {
        variables.clear();
    }
    
    /**
     * 获取所有变量名
     */
    public Set<String> keySet() {
        Set<String> keys = new HashSet<>(variables.keySet());
        if (parent != null) {
            keys.addAll(parent.keySet());
        }
        return keys;
    }
    
    /**
     * 获取变量数量
     */
    public int size() {
        return keySet().size();
    }
    
    /**
     * 检查是否为空
     */
    public boolean isEmpty() {
        return variables.isEmpty() && (parent == null || parent.isEmpty());
    }
    
    /**
     * 转换为Map
     */
    public Map<String, Object> toMap() {
        Map<String, Object> result = new HashMap<>();
        if (parent != null) {
            result.putAll(parent.toMap());
        }
        result.putAll(variables);
        return result;
    }
    
    @Override
    public String toString() {
        return "TemplateContext{" +
                "variables=" + variables +
                ", parent=" + (parent != null ? "exists" : "null") +
                '}';
    }
    
    /**
     * 便捷方法：创建包含Bundle信息的上下文
     */
    public static TemplateContext fromBundleInfo(com.fasnote.alm.license.builder.model.BundleInfo bundleInfo) {
        TemplateContext context = new TemplateContext();

        if (bundleInfo != null) {
            context.put("bundle_symbolic_name", bundleInfo.getSymbolicName());
            context.put("bundle_version", bundleInfo.getVersion());
            context.put("bundle_name", bundleInfo.getName());
            context.put("bundle_vendor", bundleInfo.getVendor());
            context.put("bundle_activator", bundleInfo.getActivator());

            // 解析执行环境并转换为ProGuard目标版本
            String executionEnvironment = bundleInfo.getExecutionEnvironment();
            String javaTargetVersion = "11"; // 默认值
            if (executionEnvironment != null) {
                String parsedVersion = convertExecutionEnvironmentToTargetVersion(executionEnvironment);
                if (parsedVersion != null) {
                    javaTargetVersion = parsedVersion;
                }
            }
            context.put("java_target_version", javaTargetVersion);
            
            // 导出包列表
            if (bundleInfo.getExportedPackages() != null) {
                List<Map<String, Object>> exportedPackages = new ArrayList<>();
                for (String pkg : bundleInfo.getExportedPackages()) {
                    Map<String, Object> packageInfo = new HashMap<>();
                    packageInfo.put("package_name", pkg.split(";")[0].trim());
                    packageInfo.put("full_declaration", pkg);
                    exportedPackages.add(packageInfo);
                }
                context.put("exported_packages", exportedPackages);
            }
            
            // 必需Bundle列表
            if (bundleInfo.getRequiredBundles() != null) {
                List<Map<String, Object>> requiredBundles = new ArrayList<>();
                for (String bundle : bundleInfo.getRequiredBundles()) {
                    Map<String, Object> bundleInfo2 = new HashMap<>();
                    bundleInfo2.put("bundle_name", bundle);
                    requiredBundles.add(bundleInfo2);
                }
                context.put("required_bundles", requiredBundles);
            }

            // XML配置文件引用的类
            if (bundleInfo.getXmlConfigInfo() != null) {
                com.fasnote.alm.license.builder.util.XmlConfigParser.XmlConfigInfo xmlConfig = bundleInfo.getXmlConfigInfo();

                // 所有XML配置类列表
                List<Map<String, Object>> xmlConfigClasses = new ArrayList<>();
                for (String className : xmlConfig.getAllClasses()) {
                    Map<String, Object> classInfo = new HashMap<>();
                    classInfo.put("class_name", className);
                    classInfo.put("package_name", getPackageName(className));
                    xmlConfigClasses.add(classInfo);
                }
                context.put("xml_config_classes", xmlConfigClasses);

                // 分类的XML配置类
                context.put("web_xml_classes", createClassInfoList(xmlConfig.getWebXmlClasses()));
                context.put("hive_module_classes", createClassInfoList(xmlConfig.getHiveModuleClasses()));
                context.put("plugin_xml_classes", createClassInfoList(xmlConfig.getPluginXmlClasses()));
            }
        }

        return context;
    }

    /**
     * 创建类信息列表
     */
    private static List<Map<String, Object>> createClassInfoList(java.util.Set<String> classNames) {
        List<Map<String, Object>> classList = new ArrayList<>();
        for (String className : classNames) {
            Map<String, Object> classInfo = new HashMap<>();
            classInfo.put("class_name", className);
            classInfo.put("package_name", getPackageName(className));
            classList.add(classInfo);
        }
        return classList;
    }

    /**
     * 从类名提取包名
     */
    private static String getPackageName(String className) {
        int lastDotIndex = className.lastIndexOf('.');
        return lastDotIndex > 0 ? className.substring(0, lastDotIndex) : "";
    }

    /**
     * 将Bundle-RequiredExecutionEnvironment转换为ProGuard目标版本
     *
     * @param executionEnvironment Bundle-RequiredExecutionEnvironment的值，如 "JavaSE-11"
     * @return ProGuard -target 参数值，如 "11"
     */
    private static String convertExecutionEnvironmentToTargetVersion(String executionEnvironment) {
        if (executionEnvironment == null || executionEnvironment.trim().isEmpty()) {
            return null;
        }

        // 移除空格并转换为小写进行匹配
        String env = executionEnvironment.trim();

        // 处理常见的执行环境格式
        if (env.startsWith("JavaSE-")) {
            // JavaSE-11, JavaSE-17, JavaSE-21 等
            String version = env.substring("JavaSE-".length());
            return version;
        } else if (env.startsWith("J2SE-")) {
            // J2SE-1.4, J2SE-1.5 等（旧格式）
            String version = env.substring("J2SE-".length());
            // 转换旧格式：1.4 -> 1.4, 1.5 -> 1.5, 1.6 -> 1.6, 1.7 -> 1.7, 1.8 -> 1.8
            return version;
        } else if (env.matches("^\\d+(\\.\\d+)*$")) {
            // 直接是版本号：11, 17, 21 等
            return env;
        } else if (env.equals("CDC-1.0/Foundation-1.0") || env.equals("CDC-1.1/Foundation-1.1")) {
            // CDC 环境，映射到 Java 1.4
            return "1.4";
        } else if (env.equals("OSGi/Minimum-1.0") || env.equals("OSGi/Minimum-1.1") || env.equals("OSGi/Minimum-1.2")) {
            // OSGi 最小环境，映射到 Java 1.4
            return "1.4";
        }

        // 如果无法识别，返回 null（将不会添加 -target 参数）
        return null;
    }
}
