package com.fasnote.alm.license.builder;

import com.fasnote.alm.license.builder.config.BuildConfigLoader;
import com.fasnote.alm.license.builder.core.BuildConfiguration;
import com.fasnote.alm.license.builder.model.BundleInfo;
import com.fasnote.alm.license.builder.util.BundleInfoResolver;
import com.fasnote.alm.license.builder.validation.*;
import com.fasnote.alm.license.builder.validation.validators.ClassIntegrityValidator;
import com.fasnote.alm.license.builder.validation.validators.DependencyResolutionValidator;
import com.fasnote.alm.license.builder.validation.validators.LicenseIntegrityValidator;
import com.fasnote.alm.license.builder.exception.BuildException;
import com.fasnote.alm.license.builder.tycho.TychoConfigGenerator;
import org.apache.commons.cli.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 许可证构建器命令行接口
 * 提供命令行工具用于：
 * 1. Tycho配置生成和Eclipse插件构建
 * 2. 许可证构建结果验证
 */
public class LicenseBuilderCLI {
    
    private static final Logger logger = LoggerFactory.getLogger(LicenseBuilderCLI.class);
    
    private static final String VERSION = "1.0.0";
    
    public static void main(String[] args) {
        try {
            LicenseBuilderCLI cli = new LicenseBuilderCLI();
            cli.run(args);
        } catch (Exception e) {
            logger.error("构建失败: {}", e.getMessage());
            if (logger.isDebugEnabled()) {
                logger.debug("详细错误信息", e);
            }
            System.exit(1);
        }
    }
    
    public void run(String[] args) throws BuildException {
        // 创建命令行选项
        Options options = createOptions();
        
        try {
            // 解析命令行参数
            CommandLineParser parser = new DefaultParser();
            CommandLine cmd = parser.parse(options, args);
            
            // 处理帮助和版本选项
            if (cmd.hasOption("h")) {
                printHelp(options);
                return;
            }
            
            if (cmd.hasOption("v")) {
                printVersion();
                return;
            }
            
            // 验证必需参数
            validateRequiredOptions(cmd);

            // 检查是否为Tycho模式
            if (cmd.hasOption("tycho-mode")) {
                executeTychoMode(cmd);
            } else {
                // 验证模式
                BuildConfiguration config = loadConfiguration(cmd);
                executeValidation(config);
                logger.info("许可证验证完成!");
            }
            
        } catch (ParseException e) {
            logger.error("命令行参数解析失败: {}", e.getMessage());
            printHelp(options);
            throw new BuildException("命令行参数错误", e);
        }
    }
    
    /**
     * 创建命令行选项
     */
    private Options createOptions() {
        Options options = new Options();
        
        // 基本选项
        options.addOption("h", "help", false, "显示帮助信息");
        options.addOption("v", "version", false, "显示版本信息");
        options.addOption("c", "config", true, "配置文件路径");
        
        // 输入输出选项
        options.addOption("i", "input", true, "输入JAR文件路径");
        options.addOption("o", "output", true, "输出目录路径");
        
        // Eclipse路径选项
        options.addOption("e", "eclipse", true, "Eclipse安装路径");
        
        // 验证选项
        options.addOption("m", "mode", true, "验证模式: validate（验证构建结果）, tycho-config（生成Tycho配置）, tycho-build（完整Tycho构建）");
        options.addOption("", "clean", false, "清理构建目录");

        // Tycho相关选项
        options.addOption("", "tycho-mode", false, "启用Tycho模式（Eclipse插件构建）");
        options.addOption("", "plugin-dir", true, "Eclipse插件项目目录路径");
        options.addOption("", "polarion-path", true, "Polarion安装路径");
        options.addOption("", "skip-maven", false, "跳过Maven构建（仅生成配置）");
        
        // 分离选项
        options.addOption("", "separation-strategy", true, "分离策略: AUTO, MANUAL, HYBRID");
        options.addOption("", "licensed-packages", true, "需要许可证的包列表（逗号分隔）");
        options.addOption("", "licensed-classes", true, "需要许可证的类列表（逗号分隔）");
        
        // 混淆选项
        options.addOption("", "obfuscation", true, "混淆级别: NONE, LIGHT, STANDARD, AGGRESSIVE");
        options.addOption("", "obfuscation-level", true, "混淆级别: NONE, LIGHT, STANDARD, AGGRESSIVE（同--obfuscation）");
        options.addOption("", "obfuscation-tool", true, "混淆工具: INTERNAL, PROGUARD, ALLATORI");
        
        // 许可证有效期选项
        options.addOption("", "license-validity", true, "许可证有效期: UNLIMITED, DAYS_30, DAYS_90, DAYS_180, DAYS_365, CUSTOM");
        options.addOption("", "custom-validity-days", true, "自定义有效期天数（当--license-validity=CUSTOM时使用）");

        // 数字签名选项
        options.addOption("", "enable-signing", false, "启用数字签名");
        options.addOption("", "auto-signing", false, "自动启用数字签名（使用默认密钥库）");
        options.addOption("", "keystore-path", true, "密钥库文件路径");
        options.addOption("", "signing-alias", true, "签名别名");
        options.addOption("", "store-password", true, "密钥库密码");
        options.addOption("", "key-password", true, "密钥密码");
        options.addOption("", "tsa-url", true, "时间戳服务器URL");

        // 日志选项
        options.addOption("", "log-level", true, "日志级别: TRACE, DEBUG, INFO, WARN, ERROR");
        options.addOption("", "verbose", false, "详细输出模式");
        options.addOption("", "quiet", false, "静默模式");
        
        return options;
    }
    
    /**
     * 打印帮助信息
     */
    private void printHelp(Options options) {
        HelpFormatter formatter = new HelpFormatter();
        formatter.setWidth(120);
        
        System.out.println("ALM License Builder v" + VERSION);
        System.out.println("许可证构建工具 - 负责代码混淆、实现类分离和许可证打包");
        System.out.println();
        
        formatter.printHelp("license-builder", options);
        
        System.out.println();
        System.out.println("使用示例:");
        System.out.println("  # 使用配置文件构建");
        System.out.println("  license-builder -c license-build.yml");
        System.out.println();
        System.out.println("  # 快速构建");
        System.out.println("  license-builder -i plugin.jar -o ./target -e /eclipse");
        System.out.println();
        System.out.println("  # 自定义分离策略");
        System.out.println("  license-builder -i plugin.jar --separation-strategy MANUAL \\");
        System.out.println("                  --licensed-packages com.example.impl,com.example.premium");
        System.out.println();
        System.out.println("  # 指定混淆级别");
        System.out.println("  license-builder -c config.yml --obfuscation AGGRESSIVE --obfuscation-tool PROGUARD");
        System.out.println();
        System.out.println("  # 设置许可证有效期");
        System.out.println("  license-builder -c config.yml --license-validity UNLIMITED      # 无限制");
        System.out.println("  license-builder -c config.yml --license-validity DAYS_365       # 1年");
        System.out.println("  license-builder -c config.yml --custom-validity-days 60         # 自定义60天");
        System.out.println();
        System.out.println("  # Tycho模式 - Eclipse插件构建");
        System.out.println("  license-builder --tycho-mode --plugin-dir /path/to/plugin --mode tycho-config  # 仅生成配置");
        System.out.println("  license-builder --tycho-mode --plugin-dir /path/to/plugin --mode tycho-build   # 完整构建");
        System.out.println("  license-builder --tycho-mode --plugin-dir /path/to/plugin --polarion-path /opt/polarion");
        System.out.println("  license-builder --tycho-mode --plugin-dir /path/to/plugin --skip-maven        # 跳过Maven构建");
        System.out.println();
        System.out.println("  # 数字签名 - 在Tycho构建中启用JAR签名");
        System.out.println("  license-builder --tycho-mode --plugin-dir /path/to/plugin --enable-signing \\");
        System.out.println("                  --keystore-path /path/to/keystore.jks --signing-alias myalias \\");
        System.out.println("                  --store-password storepass --key-password keypass");
        System.out.println();
        System.out.println("  # 自动签名 - 使用默认密钥库（自动创建）");
        System.out.println("  license-builder --tycho-mode --plugin-dir /path/to/plugin --auto-signing");
        System.out.println();
        System.out.println("许可证有效期选项:");
        System.out.println("  UNLIMITED   - 无限制");
        System.out.println("  DAYS_30     - 30天");
        System.out.println("  DAYS_90     - 90天（默认）");
        System.out.println("  DAYS_180    - 180天");
        System.out.println("  DAYS_365    - 365天");
        System.out.println("  CUSTOM      - 自定义天数（需配合--custom-validity-days使用）");
    }
    
    /**
     * 打印版本信息
     */
    private void printVersion() {
        System.out.println("ALM License Builder " + VERSION);
        System.out.println("Copyright (c) 2024 FasNote ALM Team");
    }
    
    /**
     * 验证必需选项
     */
    private void validateRequiredOptions(CommandLine cmd) throws BuildException {
        // Tycho模式的验证
        if (cmd.hasOption("tycho-mode")) {
            if (!cmd.hasOption("plugin-dir")) {
                throw new BuildException("Tycho模式下必须指定插件目录(--plugin-dir)");
            }
            return; // Tycho模式不需要其他验证
        }

        // 传统模式的验证
        if (!cmd.hasOption("c") && !cmd.hasOption("i")) {
            throw new BuildException("必须指定配置文件(-c)或输入文件(-i)");
        }
    }
    
    /**
     * 加载配置
     */
    private BuildConfiguration loadConfiguration(CommandLine cmd) throws BuildException {
        try {
            BuildConfigLoader loader = new BuildConfigLoader();
            BuildConfiguration config;
            
            if (cmd.hasOption("c")) {
                // 从配置文件加载
                Path configPath = Paths.get(cmd.getOptionValue("c"));
                config = loader.loadFromFile(configPath);
                logger.info("已加载配置文件: {}", configPath.toAbsolutePath());
            } else {
                // 从命令行参数创建配置
                config = loader.createFromCommandLine(cmd);
                logger.info("已从命令行参数创建配置");
            }
            
            // 应用命令行覆盖
            applyCommandLineOverrides(config, cmd);
            
            return config;
            
        } catch (Exception e) {
            throw new BuildException("配置加载失败", e);
        }
    }
    
    /**
     * 应用命令行覆盖选项
     */
    private void applyCommandLineOverrides(BuildConfiguration config, CommandLine cmd) {
        // 输出目录覆盖
        if (cmd.hasOption("o")) {
            config.getBuild().setOutputDir(cmd.getOptionValue("o"));
        }
        
        // Eclipse路径覆盖
        if (cmd.hasOption("e")) {
            config.getInput().setEclipsePath(cmd.getOptionValue("e"));
        }
        
        // 构建模式覆盖
        if (cmd.hasOption("m")) {
            String mode = cmd.getOptionValue("m").toLowerCase();
            System.setProperty("build.mode", mode);
            logger.info("构建模式设置为: {}", mode);
        }
        
        // 清理构建目录
        if (cmd.hasOption("clean")) {
            config.getBuild().setCleanBuild(true);
        }


        // 分离策略覆盖
        if (cmd.hasOption("separation-strategy")) {
            config.getSeparation().setStrategy(cmd.getOptionValue("separation-strategy"));
        }
        
        // 许可证包覆盖
        if (cmd.hasOption("licensed-packages")) {
            String[] packages = cmd.getOptionValue("licensed-packages").split(",");
            config.getSeparation().getLicensedPackages().clear();
            for (String pkg : packages) {
                config.getSeparation().getLicensedPackages().add(pkg.trim());
            }
        }
        
        // 许可证类覆盖
        if (cmd.hasOption("licensed-classes")) {
            String[] classes = cmd.getOptionValue("licensed-classes").split(",");
            config.getSeparation().getLicensedClasses().clear();
            for (String cls : classes) {
                config.getSeparation().getLicensedClasses().add(cls.trim());
            }
        }
        
        // 混淆级别覆盖
        if (cmd.hasOption("obfuscation")) {
            String level = cmd.getOptionValue("obfuscation");
            if (!"NONE".equals(level)) {
                config.getObfuscation().setEnabled(true);
                config.getObfuscation().setLevel(level);
            } else {
                config.getObfuscation().setEnabled(false);
            }
        }

        // 混淆级别覆盖（--obfuscation-level 同义词）
        if (cmd.hasOption("obfuscation-level")) {
            String level = cmd.getOptionValue("obfuscation-level");
            if (!"NONE".equals(level)) {
                config.getObfuscation().setEnabled(true);
                config.getObfuscation().setLevel(level);
            } else {
                config.getObfuscation().setEnabled(false);
            }
        }

        // 混淆工具覆盖
        if (cmd.hasOption("obfuscation-tool")) {
            config.getObfuscation().setTool(cmd.getOptionValue("obfuscation-tool"));
        }
        
        // 许可证有效期覆盖
        if (cmd.hasOption("license-validity")) {
            String validityStr = cmd.getOptionValue("license-validity").toUpperCase();
            try {
                BuildConfiguration.PackagingConfig.LicenseConfig.ValidityDuration validity = 
                    BuildConfiguration.PackagingConfig.LicenseConfig.ValidityDuration.valueOf(validityStr);
                config.getPackaging().getLicense().setValidityDuration(validity);
                
                if (validity == BuildConfiguration.PackagingConfig.LicenseConfig.ValidityDuration.UNLIMITED) {
                    logger.info("许可证有效期设置为: 无限制");
                } else {
                    logger.info("许可证有效期设置为: {}", validity.getDescription());
                }
                
            } catch (IllegalArgumentException e) {
                logger.warn("无效的许可证有效期选项: {}，使用默认值DAYS_90", validityStr);
            }
        }
        
        // 自定义有效期天数覆盖
        if (cmd.hasOption("custom-validity-days")) {
            try {
                int customDays = Integer.parseInt(cmd.getOptionValue("custom-validity-days"));
                if (customDays > 0) {
                    config.getPackaging().getLicense().setCustomValidityDays(customDays);
                    // 如果指定了自定义天数，自动设置为CUSTOM模式
                    config.getPackaging().getLicense().setValidityDuration(
                        BuildConfiguration.PackagingConfig.LicenseConfig.ValidityDuration.CUSTOM);
                    logger.info("许可证自定义有效期设置为: {} 天", customDays);
                } else {
                    logger.warn("自定义有效期天数必须大于0，使用默认值");
                }
            } catch (NumberFormatException e) {
                logger.warn("无效的自定义有效期天数: {}，使用默认值", cmd.getOptionValue("custom-validity-days"));
            }
        }
        
        // 日志级别覆盖
        if (cmd.hasOption("log-level")) {
            config.getLogging().setLevel(cmd.getOptionValue("log-level"));
        }
        
        // 详细模式
        if (cmd.hasOption("verbose")) {
            config.getLogging().setLevel("DEBUG");
            config.getLogging().setFormat("DETAILED");
        }
        
        // 静默模式
        if (cmd.hasOption("quiet")) {
            config.getLogging().setLevel("ERROR");
            config.getLogging().setConsole(false);
        }
    }
    
    /**
     * 执行验证
     */
    private void executeValidation(BuildConfiguration config) throws BuildException {
        logger.info("开始执行许可证验证...");

        try {
            // 获取Bundle信息
            BundleInfo bundleInfo = BundleInfoResolver.getBundleInfo();

            // 创建验证器选择器并注册验证器
            ValidatorSelector selector = createValidatorSelector();

            // 创建验证上下文
            ValidationContext context = createValidationContext(config, bundleInfo);

            // 执行验证
            executeValidators(selector, bundleInfo, context);

            logger.info("验证执行完成");

        } catch (Exception e) {
            throw new BuildException("验证失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建验证器选择器
     */
    private ValidatorSelector createValidatorSelector() {
        ValidatorSelector selector = new ValidatorSelector();

        // 注册所有验证器
        selector.registerValidator(new ClassIntegrityValidator());
        selector.registerValidator(new DependencyResolutionValidator());
        selector.registerValidator(new LicenseIntegrityValidator());

        return selector;
    }

    /**
     * 创建验证上下文
     */
    private ValidationContext createValidationContext(BuildConfiguration config, BundleInfo bundleInfo) {
        // CLI 工具默认使用 POST_OBFUSCATION 阶段
        BuildPhase phase = BuildPhase.POST_OBFUSCATION;

        // 检测插件类型
        PluginType pluginType = PluginType.detectFromBundleInfo(bundleInfo);

        return ValidationContext.builder()
                .currentPhase(phase)
                .pluginType(pluginType)
                .buildMode("validate")
                .configuration(config)
                .build();
    }

    /**
     * 执行验证器
     */
    private void executeValidators(ValidatorSelector selector, BundleInfo bundleInfo,
                                 ValidationContext context) throws BuildException {

        List<PhaseAwareValidator> validators = selector.selectValidators(context);

        if (validators.isEmpty()) {
            logger.warn("没有找到适用的验证器");
            return;
        }

        logger.info("执行 {} 个验证器...", validators.size());

        boolean hasFailure = false;
        for (PhaseAwareValidator validator : validators) {
            try {
                logger.info("执行验证器: {}", validator.getName());

                ValidationResult result = validator.validate(bundleInfo, context);

                // 输出验证结果
                logValidationResult(result);

                if (!result.isSuccess() && validator.isCritical()) {
                    hasFailure = true;
                }

            } catch (Exception e) {
                logger.error("验证器 {} 执行失败", validator.getName(), e);
                if (validator.isCritical()) {
                    hasFailure = true;
                }
            }
        }

        if (hasFailure) {
            throw new BuildException("验证失败，存在关键错误");
        }
    }

    /**
     * 输出验证结果
     */
    private void logValidationResult(ValidationResult result) {
        if (result.isSuccess()) {
            logger.info("[{}] 验证通过", result.getValidatorName());
            for (String message : result.getMessages()) {
                logger.info("  {}", message);
            }
        } else {
            logger.error("[{}] 验证失败", result.getValidatorName());
            for (String message : result.getMessages()) {
                logger.error("  {}", message);
            }
        }

        // 输出警告信息
        for (String warning : result.getWarnings()) {
            logger.warn("  {}", warning);
        }
    }

    /**
     * 执行Tycho模式构建
     */
    private void executeTychoMode(CommandLine cmd) throws BuildException {
        try {
            logger.info("开始执行Tycho模式构建...");

            // 获取参数
            Path pluginDir = Paths.get(cmd.getOptionValue("plugin-dir"));
            Path polarionPath = getPolarionPath(cmd);
            String mode = cmd.getOptionValue("m", "tycho-build");
            boolean skipMaven = cmd.hasOption("skip-maven");

            // 验证插件目录
            validatePluginDirectory(pluginDir);

            // 设置系统属性（用于TychoConfigGenerator）
            setTychoSystemProperties(cmd);

            // 创建配置生成器
            TychoConfigGenerator configGenerator = new TychoConfigGenerator(pluginDir, polarionPath);

            try {
                // 根据模式执行不同操作
                switch (mode.toLowerCase()) {
                    case "tycho-config":
                        // 仅生成配置
                        configGenerator.generateTychoConfiguration();
                        logger.info("Tycho配置生成完成!");
                        break;

                    case "tycho-build":
                        // 完整构建流程
                        configGenerator.generateTychoConfiguration();

                        if (!skipMaven) {
                            executeMavenBuild(pluginDir, cmd);
                        }

                        logger.info("Tycho构建完成!");
                        break;

                    default:
                        throw new BuildException("不支持的Tycho模式: " + mode);
                }

            } finally {
                // 清理临时文件
                configGenerator.cleanup();
            }

        } catch (Exception e) {
            if (e instanceof BuildException) {
                throw (BuildException) e;
            }
            throw new BuildException("Tycho模式执行失败", e);
        }
    }

    /**
     * 获取Polarion路径
     */
    private Path getPolarionPath(CommandLine cmd) {
        if (cmd.hasOption("polarion-path")) {
            return Paths.get(cmd.getOptionValue("polarion-path"));
        }

        // 尝试从环境变量获取
        String polarionHome = System.getenv("POLARION_HOME");
        if (polarionHome != null) {
            Path polarionPath = Paths.get(polarionHome, "polarion");
            if (Files.exists(polarionPath.resolve("plugins"))) {
                logger.info("从环境变量检测到Polarion路径: {}", polarionPath);
                return polarionPath;
            }
        }

        // 使用默认路径
        logger.info("使用默认Polarion路径: /opt/polarion/polarion");
        return Paths.get("/opt/polarion/polarion");
    }

    /**
     * 验证插件目录
     */
    private void validatePluginDirectory(Path pluginDir) throws BuildException {
        if (!Files.exists(pluginDir)) {
            throw new BuildException("插件目录不存在: " + pluginDir);
        }

        Path manifestFile = pluginDir.resolve("META-INF/MANIFEST.MF");
        if (!Files.exists(manifestFile)) {
            throw new BuildException("MANIFEST.MF文件不存在: " + manifestFile);
        }

        logger.info("插件目录验证通过: {}", pluginDir);
    }

    /**
     * 设置Tycho相关的系统属性
     */
    private void setTychoSystemProperties(CommandLine cmd) {
        // 混淆相关属性
        if (cmd.hasOption("obfuscation")) {
            String level = cmd.getOptionValue("obfuscation");
            if ("NONE".equals(level)) {
                System.setProperty("skip.obfuscation", "true");
            } else {
                System.setProperty("skip.obfuscation", "false");
                System.setProperty("obfuscation.level", level);
            }
        }

        // 处理 --obfuscation-level（同义词）
        if (cmd.hasOption("obfuscation-level")) {
            String level = cmd.getOptionValue("obfuscation-level");
            if ("NONE".equals(level)) {
                System.setProperty("skip.obfuscation", "true");
            } else {
                System.setProperty("skip.obfuscation", "false");
                System.setProperty("obfuscation.level", level);
            }
        }

        if (cmd.hasOption("obfuscation-tool")) {
            System.setProperty("obfuscation.tool", cmd.getOptionValue("obfuscation-tool"));
        }

        if (cmd.hasOption("license-validity")) {
            System.setProperty("license.validity", cmd.getOptionValue("license-validity"));
        }

        // 数字签名相关属性
        if (cmd.hasOption("enable-signing") || cmd.hasOption("auto-signing")) {
            System.setProperty("signing.enabled", "true");

            // 如果是自动签名，设置默认值
            if (cmd.hasOption("auto-signing")) {
                setupAutoSigning(cmd);
            }
        }

        if (cmd.hasOption("keystore-path")) {
            System.setProperty("signing.keystore.path", cmd.getOptionValue("keystore-path"));
        }

        if (cmd.hasOption("signing-alias")) {
            System.setProperty("signing.alias", cmd.getOptionValue("signing-alias"));
        }

        if (cmd.hasOption("store-password")) {
            System.setProperty("signing.storepass", cmd.getOptionValue("store-password"));
        }

        if (cmd.hasOption("key-password")) {
            System.setProperty("signing.keypass", cmd.getOptionValue("key-password"));
        }

        if (cmd.hasOption("tsa-url")) {
            System.setProperty("signing.tsa.url", cmd.getOptionValue("tsa-url"));
        }

        logger.debug("Tycho系统属性设置完成");
    }

    /**
     * 设置自动签名的默认配置
     */
    private void setupAutoSigning(CommandLine cmd) {
        try {
            // 默认密钥库路径
            String defaultKeystorePath = System.getProperty("user.home") + "/.license-builder/default-keystore.jks";
            String defaultAlias = "license-builder";
            String defaultPassword = "license123";

            // 如果用户没有指定，使用默认值
            if (!cmd.hasOption("keystore-path")) {
                System.setProperty("signing.keystore.path", defaultKeystorePath);

                // 检查默认密钥库是否存在，不存在则创建
                Path keystorePath = Paths.get(defaultKeystorePath);
                if (!Files.exists(keystorePath)) {
                    createDefaultKeystore(keystorePath, defaultAlias, defaultPassword);
                }
            }

            if (!cmd.hasOption("signing-alias")) {
                System.setProperty("signing.alias", defaultAlias);
            }

            if (!cmd.hasOption("store-password")) {
                System.setProperty("signing.storepass", defaultPassword);
            }

            if (!cmd.hasOption("key-password")) {
                System.setProperty("signing.keypass", defaultPassword);
            }

            logger.info("自动签名配置完成 - 密钥库: {}, 别名: {}",
                       System.getProperty("signing.keystore.path"),
                       System.getProperty("signing.alias"));

        } catch (Exception e) {
            logger.warn("自动签名配置失败: {}", e.getMessage());
        }
    }

    /**
     * 创建默认密钥库
     */
    private void createDefaultKeystore(Path keystorePath, String alias, String password) throws Exception {
        logger.info("创建默认密钥库: {}", keystorePath);

        // 确保目录存在
        Files.createDirectories(keystorePath.getParent());

        // 构建keytool命令
        List<String> command = Arrays.asList(
            "keytool", "-genkey",
            "-alias", alias,
            "-keyalg", "RSA",
            "-keysize", "2048",
            "-keystore", keystorePath.toString(),
            "-validity", "3650", // 10年有效期
            "-storepass", password,
            "-keypass", password,
            "-dname", "CN=License Builder, OU=Development, O=License Builder, L=Unknown, ST=Unknown, C=US"
        );

        ProcessBuilder pb = new ProcessBuilder(command);
        Process process = pb.start();

        int exitCode = process.waitFor();
        if (exitCode == 0) {
            logger.info("默认密钥库创建成功");
        } else {
            throw new Exception("密钥库创建失败，退出码: " + exitCode);
        }
    }



    /**
     * 执行Maven构建
     */
    private void executeMavenBuild(Path pluginDir, CommandLine cmd) throws BuildException {
        try {
            logger.info("执行Maven构建...");

            ProcessBuilder pb = new ProcessBuilder();
            pb.directory(pluginDir.toFile());

            // 构建Maven命令
            pb.command().add("mvn");
            pb.command().add("clean");

            // 根据构建模式选择不同的目标
            String buildMode = System.getProperty("build.mode", "obfuscate-only");
            if ("full".equals(buildMode)) {
                pb.command().add("install");  // full模式执行到install阶段以触发许可证处理
            } else {
                pb.command().add("package");  // obfuscate-only模式只执行到package阶段
            }

            // 添加详细输出选项
            if (cmd.hasOption("verbose")) {
                pb.command().add("-X");
            } else {
                pb.command().add("-q");
            }

            logger.info("执行命令: {}", String.join(" ", pb.command()));

            // 执行构建
            Process process = pb.start();

            // 处理输出
            if (cmd.hasOption("verbose")) {
                process.getInputStream().transferTo(System.out);
                process.getErrorStream().transferTo(System.err);
            }

            boolean finished = process.waitFor(600, TimeUnit.SECONDS);
            int exitCode = finished ? process.exitValue() : -1;

            if (exitCode != 0) {
                throw new BuildException("Maven构建失败，退出码: " + exitCode);
            }

            logger.info("Maven构建完成");

        } catch (IOException | InterruptedException e) {
            throw new BuildException("执行Maven构建失败", e);
        }
    }
}