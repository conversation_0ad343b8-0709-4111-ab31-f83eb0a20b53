package com.fasnote.alm.injection.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 服务注解
 * 标记一个类为服务实现类，支持许可证感知的优先级管理
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface Service {

    /**
     * 服务名称
     */
    String value() default "";

    /**
     * 服务接口类型（如果不指定，将自动推断）
     */
    Class<?>[] interfaces() default {};

    /**
     * 服务优先级（数字越小优先级越高）
     * 用于支持许可证实现和回退实现的优先级管理
     */
    int priority() default 100;

    /**
     * 生命周期
     */
    Scope scope() default Scope.SINGLETON;

    /**
     * 生命周期枚举
     */
    enum Scope {
        SINGLETON,  // 单例
        PROTOTYPE   // 原型（每次创建新实例）
    }
}