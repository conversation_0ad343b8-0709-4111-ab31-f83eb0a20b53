package com.fasnote.alm.license.builder.exception;

import java.util.HashMap;
import java.util.Map;

/**
 * 构建异常基类
 * 支持上下文信息的异常类
 */
public class BuildException extends Exception {
    
    private final String operation;
    private final Map<String, Object> context = new HashMap<>();
    
    public BuildException(String message) {
        super(message);
        this.operation = null;
    }
    
    public BuildException(String message, Throwable cause) {
        super(message, cause);
        this.operation = null;
    }
    
    public BuildException(String operation, String message) {
        super(message);
        this.operation = operation;
    }
    
    public BuildException(String operation, String message, Throwable cause) {
        super(message, cause);
        this.operation = operation;
    }
    
    /**
     * 添加上下文信息
     * @param key 键
     * @param value 值
     * @return 当前异常实例（支持链式调用）
     */
    public BuildException addContext(String key, Object value) {
        context.put(key, value);
        return this;
    }
    
    /**
     * 获取操作名称
     * @return 操作名称
     */
    public String getOperation() {
        return operation;
    }
    
    /**
     * 获取上下文信息
     * @return 上下文Map
     */
    public Map<String, Object> getContext() {
        return new HashMap<>(context);
    }
    
    /**
     * 获取指定键的上下文值
     * @param key 键
     * @return 值
     */
    public Object getContextValue(String key) {
        return context.get(key);
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName()).append(": ");
        
        if (operation != null) {
            sb.append("[").append(operation).append("] ");
        }
        
        sb.append(getMessage());
        
        if (!context.isEmpty()) {
            sb.append(" (Context: ").append(context).append(")");
        }
        
        return sb.toString();
    }
}