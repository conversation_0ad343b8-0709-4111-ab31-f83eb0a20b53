package com.fasnote.alm.plugin.manage.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用户数量限制检查注解
 *
 * 标记需要进行用户数量限制检查的方法。 被此注解标记的方法在调用前会验证当前用户数量是否超出许可证限制。
 */
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
public @interface UserLimitCheck {

	/**
	 * 用户数量获取方式枚举
	 */
	enum UserCountSource {
		/**
		 * 自动检测（从Polarion系统获取）
		 */
		AUTO_DETECT,

		/**
		 * 从方法参数获取（方法必须有int类型的参数）
		 */
		METHOD_PARAMETER,

		/**
		 * 使用自定义提供者
		 */
		CUSTOM,

		/**
		 * 使用固定值（主要用于测试）
		 */
		FIXED_VALUE
	}

	/**
	 * 是否启用验证 默认为true，设置为false可以临时禁用验证
	 */
	boolean enabled() default true;

	/**
	 * 自定义错误消息 如果为空，则使用默认的错误消息
	 */
	String message() default "";

	/**
	 * 验证失败时的处理策略
	 */
	LicenseRequired.FailureStrategy onFailure() default LicenseRequired.FailureStrategy.THROW_EXCEPTION;

	/**
	 * 是否需要同时进行基本许可证验证 默认为true，会先进行基本的许可证验证，然后再验证用户数量限制
	 */
	boolean requireBasicValidation() default true;

	/**
	 * 自定义用户数量提供者类（当userCountSource为CUSTOM时使用）
	 */
	Class<?> userCountProvider() default Void.class;

	/**
	 * 当前用户数量的获取方式
	 */
	UserCountSource userCountSource() default UserCountSource.AUTO_DETECT;
}
