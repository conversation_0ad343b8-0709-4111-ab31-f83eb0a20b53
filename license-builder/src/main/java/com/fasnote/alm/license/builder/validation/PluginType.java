package com.fasnote.alm.license.builder.validation;

/**
 * 插件类型枚举
 * 定义不同类型的插件，用于验证器的类型感知选择
 */
public enum PluginType {
    
    /**
     * 业务插件
     * 包含业务逻辑的插件，需要许可证保护
     */
    BUSINESS("business", "业务插件"),
    
    /**
     * 管理插件
     * 管理类插件，通常不需要许可证保护
     */
    MANAGE("manage", "管理插件"),
    
    /**
     * 所有插件类型
     * 适用于所有插件类型的通用验证器
     */
    ALL("all", "所有插件类型");
    
    private final String code;
    private final String description;
    
    PluginType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取插件类型
     */
    public static PluginType fromCode(String code) {
        for (PluginType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的插件类型代码: " + code);
    }
    
    /**
     * 根据Bundle信息自动检测插件类型
     */
    public static PluginType detectFromBundleInfo(com.fasnote.alm.license.builder.model.BundleInfo bundleInfo) {
        if (bundleInfo == null) {
            return ALL;
        }
        
        String symbolicName = bundleInfo.getSymbolicName();
        if (symbolicName == null) {
            return ALL;
        }

        // 根据Bundle SymbolicName模式判断插件类型
        if (symbolicName.contains(".manage.") || symbolicName.endsWith(".manage")) {
            return MANAGE;
        } else {
            return BUSINESS;
        }
    }
    
    @Override
    public String toString() {
        return description + "(" + code + ")";
    }
}
