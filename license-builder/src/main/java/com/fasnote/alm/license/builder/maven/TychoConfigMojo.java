package com.fasnote.alm.license.builder.maven;

import com.fasnote.alm.license.builder.exception.BuildException;
import com.fasnote.alm.license.builder.tycho.TychoConfigGenerator;
import org.apache.maven.plugin.AbstractMojo;
import org.apache.maven.plugin.MojoExecutionException;
import org.apache.maven.plugin.MojoFailureException;
import org.apache.maven.plugins.annotations.*;
import org.apache.maven.project.MavenProject;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Maven插件：Tycho配置生成器
 * 
 * 专门用于生成Tycho构建所需的配置文件，包括：
 * - pom.xml（包含混淆配置）
 * - build.properties
 * - target platform配置
 */
@Mojo(
    name = "tycho-config",
    defaultPhase = LifecyclePhase.INITIALIZE,
    requiresDependencyResolution = ResolutionScope.NONE
)
public class TychoConfigMojo extends AbstractMojo {

    /**
     * Maven项目对象
     */
    @Parameter(defaultValue = "${project}", readonly = true, required = true)
    private MavenProject project;

    /**
     * 插件源目录
     */
    @Parameter(property = "tycho.plugin.dir", defaultValue = "${basedir}")
    private File pluginDir;

    /**
     * 混淆工具选择
     */
    @Parameter(property = "tycho.obfuscation.tool", defaultValue = "YGUARD")
    private String obfuscationTool;

    /**
     * 混淆级别
     */
    @Parameter(property = "tycho.obfuscation.level", defaultValue = "STANDARD")
    private String obfuscationLevel;

    /**
     * 是否启用签名
     */
    @Parameter(property = "tycho.signing.enabled", defaultValue = "false")
    private boolean signingEnabled;

    /**
     * 签名密钥库路径
     */
    @Parameter(property = "tycho.signing.keystore")
    private String keystorePath;

    /**
     * 签名别名
     */
    @Parameter(property = "tycho.signing.alias")
    private String signingAlias;

    /**
     * Polarion安装路径
     */
    @Parameter(property = "tycho.polarion.home")
    private String polarionHome;

    /**
     * 跳过Tycho配置生成
     */
    @Parameter(property = "tycho.config.skip", defaultValue = "false")
    private boolean skip;

    @Override
    public void execute() throws MojoExecutionException, MojoFailureException {
        if (skip) {
            getLog().info("Tycho配置生成已跳过");
            return;
        }

        try {
            getLog().info("开始生成Tycho配置...");

            // 创建配置生成器
            TychoConfigGenerator generator = createConfigGenerator();

            // 生成配置文件
            generator.generateTychoConfiguration();

            getLog().info("Tycho配置生成完成");

        } catch (BuildException e) {
            throw new MojoExecutionException("Tycho配置生成失败: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new MojoExecutionException("Tycho配置生成过程中发生未知错误", e);
        }
    }

    /**
     * 创建配置生成器
     */
    private TychoConfigGenerator createConfigGenerator() {
        // 设置系统属性以配置生成器
        if (obfuscationTool != null) {
            System.setProperty("obfuscation.tool", obfuscationTool.toUpperCase());
        }
        if (obfuscationLevel != null) {
            System.setProperty("obfuscation.level", obfuscationLevel.toUpperCase());
        }
        if (signingEnabled) {
            System.setProperty("signing.enabled", "true");
            if (keystorePath != null) {
                System.setProperty("signing.keystore", keystorePath);
            }
            if (signingAlias != null) {
                System.setProperty("signing.alias", signingAlias);
            }
        }
        if (polarionHome != null) {
            System.setProperty("polarion.home", polarionHome);
        }

        // 需要提供pluginDir和polarionPath参数
        Path polarionPath = polarionHome != null ? Paths.get(polarionHome) : null;
        return new TychoConfigGenerator(pluginDir.toPath(), polarionPath);
    }
}
