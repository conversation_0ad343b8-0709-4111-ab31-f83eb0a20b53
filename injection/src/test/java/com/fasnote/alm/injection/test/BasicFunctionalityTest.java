package com.fasnote.alm.injection.test;

import javax.inject.Inject;

import com.fasnote.alm.injection.api.IDependencyInjector;
import com.fasnote.alm.injection.impl.DependencyInjector;
import com.fasnote.alm.injection.annotations.Service;

/**
 * 基本的功能测试
 */
public class BasicFunctionalityTest {
    
    // 测试接口
    public interface TestService {
        String doSomething();
    }
    
    // 测试实现
    @Service
    public static class TestServiceImpl implements TestService {
        @Override
        public String doSomething() {
            return "Hello from TestService";
        }
    }
    
    // 测试客户端
    public static class TestClient {
        @Inject
        private TestService testService;
        
        public String useService() {
            return testService != null ? testService.doSomething() : "Service not injected";
        }
        
        public void setTestService(TestService service) {
            this.testService = service;
        }
    }
    
    public static void main(String[] args) {
        System.out.println("测试依赖注入框架...");
        
        try {
            // 1. 创建依赖注入器
            IDependencyInjector injector = new DependencyInjector();
            
            // 2. 注册服务实现
            injector.registerImplementation(TestService.class, TestServiceImpl.class);
            
            // 3. 测试获取服务
            TestService service = injector.getService(TestService.class);
            if (service != null) {
                System.out.println("✓ 服务获取成功: " + service.doSomething());
            } else {
                System.out.println("✗ 服务获取失败");
            }
            
            // 4. 测试创建并注入依赖
            TestClient client = injector.createInstance(TestClient.class);
            if (client != null) {
                String result = client.useService();
                System.out.println("✓ 依赖注入成功: " + result);
            } else {
                System.out.println("✗ 创建客户端失败");
            }
            
            // 5. 打印统计信息
            System.out.println("统计信息: " + injector.getStatistics());
            
            System.out.println("测试完成！");
            
        } catch (Exception e) {
            System.out.println("✗ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}