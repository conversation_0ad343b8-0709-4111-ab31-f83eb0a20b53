# Builder插件集成方案

## 1. 集成架构概述

builder插件需要与新的许可证DI框架集成，主要包括以下几个方面：

### 1.1 字节码转换更新
- **当前状态**: `ServiceInjectionTransformer`将`Services.create()`和`DI.get()`转换为`LicenseManagementFramework.getService()`
- **集成方案**: 保持现有转换逻辑，但确保转换后的代码能正确使用新的DI框架

### 1.2 许可证文件生成
- **功能**: 在构建过程中生成包含加密实现类和服务映射的lic文件
- **集成点**: builder插件的`LicensePackager`组件

### 1.3 依赖关系处理
- **功能**: 确保builder能正确处理injection插件的依赖
- **集成点**: 依赖解析和打包过程

## 2. 详细集成方案

### 2.1 更新ServiceInjectionTransformer

当前的转换器将DI调用转换为`LicenseManagementFramework`，这是正确的，因为：
1. LicenseManagementFramework内部使用新的DI框架
2. 通过统一入口可以进行许可证验证和权限控制
3. 保持向后兼容性

### 2.2 许可证文件格式增强

lic文件需要包含以下新格式：

```json
{
  "pluginId": "com.example.business.plugin",
  "productName": "Business Analytics Plugin",
  "version": "1.0.0",
  "licenseType": "Commercial",
  "issuer": "FasNote Technologies",
  
  "issueDate": "2024-01-01 00:00:00",
  "effectiveDate": "2024-01-01 00:00:00", 
  "expiryDate": "2025-01-01 00:00:00",
  
  "machineCode": "MACHINE_HASH_CODE",
  "signature": "LICENSE_SIGNATURE",
  "contentHash": "CONTENT_HASH",
  
  "licensedTo": "Example Corporation",
  "organization": "Example Corp",
  "maxUsers": 100,
  
  "features": {
    "advanced_analytics": true,
    "data_export": true,
    "real_time_processing": true,
    "custom_reports": true
  },
  
  "limitations": {
    "max_data_size": 1000000,
    "concurrent_users": 50
  },
  
  "serviceMappings": {
    "com.example.business.IDataAnalyzer": "com.example.business.impl.PremiumDataAnalyzer",
    "com.example.business.IReportGenerator": "com.example.business.impl.AdvancedReportGenerator",
    "com.example.business.IDataExporter": "com.example.business.impl.MultiFormatExporter"
  },
  
  "encryptedClassData": "BASE64_ENCODED_ENCRYPTED_CLASS_BYTES",
  
  "customProperties": {
    "deployment_environment": "production",
    "support_level": "premium"
  }
}
```

### 2.3 构建流程集成

builder插件的构建流程需要以下更新：

1. **类分离阶段**: 识别需要加密的实现类
2. **加密阶段**: 将实现类加密并嵌入lic文件
3. **服务映射生成**: 自动生成接口到实现类的映射
4. **字节码转换**: 确保业务代码使用正确的DI调用
5. **打包阶段**: 生成最终的插件包和lic文件

## 3. 使用方式

### 3.1 业务插件开发者使用

```java
// 业务插件代码 (BusinessPlugin.java)
public class BusinessPlugin {
    
    public void executeAnalysis() {
        // 通过DI框架获取服务（将被转换为LicenseManagementFramework调用）
        IDataAnalyzer analyzer = DI.get(IDataAnalyzer.class);
        
        if (analyzer != null) {
            // 使用许可证中的高级实现
            AnalysisResult result = analyzer.analyze(data);
            processResult(result);
        } else {
            // 降级到基础功能
            executeBasicAnalysis();
        }
    }
}
```

### 3.2 许可证实现类开发

```java
// 高级数据分析器实现 (将被加密并放入lic文件)
public class PremiumDataAnalyzer implements IDataAnalyzer, LicenseAware {
    
    private PluginLicense license;
    
    @Override
    public AnalysisResult analyze(String data) {
        if (license != null && license.hasFeature("advanced_algorithms")) {
            return performAdvancedAnalysis(data);
        } else {
            return performBasicAnalysis(data);
        }
    }
    
    @Override
    public void setLicenseInfo(PluginLicense license) {
        this.license = license;
    }
    
    @Override
    public PluginLicense getLicenseInfo() {
        return license;
    }
}
```

### 3.3 构建配置

```xml
<!-- pom.xml中的builder插件配置 -->
<plugin>
    <groupId>com.fasnote.alm</groupId>
    <artifactId>license-builder-maven-plugin</artifactId>
    <version>1.0.0</version>
    <configuration>
        <!-- 许可证配置 -->
        <licenseConfig>
            <pluginId>com.example.business.plugin</pluginId>
            <productName>Business Analytics Plugin</productName>
            <version>1.0.0</version>
        </licenseConfig>
        
        <!-- 类分离配置 -->
        <separationConfig>
            <encryptImplementations>true</encryptImplementations>
            <implementationPackages>
                <package>com.example.business.impl</package>
            </implementationPackages>
        </separationConfig>
        
        <!-- 服务映射配置 -->
        <serviceMappings>
            <mapping>
                <interface>com.example.business.IDataAnalyzer</interface>
                <implementation>com.example.business.impl.PremiumDataAnalyzer</implementation>
            </mapping>
            <mapping>
                <interface>com.example.business.IReportGenerator</interface>
                <implementation>com.example.business.impl.AdvancedReportGenerator</implementation>
            </mapping>
        </serviceMappings>
        
        <!-- 字节码转换配置 -->
        <transformationConfig>
            <enableDITransformation>true</enableDITransformation>
            <targetFramework>LicenseManagementFramework</targetFramework>
        </transformationConfig>
    </configuration>
    <executions>
        <execution>
            <goals>
                <goal>build-licensed-plugin</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

## 4. 运行时集成流程

### 4.1 插件加载时序

1. **OSGi容器启动** → 加载manage插件
2. **manage插件激活** → 初始化LicenseManager和DI框架
3. **LicenseModule注册** → 安装到DI框架中
4. **业务插件加载** → OSGi加载业务插件jar
5. **许可证注册** → 调用`LicenseManager.registerPluginLicense()`
6. **服务解密注册** → LicenseManager解密lic中的实现类并注册到DI框架
7. **业务代码执行** → 通过DI.get()获取许可证中的实现

### 4.2 服务获取流程

```
业务代码: DI.get(IService.class)
    ↓
字节码转换: LicenseManagementFramework.getInstance().getService(IService.class)
    ↓  
LicenseManagementFramework: 委托给全局DI框架
    ↓
DI框架: 触发LicenseModule拦截器
    ↓
LicenseModule: 从许可证中查找并创建实现类实例
    ↓
返回许可证中的服务实例给业务代码
```

## 5. 开发和部署流程

### 5.1 开发阶段
1. 开发者编写业务插件代码，使用`DI.get()`获取服务
2. 实现许可证版本的服务类，实现`LicenseAware`接口
3. 配置builder插件的服务映射和加密设置

### 5.2 构建阶段  
1. builder插件分析业务代码，识别DI调用
2. 将DI调用转换为LicenseManagementFramework调用
3. 加密实现类并生成服务映射
4. 打包生成业务插件jar和lic许可证文件

### 5.3 部署阶段
1. 部署injection和manage插件到目标环境
2. 安装业务插件jar到plugins目录
3. 注册lic许可证文件
4. 启动应用，验证服务可用性

## 6. 优势和特性

### 6.1 透明集成
- 业务代码无需修改，继续使用`DI.get()`
- 通过字节码转换实现透明的许可证集成
- 保持与现有代码的100%兼容性

### 6.2 安全性
- 实现类完全加密，无法直接反编译
- 许可证验证后才能获取服务实现
- 支持功能级别的权限控制

### 6.3 灵活性
- 支持多种许可证策略（时间限制、功能限制、用户数限制）
- 可以为不同客户提供不同级别的功能
- 支持运行时许可证更新

### 6.4 可维护性
- 清晰的分层架构，职责分离
- 标准化的许可证文件格式
- 完整的日志和错误处理机制