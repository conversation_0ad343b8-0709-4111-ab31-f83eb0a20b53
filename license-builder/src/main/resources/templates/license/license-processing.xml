            <!-- 许可证处理插件 -->
            <plugin>
                <groupId>com.fasnote.alm</groupId>
                <artifactId>license-builder</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <executions>
                    <!-- 混淆后验证 - 在 process-classes 阶段自动执行 -->
                    <execution>
                        <id>post-obfuscation-validate</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>post-obfuscation-validate</goal>
                        </goals>
                        <configuration>
                            <inputJar>${project.build.directory}/${project.build.finalName}-obfuscated.jar</inputJar>
                            <pluginDir>${project.basedir}</pluginDir>
                            <verbose>false</verbose>
                            <failOnError>true</failOnError>
                        </configuration>
                    </execution>
                    <!-- 执行许可证构建 - 在 install 阶段执行 -->
                    <execution>
                        <id>build-license</id>
                        <phase>install</phase>
                        <goals>
                            <goal>license-build</goal>
                        </goals>
                        <configuration>
                            <inputJar>${project.build.directory}/${project.build.finalName}-obfuscated.jar</inputJar>
                            <outputDir>${project.build.directory}/license-output</outputDir>
                            <pluginDir>${project.basedir}</pluginDir>
                            <separationEnabled>true</separationEnabled>
                            <encryptionEnabled>true</encryptionEnabled>
                        </configuration>
                    </execution>
                    <!-- 许可证构建后验证 - 在 verify 阶段自动执行 -->
                    <execution>
                        <id>post-license-build-validate</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>post-license-build-validate</goal>
                        </goals>
                        <configuration>
                            <inputJar>${project.build.directory}/${project.build.finalName}-obfuscated.jar</inputJar>
                            <outputDir>${project.build.directory}/license-output</outputDir>
                            <pluginDir>${project.basedir}</pluginDir>
                            <verbose>false</verbose>
                            <failOnError>true</failOnError>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
