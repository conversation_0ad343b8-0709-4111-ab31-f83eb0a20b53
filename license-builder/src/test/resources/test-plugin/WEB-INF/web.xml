<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee
         http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         version="3.0">

    <servlet>
        <servlet-name>FeishuAuthServlet</servlet-name>
        <servlet-class>com.fasnote.alm.auth.feishu.servlet.FeishuAuthServlet</servlet-class>
    </servlet>
    
    <servlet-mapping>
        <servlet-name>FeishuAuthServlet</servlet-name>
        <url-pattern>/feishu/auth/*</url-pattern>
    </servlet-mapping>
    
    <filter>
        <filter-name>FeishuSecurityFilter</filter-name>
        <filter-class>com.fasnote.alm.auth.feishu.filter.FeishuSecurityFilter</filter-class>
    </filter>
    
    <filter-mapping>
        <filter-name>FeishuSecurityFilter</filter-name>
        <url-pattern>/feishu/*</url-pattern>
    </filter-mapping>
    
    <listener>
        <listener-class>com.fasnote.alm.auth.feishu.listener.FeishuContextListener</listener-class>
    </listener>
    
    <context-param>
        <param-name>configurationClass</param-name>
        <param-value>com.fasnote.alm.auth.feishu.config.FeishuConfiguration</param-value>
    </context-param>

</web-app>