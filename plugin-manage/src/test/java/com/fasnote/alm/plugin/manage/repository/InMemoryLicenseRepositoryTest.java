package com.fasnote.alm.plugin.manage.repository;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.repository.impl.InMemoryLicenseRepository;
import com.fasnote.alm.plugin.manage.test.LicenseTestBase;

/**
 * 内存许可证存储库测试类
 */
@DisplayName("内存许可证存储库测试")
class InMemoryLicenseRepositoryTest extends LicenseTestBase {

	private InMemoryLicenseRepository repository;

	@BeforeEach
	void setUp() {
		repository = new InMemoryLicenseRepository();
	}

	@Test
	@DisplayName("检查许可证存在性应该正确")
	void shouldCheckLicenseExistence() {
		// Given
		PluginLicense license = createTestLicense();

		// When & Then
		assertFalse(repository.existsByPluginId(TEST_PLUGIN_ID));

		repository.save(license);
		assertTrue(repository.existsByPluginId(TEST_PLUGIN_ID));

		repository.deleteByPluginId(TEST_PLUGIN_ID);
		assertFalse(repository.existsByPluginId(TEST_PLUGIN_ID));
	}

	@Test
	@DisplayName("清空存储库应该成功")
	void shouldClearRepository() {
		// Given
		PluginLicense license1 = createTestLicense("plugin1", LocalDateTime.now().plusDays(30));
		PluginLicense license2 = createTestLicense("plugin2", LocalDateTime.now().plusDays(60));
		repository.save(license1);
		repository.save(license2);
		assertEquals(2, repository.size());

		// When
		repository.clear();

		// Then
		assertEquals(0, repository.size());
		assertTrue(repository.findAll().isEmpty());
	}

	@Test
	@DisplayName("删除许可证应该成功")
	void shouldDeleteLicense() {
		// Given
		PluginLicense license = createTestLicense();
		repository.save(license);
		assertTrue(repository.existsByPluginId(TEST_PLUGIN_ID));

		// When
		repository.deleteByPluginId(TEST_PLUGIN_ID);

		// Then
		assertFalse(repository.existsByPluginId(TEST_PLUGIN_ID));
		assertEquals(0, repository.size());
	}

	@Test
	@DisplayName("获取所有许可证应该成功")
	void shouldFindAllLicenses() {
		// Given
		PluginLicense license1 = createTestLicense("plugin1", LocalDateTime.now().plusDays(30));
		PluginLicense license2 = createTestLicense("plugin2", LocalDateTime.now().plusDays(60));
		repository.save(license1);
		repository.save(license2);

		// When
		List<PluginLicense> result = repository.findAll();

		// Then
		assertEquals(2, result.size());
		assertTrue(result.stream().anyMatch(l -> "plugin1".equals(l.getPluginId())));
		assertTrue(result.stream().anyMatch(l -> "plugin2".equals(l.getPluginId())));
	}

	@Test
	@DisplayName("获取所有插件ID应该成功")
	void shouldFindAllPluginIds() {
		// Given
		PluginLicense license1 = createTestLicense("plugin1", LocalDateTime.now().plusDays(30));
		PluginLicense license2 = createTestLicense("plugin2", LocalDateTime.now().plusDays(60));
		repository.save(license1);
		repository.save(license2);

		// When
		List<String> result = repository.findAllPluginIds();

		// Then
		assertEquals(2, result.size());
		assertTrue(result.contains("plugin1"));
		assertTrue(result.contains("plugin2"));
	}

	@Test
	@DisplayName("根据功能查找许可证应该成功")
	void shouldFindLicenseByFeature() {
		// Given
		PluginLicense license = createTestLicense();
		repository.save(license);

		// When & Then
		Optional<PluginLicense> result1 = repository.findByFeature("BasicFeature");
		assertTrue(result1.isPresent());
		assertEquals(TEST_PLUGIN_ID, result1.get().getPluginId());

		Optional<PluginLicense> result2 = repository.findByFeature("AdvancedFeature");
		assertTrue(result2.isPresent());

		Optional<PluginLicense> result3 = repository.findByFeature("PremiumFeature");
		assertFalse(result3.isPresent()); // PremiumFeature在测试数据中为false

		Optional<PluginLicense> result4 = repository.findByFeature("NonExistentFeature");
		assertFalse(result4.isPresent());
	}

	@Test
	@DisplayName("根据插件ID查找许可证应该成功")
	void shouldFindLicenseByPluginId() {
		// Given
		PluginLicense license = createTestLicense();
		repository.save(license);

		// When
		Optional<PluginLicense> result = repository.findByPluginId(TEST_PLUGIN_ID);

		// Then
		assertTrue(result.isPresent());
		assertEquals(TEST_PLUGIN_ID, result.get().getPluginId());
	}

	@Test
	@DisplayName("删除不存在的许可证应该不报错")
	void shouldNotFailWhenDeletingNonExistentLicense() {
		// When & Then
		assertDoesNotThrow(() -> repository.deleteByPluginId("non-existent"));
	}

	@Test
	@DisplayName("删除许可证时传入空ID应该不报错")
	void shouldNotFailWhenDeletingWithNullPluginId() {
		// When & Then
		assertDoesNotThrow(() -> repository.deleteByPluginId(null));
		assertDoesNotThrow(() -> repository.deleteByPluginId(""));
		assertDoesNotThrow(() -> repository.deleteByPluginId("   "));
	}

	@Test
	@DisplayName("查找不存在的许可证应该返回空")
	void shouldReturnEmptyForNonExistentLicense() {
		// When
		Optional<PluginLicense> result = repository.findByPluginId("non-existent");

		// Then
		assertFalse(result.isPresent());
	}

	@Test
	@DisplayName("查找许可证时传入空ID应该返回空")
	void shouldReturnEmptyForNullPluginId() {
		// When
		Optional<PluginLicense> result1 = repository.findByPluginId(null);
		Optional<PluginLicense> result2 = repository.findByPluginId("");
		Optional<PluginLicense> result3 = repository.findByPluginId("   ");

		// Then
		assertFalse(result1.isPresent());
		assertFalse(result2.isPresent());
		assertFalse(result3.isPresent());
	}

	@Test
	@DisplayName("根据功能查找许可证时传入空值应该返回空")
	void shouldReturnEmptyWhenFindingByNullFeature() {
		// Given
		PluginLicense license = createTestLicense();
		repository.save(license);

		// When & Then
		assertFalse(repository.findByFeature(null).isPresent());
		assertFalse(repository.findByFeature("").isPresent());
		assertFalse(repository.findByFeature("   ").isPresent());
	}

	@Test
	@DisplayName("保存许可证应该成功")
	void shouldSaveLicense() {
		// Given
		PluginLicense license = createTestLicense();

		// When
		repository.save(license);

		// Then
		assertEquals(1, repository.size());
		assertTrue(repository.existsByPluginId(TEST_PLUGIN_ID));
	}

	@Test
	@DisplayName("保存插件ID为空的许可证应该抛出异常")
	void shouldThrowExceptionWhenSavingLicenseWithNullPluginId() {
		// Given
		PluginLicense license = createTestLicense();
		license.setPluginId(null);

		// When & Then
		assertThrows(IllegalArgumentException.class, () -> repository.save(license));
	}

	@Test
	@DisplayName("保存空许可证应该抛出异常")
	void shouldThrowExceptionWhenSavingNullLicense() {
		// When & Then
		assertThrows(IllegalArgumentException.class, () -> repository.save(null));
	}

	@Test
	@DisplayName("更新许可证应该成功")
	void shouldUpdateLicense() {
		// Given
		PluginLicense originalLicense = createTestLicense();
		repository.save(originalLicense);

		// 创建更新后的许可证
		PluginLicense updatedLicense = createTestLicense(TEST_PLUGIN_ID, LocalDateTime.now().plusDays(60));

		// When
		repository.save(updatedLicense); // 相同插件ID会覆盖

		// Then
		assertEquals(1, repository.size()); // 仍然只有一个许可证
		Optional<PluginLicense> result = repository.findByPluginId(TEST_PLUGIN_ID);
		assertTrue(result.isPresent());
		// 验证是更新后的许可证（通过过期时间判断）
		assertTrue(result.get().getExpiryDate().isAfter(LocalDateTime.now().plusDays(59)));
	}
}