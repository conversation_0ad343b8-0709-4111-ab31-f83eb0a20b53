package com.fasnote.alm.injection.impl;

import com.fasnote.alm.injection.api.IBindingBuilder;
import com.fasnote.alm.injection.api.IServiceProvider;

/**
 * 绑定构建器实现
 */
public class BindingBuilder<T> implements IBindingBuilder<T> {
    
    private final DependencyInjector injector;
    private final Class<T> serviceClass;
    private final Class<? extends T> implementationClass;
    
    private String name;
    private boolean singleton = false;
    private boolean primary = false;
    private int priority = 0;
    private IServiceProvider<T> provider;
    
    public BindingBuilder(DependencyInjector injector, Class<T> serviceClass, Class<? extends T> implementationClass) {
        this.injector = injector;
        this.serviceClass = serviceClass;
        this.implementationClass = implementationClass;
    }
    
    @Override
    public IBindingBuilder<T> named(String name) {
        this.name = name;
        return this;
    }
    
    @Override
    public IBindingBuilder<T> asSingleton() {
        this.singleton = true;
        return this;
    }
    
    @Override
    public IBindingBuilder<T> asPrototype() {
        this.singleton = false;
        return this;
    }
    
    @Override
    public IBindingBuilder<T> asPrimary() {
        this.primary = true;
        return this;
    }
    
    @Override
    public IBindingBuilder<T> withPriority(int priority) {
        this.priority = priority;
        return this;
    }
    
    @Override
    public IBindingBuilder<T> toProvider(IServiceProvider<T> provider) {
        this.provider = provider;
        return this;
    }
    
    @Override
    public void build() {
        if (provider != null) {
            // 注册提供者
            injector.registerProvider(serviceClass, provider);
        } else if (singleton) {
            // 单例模式：延迟初始化，注册实现类而不是立即创建实例
            injector.registerSingletonImplementation(serviceClass, implementationClass, name, primary);
        } else {
            // 注册实现类
            injector.registerImplementation(serviceClass, implementationClass, name, primary);
        }
    }
}