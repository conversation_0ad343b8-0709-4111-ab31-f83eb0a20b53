                                        <!-- 保持关键类不混淆 -->
                                        <keep>
                                            {{#if exported_packages}}
                                            <!-- OSGi Export-Package 公开接口 -->
                                            {{#each exported_packages}}
                                            <class name="${package_name}.**"/>
                                            {{/each}}
                                            {{/if}}

                                            {{#if xml_config_classes}}
                                            <!-- XML配置文件引用类 -->
                                            {{#each xml_config_classes}}
                                            <class name="${class_name}"/>
                                            {{/each}}
                                            {{/if}}

                                            <!-- 许可证插件核心接口 -->
                                            <class name="com.fasnote.alm.plugin.manage.plugin.ILicensePlugin"/>
                                            <class name="com.fasnote.alm.plugin.manage.annotation.**"/>

                                            <!-- OSGi 框架相关 -->
                                            <class extends="org.osgi.framework.BundleActivator"/>
                                            <class implements="org.eclipse.core.runtime.IExecutableExtension"/>

                                            <!-- 基础 Java 类型 -->
                                            <class extends="java.lang.Enum"/>
                                            <class implements="java.io.Serializable"/>
                                        </keep>
