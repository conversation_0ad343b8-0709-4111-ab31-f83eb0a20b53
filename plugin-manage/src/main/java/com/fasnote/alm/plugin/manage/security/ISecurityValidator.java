package com.fasnote.alm.plugin.manage.security;

import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;

/**
 * 安全验证器接口
 *
 * 定义许可证安全验证的标准接口，包括： - 机器码绑定验证 - 数字签名验证 - 时间限制验证 - 完整性检查 - 功能权限验证 - 用户数量限制验证
 *
 * 此接口的实现类将被混淆加密，通过许可证文件动态加载
 */
public interface ISecurityValidator {

	/**
	 * 获取当前机器码
	 *
	 * @return 机器码字符串
	 */
	String getCurrentMachineCode();

	/**
	 * 初始化安全验证器
	 *
	 * @throws SecurityException 初始化失败时抛出
	 */
	void initialize() throws SecurityException;

	/**
	 * 检查验证器是否已初始化
	 *
	 * @return true表示已初始化
	 */
	boolean isInitialized();

	/**
	 * 验证功能权限
	 *
	 * @param license     许可证对象
	 * @param featureName 功能名称
	 * @return 验证结果
	 */
	ValidationResult validateFeaturePermission(PluginLicense license, String featureName);

	/**
	 * 验证许可证完整性
	 *
	 * @param license 许可证对象
	 * @return 验证结果
	 */
	ValidationResult validateIntegrity(PluginLicense license);

	/**
	 * 验证许可证（基本验证） 包括完整性、签名、时间限制和机器码绑定验证
	 *
	 * @param license 许可证对象
	 * @return 验证结果
	 */
	ValidationResult validateLicense(PluginLicense license);

	/**
	 * 验证许可证（包含功能权限验证）
	 *
	 * @param license     许可证对象
	 * @param featureName 需要验证的功能名称
	 * @return 验证结果
	 */
	ValidationResult validateLicenseWithFeature(PluginLicense license, String featureName);

	/**
	 * 验证许可证（包含用户数量限制验证）
	 *
	 * @param license          许可证对象
	 * @param currentUserCount 当前用户数量
	 * @return 验证结果
	 */
	ValidationResult validateLicenseWithUserLimit(PluginLicense license, int currentUserCount);

	/**
	 * 验证机器码绑定
	 *
	 * @param license 许可证对象
	 * @return 验证结果
	 */
	ValidationResult validateMachineBinding(PluginLicense license);

	/**
	 * 验证数字签名
	 *
	 * @param license 许可证对象
	 * @return 验证结果
	 */
	ValidationResult validateSignature(PluginLicense license);

	/**
	 * 验证时间限制
	 *
	 * @param license 许可证对象
	 * @return 验证结果
	 */
	ValidationResult validateTimeLimit(PluginLicense license);

	/**
	 * 验证用户数量限制
	 *
	 * @param license          许可证对象
	 * @param currentUserCount 当前用户数量
	 * @return 验证结果
	 */
	ValidationResult validateUserLimit(PluginLicense license, int currentUserCount);
}
