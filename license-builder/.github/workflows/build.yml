name: Build ALM Plugin

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      plugin-path:
        description: '插件源代码路径'
        required: true
        default: './my-plugin'
      skip-obfuscation:
        description: '跳过代码混淆'
        required: false
        type: boolean
        default: false

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      
      - name: 设置Java环境
        uses: actions/setup-java@v3
        with:
          java-version: '11'
          distribution: 'temurin'
      
      - name: 缓存Maven依赖
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-m2-
      
      - name: 构建ALM插件
        id: build-plugin
        uses: ./
        with:
          plugin-path: ${{ github.event.inputs.plugin-path || './my-plugin' }}
          skip-obfuscation: ${{ github.event.inputs.skip-obfuscation || false }}
          output-path: './output'
      
      - name: 显示构建结果
        run: |
          echo "构建完成!"
          echo "插件JAR: ${{ steps.build-plugin.outputs.plugin-jar }}"
          echo "许可证文件: ${{ steps.build-plugin.outputs.license-file }}"
          echo "构建日志: ${{ steps.build-plugin.outputs.build-log }}"
          
          # 显示文件大小
          if [[ -f "${{ steps.build-plugin.outputs.plugin-jar }}" ]]; then
            echo "插件JAR大小: $(du -h "${{ steps.build-plugin.outputs.plugin-jar }}" | cut -f1)"
          fi
          
          if [[ -f "${{ steps.build-plugin.outputs.license-file }}" ]]; then
            echo "许可证文件大小: $(du -h "${{ steps.build-plugin.outputs.license-file }}" | cut -f1)"
          fi
      
      - name: 上传构建产物
        uses: actions/upload-artifact@v3
        with:
          name: alm-plugin-build-${{ github.run_number }}
          path: |
            ${{ steps.build-plugin.outputs.plugin-jar }}
            ${{ steps.build-plugin.outputs.license-file }}
            ${{ steps.build-plugin.outputs.build-log }}
          retention-days: 30
      
      - name: 创建发布 (仅限标签)
        if: startsWith(github.ref, 'refs/tags/')
        uses: softprops/action-gh-release@v1
        with:
          files: |
            ${{ steps.build-plugin.outputs.plugin-jar }}
            ${{ steps.build-plugin.outputs.license-file }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # 多平台构建
  build-matrix:
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        java: [11, 17]
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v3
      
      - name: 设置Java环境
        uses: actions/setup-java@v3
        with:
          java-version: ${{ matrix.java }}
          distribution: 'temurin'
      
      - name: 构建ALM插件
        uses: ./
        with:
          plugin-path: './example-plugin'
          skip-obfuscation: true
          output-path: './output-${{ matrix.os }}-java${{ matrix.java }}'
      
      - name: 上传构建产物
        uses: actions/upload-artifact@v3
        with:
          name: alm-plugin-${{ matrix.os }}-java${{ matrix.java }}
          path: ./output-${{ matrix.os }}-java${{ matrix.java }}/*
          retention-days: 7